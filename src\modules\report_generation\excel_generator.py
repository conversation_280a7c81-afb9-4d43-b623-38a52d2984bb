"""
Excel报表生成器 (Excel Generator)

专门处理Excel模板的数据填充、格式保持和报表生成。
支持复杂的Excel结构，包括多工作表、公式、图表等。

主要功能：
- Excel模板解析和数据绑定
- 保持原始格式、样式和公式
- 支持批量数据填充和表格扩展
- 工作表管理和数据验证
- 生成新的Excel文件

依赖库：
- openpyxl: Excel文件处理
- xlsxwriter: Excel文件写入
- pandas: 数据处理

Author: Development Team
Date: 2025-06-13
"""

import os
import shutil
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import logging
from datetime import datetime

try:
    import openpyxl
    from openpyxl import load_workbook, Workbook
    from openpyxl.styles import PatternFill, Border, Side, Alignment, Font
    from openpyxl.cell.cell import Cell, MergedCell
    import pandas as pd
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    
from src.utils.log_config import get_module_logger
from .template_engine import TemplateEngine
from src.utils.logging_utils import log_sample


class ExcelGenerator:
    """Excel报表生成器"""
    
    def __init__(self, template_engine: Optional[TemplateEngine] = None):
        """
        初始化Excel生成器
        
        Args:
            template_engine: 模板引擎实例，如果为None则创建新实例
        """
        self.logger = get_module_logger(__name__)
        
        # 检查依赖库
        if not EXCEL_AVAILABLE:
            raise ImportError("缺少Excel处理依赖库，请安装: pip install openpyxl pandas xlsxwriter")
            
        # 初始化模板引擎
        if template_engine is None:
            self.template_engine = TemplateEngine()
        else:
            self.template_engine = template_engine
            
        self.logger.info("Excel生成器初始化完成")
        
    def generate_report(self, 
                       template_key: str, 
                       data: Dict[str, Any], 
                       output_path: str, 
                       save_options: Optional[Dict[str, Any]] = None) -> bool:
        """
        生成Excel报表
        
        Args:
            template_key: 模板键名
            data: 填充数据字典
            output_path: 输出文件路径
            save_options: 保存选项
            
        Returns:
            bool: 是否生成成功
        """
        try:
            # 任务级上下文（不记录敏感数据，仅记录模板键与输出文件名）
            try:
                from src.utils.logging_utils import bind_context
                import os as _os
                _ctx_logger = bind_context(self.logger, component="ExcelGenerator",
                                           template_key=template_key,
                                           output_basename=_os.path.basename(output_path))
            except Exception:
                _ctx_logger = self.logger
            # 验证模板类型
            template_info = self.template_engine.get_template_info(template_key)
            if template_info['type'] != 'excel':
                raise ValueError(f"模板 {template_key} 不是Excel类型")
                
            # 获取模板路径
            template_path = self.template_engine.get_template_path(template_key)
            
            # 验证数据完整性
            validation_result = self.template_engine.validate_template_data(template_key, data)
            if not validation_result['valid']:
                self.logger.warning(f"数据验证未通过，缺失字段: {validation_result['missing_fields']}")
                
            _ctx_logger.info(f"开始生成Excel报表")
            
            # 预处理数据
            processed_data = self._preprocess_data(data, template_key)
            
            # 生成Excel文件
            success = self._generate_excel_file(template_path, processed_data, output_path, save_options)
            
            if success:
                _ctx_logger.info("Excel报表生成成功")
                # 验证生成的文件
                if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                    return True
                else:
                    self.logger.error("生成的文件不存在或为空")
                    return False
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"生成Excel报表失败: {str(e)}", exc_info=True)
            return False
            
    def _generate_excel_file(self, 
                            template_path: Path, 
                            data: Dict[str, Any], 
                            output_path: str, 
                            save_options: Optional[Dict[str, Any]] = None) -> bool:
        """
        生成Excel文件
        
        Args:
            template_path: 模板文件路径
            data: 数据字典
            output_path: 输出路径
            save_options: 保存选项
            
        Returns:
            bool: 是否生成成功
        """
        try:
            # 复制模板文件到输出路径
            shutil.copy2(template_path, output_path)
            
            # 打开工作簿
            workbook = load_workbook(output_path)
            
            # 处理每个工作表
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                self._fill_worksheet_data(worksheet, data)
                
            # 保存工作簿
            workbook.save(output_path)
            workbook.close()
            
            # 降噪：调试日志按采样输出
            try:
                if log_sample("excel-generate-success", 5):
                    self.logger.debug(f"Excel文件生成成功: {output_path}")
            except Exception:
                pass
            return True
            
        except Exception as e:
            self.logger.error(f"Excel文件生成失败: {str(e)}")
            return False
            
    def _fill_worksheet_data(self, worksheet, data: Dict[str, Any]) -> None:
        """
        填充工作表数据
        
        Args:
            worksheet: 工作表对象
            data: 数据字典
        """
        try:
            # 遍历所有单元格，查找并替换占位符
            for row in worksheet.iter_rows():
                for cell in row:
                    if cell.value and isinstance(cell.value, str):
                        # 检查是否包含占位符
                        placeholders = self.template_engine.extract_placeholders(cell.value)
                        if placeholders:
                            # 替换占位符
                            new_value = self.template_engine.replace_placeholders(cell.value, data)
                            cell.value = new_value
                            
            # 特殊处理：如果有数据表格需要填充
            if 'employee_data' in data and isinstance(data['employee_data'], list):
                self._fill_employee_table(worksheet, data['employee_data'])
                
        except Exception as e:
            self.logger.error(f"填充工作表数据失败: {str(e)}")
            
    def _fill_employee_table(self, worksheet, employee_data: List[Dict[str, Any]]) -> None:
        """
        填充员工数据表格
        
        Args:
            worksheet: 工作表对象
            employee_data: 员工数据列表
        """
        try:
            # 查找数据开始位置
            data_start_row, data_start_col = self._find_data_start_position(worksheet)
            
            # 如果找到了数据起始位置，填充员工数据
            if data_start_row is not None and data_start_col is not None:
                self._populate_employee_data(worksheet, employee_data, data_start_row, data_start_col)
                
        except Exception as e:
            self.logger.error(f"填充员工表格失败: {str(e)}")
    
    def _find_data_start_position(self, worksheet) -> tuple[Optional[int], Optional[int]]:
        """
        查找数据表格的开始位置
        
        Args:
            worksheet: 工作表对象
            
        Returns:
            tuple[Optional[int], Optional[int]]: (起始行号, 起始列号)
        """
        header_keywords = ['姓名', '工号', '员工姓名']
        
        for row_idx, row in enumerate(worksheet.iter_rows(), 1):
            for col_idx, cell in enumerate(row, 1):
                if self._is_header_cell(cell, header_keywords):
                    return row_idx + 1, col_idx  # 数据从下一行开始
                    
        return None, None
    
    def _is_header_cell(self, cell, keywords: List[str]) -> bool:
        """
        检查单元格是否为表头单元格
        
        Args:
            cell: 单元格对象
            keywords: 关键词列表
            
        Returns:
            bool: 是否为表头单元格
        """
        if not cell.value or not isinstance(cell.value, str):
            return False
        return any(keyword in cell.value for keyword in keywords)
    
    def _populate_employee_data(self, worksheet, employee_data: List[Dict[str, Any]], 
                               start_row: int, start_col: int) -> None:
        """
        填充员工数据到工作表
        
        Args:
            worksheet: 工作表对象
            employee_data: 员工数据列表
            start_row: 起始行号
            start_col: 起始列号
        """
        column_mapping = self._get_employee_column_mapping()
        
        for i, employee in enumerate(employee_data):
            row_num = start_row + i
            self._fill_employee_row(worksheet, employee, row_num, start_col, column_mapping)
    
    def _get_employee_column_mapping(self) -> Dict[str, int]:
        """
        获取员工数据字段到列偏移的映射
        
        Returns:
            Dict[str, int]: 字段到列偏移的映射
        """
        return {
            'name': 0,
            'employee_id': 1,
            'department': 2,
            'change_amount': 3,
            'change_reason': 4
        }
    
    def _fill_employee_row(self, worksheet, employee: Dict[str, Any], 
                          row_num: int, start_col: int, column_mapping: Dict[str, int]) -> None:
        """
        填充单个员工的数据行
        
        Args:
            worksheet: 工作表对象
            employee: 员工数据
            row_num: 行号
            start_col: 起始列号
            column_mapping: 列映射
        """
        for field, col_offset in column_mapping.items():
            if field in employee:
                col_num = start_col + col_offset
                worksheet.cell(row=row_num, column=col_num, value=employee[field])
            
    def _preprocess_data(self, data: Dict[str, Any], template_key: str) -> Dict[str, Any]:
        """
        预处理数据，进行格式化和转换
        
        Args:
            data: 原始数据
            template_key: 模板键名
            
        Returns:
            Dict: 处理后的数据
        """
        processed_data = data.copy()
        
        # 格式化金额
        if 'total_amount' in processed_data:
            processed_data['total_amount_formatted'] = self.template_engine.format_currency(
                processed_data['total_amount']
            )
            
        # 格式化日期
        if 'year' in processed_data and 'month' in processed_data:
            processed_data['year_month'] = f"{processed_data['year']}年{processed_data['month']}月"
            
        # 处理员工数据统计
        if 'employee_data' in processed_data:
            processed_data['employee_count'] = len(processed_data['employee_data'])
            # 计算总的异动金额
            total_change = sum(emp.get('change_amount', 0) for emp in processed_data['employee_data'])
            processed_data['total_change_amount'] = total_change
            processed_data['total_change_amount_formatted'] = self.template_engine.format_currency(total_change)
            
        # 添加当前日期
        processed_data['generate_date'] = datetime.now().strftime('%Y年%m月%d日')
        
        self.logger.debug(f"Excel数据预处理完成，字段数: {len(processed_data)}")
        return processed_data
        
    def get_template_structure(self, template_key: str) -> Dict[str, Any]:
        """
        获取模板结构信息
        
        Args:
            template_key: 模板键名
            
        Returns:
            Dict: 模板结构信息
        """
        try:
            template_path, template_info = self._get_template_info(template_key)
            workbook = load_workbook(template_path, read_only=True)
            
            structure = self._build_template_structure(workbook)
            workbook.close()
            
            self.logger.debug(f"模板 {template_key} 结构分析完成")
            return structure
            
        except Exception as e:
            self.logger.error(f"分析模板结构失败: {str(e)}")
            return {'sheets': [], 'placeholders': [], 'data_areas': []}
    
    def _get_template_info(self, template_key: str) -> tuple[Path, Dict[str, Any]]:
        """
        获取模板路径和信息
        
        Args:
            template_key: 模板键名
            
        Returns:
            tuple[Path, Dict[str, Any]]: 模板路径和信息
            
        Raises:
            ValueError: 如果模板不是Excel类型
        """
        template_path = self.template_engine.get_template_path(template_key)
        template_info = self.template_engine.get_template_info(template_key)
        
        if template_info['type'] != 'excel':
            raise ValueError(f"模板 {template_key} 不是Excel类型")
            
        return template_path, template_info
    
    def _build_template_structure(self, workbook) -> Dict[str, Any]:
        """
        构建模板结构信息
        
        Args:
            workbook: Excel工作簿对象
            
        Returns:
            Dict[str, Any]: 模板结构信息
        """
        structure_placeholders: List[str] = []
        structure: Dict[str, Any] = {
            'sheets': [],
            'placeholders': structure_placeholders,
            'data_areas': []
        }
        
        # 分析每个工作表
        for sheet_name in workbook.sheetnames:
            sheet_info = self._analyze_worksheet(workbook[sheet_name], sheet_name)
            structure['sheets'].append(sheet_info)
            
            # 收集占位符
            if 'placeholders' in sheet_info:
                structure_placeholders.extend(sheet_info['placeholders'])
                
        # 去重占位符
        structure['placeholders'] = list(set(structure_placeholders))
        return structure
    
    def _analyze_worksheet(self, worksheet, sheet_name: str) -> Dict[str, Any]:
        """
        分析单个工作表
        
        Args:
            worksheet: 工作表对象
            sheet_name: 工作表名称
            
        Returns:
            Dict[str, Any]: 工作表信息
        """
        sheet_placeholders: List[str] = []
        sheet_info = {
            'name': sheet_name,
            'max_row': worksheet.max_row,
            'max_column': worksheet.max_column,
            'placeholders': sheet_placeholders
        }
        
        # 提取占位符
        for row in worksheet.iter_rows():
            for cell in row:
                cell_placeholders = self._extract_cell_placeholders(cell)
                if cell_placeholders:
                    sheet_placeholders.extend(cell_placeholders)
                    
        return sheet_info
    
    def _extract_cell_placeholders(self, cell) -> List[str]:
        """
        提取单元格中的占位符
        
        Args:
            cell: 单元格对象
            
        Returns:
            List[str]: 占位符列表
        """
        if not cell.value or not isinstance(cell.value, str):
            return []
        
        placeholders = self.template_engine.extract_placeholders(cell.value)
        return self._convert_to_list(placeholders)
    
    def _convert_to_list(self, placeholders) -> List[str]:
        """
        将占位符转换为列表格式
        
        Args:
            placeholders: 占位符（可能是各种可迭代类型）
            
        Returns:
            List[str]: 标准化的占位符列表
        """
        if not placeholders:
            return []
        
        if isinstance(placeholders, list):
            return placeholders
        elif isinstance(placeholders, (set, tuple)):
            return list(placeholders)
        else:
            # 其他可迭代类型，尝试转换
            try:
                return list(placeholders)
            except (TypeError, ValueError):
                return []
            
    def create_summary_report(self, 
                             change_data: List[Dict[str, Any]], 
                             output_path: str,
                             summary_options: Optional[Dict[str, Any]] = None) -> bool:
        """
        创建异动汇总报表
        
        Args:
            change_data: 异动数据列表
            output_path: 输出文件路径
            summary_options: 汇总选项
            
        Returns:
            bool: 是否生成成功
        """
        try:
            # 创建新的工作簿
            workbook = Workbook()
            worksheet = workbook.active
            if worksheet is not None:
                worksheet.title = "异动汇总表"
                
                # 设置表头
                headers = ['姓名', '工号', '部门', '异动类型', '异动金额', '异动原因', '处理日期']
                for col_num, header in enumerate(headers, 1):
                    cell = worksheet.cell(row=1, column=col_num, value=header)
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    
                # 填充数据
                for row_num, data in enumerate(change_data, 2):
                    worksheet.cell(row=row_num, column=1, value=data.get('name', ''))
                    worksheet.cell(row=row_num, column=2, value=data.get('employee_id', ''))
                    worksheet.cell(row=row_num, column=3, value=data.get('department', ''))
                    worksheet.cell(row=row_num, column=4, value=data.get('change_type', ''))
                    worksheet.cell(row=row_num, column=5, value=data.get('change_amount', 0))
                    worksheet.cell(row=row_num, column=6, value=data.get('change_reason', ''))
                    worksheet.cell(row=row_num, column=7, value=data.get('process_date', ''))
                    
                # 调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    
                    # 获取第一个单元格并检查类型
                    first_cell_obj = column[0]
                    # 安全地处理Cell类型
                    if isinstance(first_cell_obj, Cell):
                        column_letter = first_cell_obj.column_letter
                    elif hasattr(first_cell_obj, 'column_letter'):
                        # 如果是MergedCell或其他具有column_letter属性的类型
                        column_letter = getattr(first_cell_obj, 'column_letter', 'A')
                    else:
                        column_letter = 'A'  # 默认值，或跳过这一列
                    
                    for cell_obj in column:
                        try:
                            # 安全地获取单元格值
                            if isinstance(cell_obj, Cell):
                                cell_value = cell_obj.value
                            else:
                                # 对于MergedCell等其他类型，可能没有value属性
                                cell_value = getattr(cell_obj, 'value', '')
                            
                            if len(str(cell_value)) > max_length:
                                max_length = len(str(cell_value))
                        except:
                            pass
                    adjusted_width = (max_length + 2) * 1.2
                    worksheet.column_dimensions[column_letter].width = adjusted_width
                
            # 保存文件
            workbook.save(output_path)
            workbook.close()
            
            self.logger.info(f"异动汇总报表生成成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"生成异动汇总报表失败: {str(e)}")
            return False
            
    def preview_report(self, template_key: str, data: Dict[str, Any]) -> Optional[str]:
        """
        预览报表内容（生成临时文件）
        
        Args:
            template_key: 模板键名
            data: 数据字典
            
        Returns:
            Optional[str]: 临时文件路径，如果失败返回None
        """
        try:
            # 创建临时文件路径
            import tempfile
            import uuid
            
            temp_dir = tempfile.gettempdir()
            temp_filename = f"preview_{template_key}_{uuid.uuid4().hex[:8]}.xlsx"
            temp_path = os.path.join(temp_dir, temp_filename)
            
            # 生成预览报表
            if self.generate_report(template_key, data, temp_path):
                self.logger.info(f"预览报表生成成功: {temp_path}")
                return temp_path
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"生成预览报表失败: {str(e)}")
            return None 