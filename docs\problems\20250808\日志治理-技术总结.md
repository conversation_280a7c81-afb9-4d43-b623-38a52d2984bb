# 日志治理 - 技术总结

## 目标与范围
- 在不改变业务语义的前提下，实现全链路日志规范化：上下文绑定、敏感脱敏、降噪（节流/采样/聚合）、SQL 日志一致性与慢 SQL 观测、策略热重建与测试保障。

## 核心能力
- 上下文绑定：`bind_context(logger, **kv)`，让日志天然携带组件/表名/任务等维度，便于溯源。
- 降噪策略：`log_throttle(key, window_s)`、`log_sample(key, n)`、`aggregate_register/aggregate_should_emit`（窗口聚合）。
- 敏感脱敏：`redact(value)` 对工号/姓名/路径等敏感信息脱敏。
- RingBuffer：近历史 DEBUG 缓存与导出（开发/现场排障），不污染主日志。
- SQL 一致性：`LOG_SQL_DETAIL` 控制明细；慢 SQL 恒定 INFO+脱敏；统一节流键位。
- 策略治理：`state/logging_policy.json` + `src/utils/log_config.py`，支持多 sink、环境区分、pytest 控制台抑制、可选热重建。

## 实施范围与主要编辑点
- 导入链路：`src/modules/data_import/*`、`src/gui/data_import_integration.py`
  - 脱敏（记录详情/样本值）、组件上下文绑定、进度采样。
- 检测链路：`src/gui/change_detection_*`
  - 组件上下文、进度采样、完成摘要脱敏+节流。
- 报表链路：`src/modules/report_generation/*`、`src/gui/report_generation_integration.py`
  - 任务上下文（`task_id/template_key/output_basename`）、成功/进度采样、避免全路径泄露。
- 导航/分页/表格：`enhanced_navigation_panel.py`、`pagination_widget.py`、`virtualized_expandable_table.py`、`column_sort_manager.py`
  - 组件上下文、统一键位的节流/采样（`nav-*`, `pagination-*`, `table-*`, `multi-sort`）。
- 状态同步/排序状态：`path_state_synchronizer.py`、`table_sort_state_manager.py`
  - 同步细节采样、同步摘要节流、状态保存/恢复/清理节流、映射适配采样。
- 数据/状态/服务：`unified_data_request_manager.py`(UDRM)、`unified_state_manager.py`(USM)、`services/table_data_service.py`(TDS)
  - UDRM：开始/完成节流（上下文含 `table/request_type/request_id`），排序标准化采样。
  - USM：表/全局状态更新摘要节流，表名脱敏。
  - TDS：排序/分页/加载/缓存/发布/格式化路径分键节流。
- 事件总线：`core/event_bus.py`
  - 订阅/发布路径采样，避免高频 DEBUG 风暴。
- SQL/存储：`modules/data_storage/database_manager.py`、`dynamic_table_manager.py`
  - 明细受 `LOG_SQL_DETAIL` 控制且节流；慢 SQL 恒定 INFO+脱敏；必要场景样本仅在明细开启时输出。
- 配置与策略：`src/utils/log_config.py`、`state/logging_policy.json`
  - 多 sink、环境适配、pytest 控制台抑制、热重建 API；默认稳健策略。

## 端到端流程图
```mermaid
graph TD
  A[业务模块: 导入/检测/报表/导航/分页/表格/排序/UDRM/USM/事件总线]
  B[bind_context 上下文绑定]
  C[redact 脱敏]
  D[log_throttle / log_sample / aggregate 降噪]
  E[log_config 策略(级别/输出/SQL/慢SQL)]
  F[Sinks 控制台/文件/性能/SQL/RingBuffer]

  A --> B --> C --> D --> E --> F
```

## 调用时序（示例：分页请求→查询→发布）
```mermaid
sequenceDiagram
  participant UI as UI/组件
  participant TDS as TableDataService
  participant UDRM as UnifiedDataRequestManager
  participant DB as DB层(DTM/DBM)
  participant EB as EventBus
  participant LC as log_config/sinks

  UI->>TDS: PageRequestEvent
  TDS->>TDS: bind_context + tds-page(throttle)
  TDS->>UDRM: DataRequest
  UDRM->>UDRM: bind_context(table,request_type,request_id)
  UDRM->>DB: 执行分页+排序查询
  DB-->>UDRM: DataFrame, total
  UDRM-->>TDS: DataResponse (udrm-done)
  TDS->>EB: 发布 DataUpdatedEvent (tds-publish)
  EB-->>UI: 事件分发 (bus-publish sample)
  Note over LC: 全程按策略/键位写入日志 sinks
```

## 常见键位（部分）
- 导航：`nav-restore-state`、`nav-auto-select`、`nav-delayed-auto-select`、`nav-force-refresh`、`nav-select`
- 分页：`pagination-ui-state`、`pagination-total`、`pagination-page-change`
- 表格：`table-set-data`、`table-render`、`table-sample-row`
- 排序：`multi-sort`、`multi-sort-mapping`、`udrm-sort`
- 同步：`sync-sort`、`sync-mapping`、`sync-pagination`、`sync-datasource`、`sync-summary`
- 状态：`usm-update`、`usm-global-update`、`sort-state-save/restore/clear`、`sort-state-cleanup`
- 服务/请求：`tds-load`、`tds-cache`、`tds-publish`、`tds-format`、`udrm-start/done`
- 事件总线：`bus-subscribe`、`bus-publish`
- SQL：`sql-query-detail`、`sql-query-brief`、`sql-ids-sample`、`sql-sort-sample`

## 测试与回归
- 新增/增强用例（均通过）：
  - 事件总线冒烟：订阅→发布→取消订阅
  - UDRM 节流冒烟：打桩查询与表存在校验，验证请求路径稳定
  - USM 节流冒烟：表状态更新不抛错
  - SQL 开关与慢 SQL：明细/阈值策略验证
  - Pytest 环境控制台 sink 抑制：无冲突

## 影响与收益
- 可观测性：日志溯源更快；跨模块问题定位稳定。
- 稳态性能：显著降低高频日志噪声与磁盘 I/O。
- 合规安全：敏感信息默认脱敏输出。
- 一致性：SQL 输出与策略一致，慢 SQL 恒定可见，明细受控。


