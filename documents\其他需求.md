


# TODO
---
1、自建插件或程序，梳理项目开发的各个阶段





---







---


请你帮我写一个脚本，功能要求：
- 在powershell中运行：ssh cc ，登录ubuntu2404服务器，进入项目目录：~/salary_changes ，执行如下命令：
1、清理python缓存。
2、删除日志目录 logs 。
3、删除状态目录 state 。
4、删除数据库目录 data/db 。
5、运行类似打包压缩命令： zip -r ./zip/salary_changes_2025070801.zip /home/<USER>/salary_changes/
6、复制压缩包到本地： scp ubuntu@*************:/home/<USER>/zip/salary_changes_2025070801.zip .
7、用 7-zip 解压压缩包。
8、如果当前目录下有项目目录 salary_changes ，删除。
9、然后，到之前解压的目录中，移动子目录 salary_changes 到当前目录。
10、以管理员身份运行另一个powershell，进入项目目录：salary_changes

请你先思考一下，给出你的处理思路，待用户确认后，再开始编写具体脚本。





---






---
## 从提示词工程到"Context Engineering上下文工程"

### context-engineering-intro
https://github.com/coleam00/Context-Engineering-Intro

https://www.aivi.fyi/aiagents/introduce-Context-Engineering-for-Claude-Code
可以采用 autogen 编写

我需要用AutoGen实现一个能够根据用户输入的开发要求生成代码，并且检查代码质量，再优化和修复代码的agent，
请根据我的需求，先索引当前项目，然后根据AutoGen官方文档[https://microsoft.github.io/autogen/stable/user-guide/agentchat-user-guide/index.htmL] 以及 CLAUDE.md和 INITIAL.md和 INITIAL_EXAMPLE.md 示例，生成INITIAL.md和CLAUDE.md


---



subagent 生成：

描述：
专业的产品需求文档（PRD）生成专家和产品经理助手。
当用户需要生成PRD文档、产品需求文档、产品规格书、功能需求分析、产品设计文档、需求整合、产品规划或编写用户故事时必须优先使用。
擅长结构化需求分析、用户故事编写、功能规格定义和产品文档标准化。





