#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 P0级问题修复效果测试脚本

测试修复的问题：
1. 线程安全QTimer错误
2. RequestDeduplicationManager缺失方法
3. 数据类型判断错误

本脚本将模拟问题场景，验证修复是否有效。
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_thread_safe_timer():
    """测试线程安全QTimer修复"""
    try:
        print("🔧 [测试1] 线程安全QTimer修复...")
        
        from src.utils.thread_safe_timer import ThreadSafeTimer
        
        # 测试safe_single_shot方法
        def test_callback():
            print("✅ Timer回调执行成功")
        
        result = ThreadSafeTimer.safe_single_shot(100, test_callback)
        
        if result:
            print("✅ [测试1] ThreadSafeTimer.safe_single_shot调用成功，无参数错误")
        else:
            print("❌ [测试1] ThreadSafeTimer.safe_single_shot调用失败")
            
        return result
        
    except Exception as e:
        print(f"❌ [测试1] 线程安全QTimer测试异常: {e}")
        return False

def test_request_deduplication_manager():
    """测试RequestDeduplicationManager缺失方法修复"""
    try:
        print("🔧 [测试2] RequestDeduplicationManager缺失方法修复...")
        
        from src.core.request_deduplication_manager import RequestDeduplicationManager
        
        # 创建实例
        manager = RequestDeduplicationManager()
        
        # 测试新添加的方法是否存在
        required_methods = [
            'mark_pagination_request_started',
            'mark_pagination_request_completed', 
            'get_pagination_request_status',
            'cleanup_expired_pagination_requests'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(manager, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ [测试2] 缺失方法: {missing_methods}")
            return False
        
        # 测试方法调用
        manager.mark_pagination_request_started("test_table", 1, "test_request_id")
        status = manager.get_pagination_request_status("test_table", 1)
        manager.mark_pagination_request_completed("test_table", 1, True)
        manager.cleanup_expired_pagination_requests()
        
        print("✅ [测试2] 所有RequestDeduplicationManager方法调用成功")
        return True
        
    except Exception as e:
        print(f"❌ [测试2] RequestDeduplicationManager测试异常: {e}")
        return False

def test_data_type_checking():
    """测试数据类型判断错误修复"""
    try:
        print("🔧 [测试3] 数据类型判断错误修复...")
        
        # 创建一个模拟的MainWorkspaceArea来测试set_data方法
        class MockMainWorkspaceArea:
            def __init__(self):
                self.has_real_data = False
                self.use_pagination = False
                
                # 模拟日志记录器
                class MockLogger:
                    def debug(self, msg): pass
                    def info(self, msg): pass
                    def warning(self, msg): pass
                    def error(self, msg, exc_info=None): pass
                
                self.logger = MockLogger()
                
            def _show_empty_table_with_prompt(self, msg=None):
                print(f"显示空表格提示: {msg}")
                
            def _get_main_window(self):
                return None
        
        # 测试不同数据类型
        workspace = MockMainWorkspaceArea()
        
        # 修改set_data方法的关键部分来测试数据类型检查
        def test_data_type_check(df):
            """测试数据类型检查逻辑"""
            is_valid_data = False
            if df is not None:
                # 🔧 [P0-CRITICAL修复] 安全的数据类型检查
                if hasattr(df, 'empty'):  # DataFrame类型
                    is_valid_data = not df.empty
                elif isinstance(df, list):  # List类型
                    is_valid_data = len(df) > 0
                elif isinstance(df, dict):  # Dict类型
                    is_valid_data = len(df) > 0
                else:
                    is_valid_data = True  # 其他类型默认有效
            return is_valid_data
        
        # 测试各种数据类型
        test_cases = [
            (None, False, "None"),
            ([], False, "空list"),
            ([{"a": 1}], True, "非空list"),
            ({}, False, "空dict"),
            ({"a": 1}, True, "非空dict"),
        ]
        
        for data, expected, description in test_cases:
            try:
                result = test_data_type_check(data)
                if result == expected:
                    print(f"✅ [测试3] {description}: {result} (预期: {expected})")
                else:
                    print(f"❌ [测试3] {description}: {result} (预期: {expected})")
                    return False
            except AttributeError as e:
                if "'list' object has no attribute 'empty'" in str(e):
                    print(f"❌ [测试3] {description}: 仍然存在empty属性错误")
                    return False
                else:
                    raise
        
        print("✅ [测试3] 数据类型判断修复验证成功")
        return True
        
    except Exception as e:
        print(f"❌ [测试3] 数据类型判断测试异常: {e}")
        return False

def main():
    """运行所有P0级修复测试"""
    print("🚀 开始P0级问题修复验证测试...")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("线程安全QTimer", test_thread_safe_timer()))
    test_results.append(("RequestDeduplicationManager", test_request_deduplication_manager()))
    test_results.append(("数据类型判断", test_data_type_checking()))
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📊 P0级修复测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有P0级问题修复验证通过！")
        return True
    else:
        print("⚠️  部分P0级问题修复需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)