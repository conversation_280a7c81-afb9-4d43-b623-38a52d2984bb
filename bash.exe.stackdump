Stack trace:
Frame         Function      Args
0007FFFFB310  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB310, 0007FFFFA210) msys-2.0.dll+0x1FEBA
0007FFFFB310  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFB5E8) msys-2.0.dll+0x67F9
0007FFFFB310  000210046832 (000210285FF9, 0007FFFFB1C8, 0007FFFFB310, 000000000000) msys-2.0.dll+0x6832
0007FFFFB310  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB310  0002100690B4 (0007FFFFB320, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFB5F0  00021006A49D (0007FFFFB320, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA2BE00000 ntdll.dll
7FFA2B6C0000 KERNEL32.DLL
7FFA28260000 KERNELBASE.dll
7FFA2AC40000 USER32.dll
7FFA28860000 win32u.dll
7FFA2A620000 GDI32.dll
7FFA28500000 gdi32full.dll
7FFA27E70000 msvcp_win.dll
7FFA28760000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA2B570000 advapi32.dll
7FFA2A580000 msvcrt.dll
7FFA2B4C0000 sechost.dll
7FFA2AB20000 RPCRT4.dll
7FFA28010000 bcrypt.dll
7FFA27760000 CRYPTBASE.DLL
7FFA27F10000 bcryptPrimitives.dll
7FFA2B620000 IMM32.DLL
