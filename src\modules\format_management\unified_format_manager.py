#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一格式管理器 - 核心组件（新架构版本）

负责整个系统的格式化管理，确保所有数据源路径的表头和数据格式保持一致。
这是统一格式管理系统的中央控制器。

🎯 [新架构特性]:
- 事件驱动架构：使用EventBus发布和订阅格式化事件
- 统一状态管理：与UnifiedStateManager集成
- 依赖注入：通过构造函数注入依赖
- 响应式更新：配置变更时自动更新相关组件

主要功能:
- 集中管理所有表格的格式配置
- 提供统一的数据格式化接口
- 协调各个子组件的工作
- 确保格式变更的全局一致性

设计原则:
- 单一数据源：所有格式配置集中管理
- 统一接口：对外提供简洁统一的API
- 配置驱动：通过配置文件控制格式规则
- 可扩展性：支持新表格类型的快速接入

创建时间: 2025-07-18
作者: 统一格式管理系统重构团队
"""

import json
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Tuple
from pathlib import Path
from datetime import datetime
import logging
import time
from src.core.event_bus import Event

# 导入项目内部模块
try:
    from src.utils.log_config import setup_logger
except ImportError:
    # 如果无法导入，使用标准日志
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

from .format_config import FormatConfig
from .field_registry import FieldRegistry  
from .format_renderer import FormatRenderer

# 🎯 [新架构集成] 事件驱动架构
from src.core.event_bus import EventBus

# 🎯 [新架构集成] 统一状态管理
from src.core.unified_state_manager import UnifiedStateManager


class UnifiedFormatManager:
    """
    统一格式管理器 - 符合新架构理念的版本
    
    🎯 [新架构特性]:
    - 事件驱动架构：使用EventBus发布和订阅格式化事件
    - 统一状态管理：与UnifiedStateManager集成
    - 依赖注入：通过构造函数注入依赖
    - 响应式更新：配置变更时自动更新相关组件
    
    这是整个格式管理系统的核心组件，负责协调和管理所有的格式化操作。
    确保不管数据来自哪个路径，最终的表头和数据格式都是统一的。
    """
    
    def __init__(self, 
                 config_path: Optional[str] = None,
                 field_mapping_path: Optional[str] = None,
                 event_bus: Optional[EventBus] = None,
                 state_manager: Optional[UnifiedStateManager] = None):
        """
        初始化统一格式管理器 - 支持依赖注入
        
        Args:
            config_path: 格式配置文件路径
            field_mapping_path: 字段映射配置文件路径
            event_bus: 事件总线实例（依赖注入）
            state_manager: 状态管理器实例（依赖注入）
        """
        self.logger = setup_logger("format_management.unified_format_manager")
        
        # 🎯 [依赖注入] 事件总线和状态管理器
        self.event_bus = event_bus or EventBus()
        self.state_manager = state_manager or UnifiedStateManager()
        
        # 配置文件路径
        self.config_path = config_path or "state/data/format_config.json"
        self.field_mapping_path = field_mapping_path or "state/data/field_mappings.json"
        
        # 初始化子组件
        self.format_config = FormatConfig(self.config_path)
        self.field_registry = FieldRegistry(self.field_mapping_path)
        self.format_renderer = FormatRenderer(self.format_config, self.field_registry)
        
        # 缓存系统
        self._format_cache = {}
        self._cache_timestamps = {}
        
        # 统计信息
        self._stats = {
            'total_formats': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'errors': 0
        }
        
        # 🎯 [事件驱动] 设置事件监听器
        self._setup_event_listeners()
        
        # 🎯 [统一状态管理] 将格式管理器状态同步到状态管理器
        self._sync_to_state_manager()
        
        # 初始化系统
        self._initialize_system()
        
        self.logger.info("🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）")
    
    def _initialize_system(self):
        """初始化格式管理系统"""
        try:
            # 确保配置目录存在
            Path(self.config_path).parent.mkdir(parents=True, exist_ok=True)
            Path(self.field_mapping_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 加载配置
            self.format_config.load_config()
            self.field_registry.load_mappings()
            
            # 验证配置完整性
            self._validate_configuration()
            
            self.logger.info("🎯 [统一格式管理] 系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"🎯 [统一格式管理] 系统初始化失败: {e}")
            # 使用默认配置继续运行
            self._setup_default_configuration()
    
    def _validate_configuration(self):
        """验证配置完整性"""
        try:
            # 检查必要的配置项
            required_configs = ['table_types', 'default_formats', 'validation_rules']
            for config in required_configs:
                if not self.format_config.has_config(config):
                    self.logger.warning(f"🎯 [统一格式管理] 缺少必要配置: {config}")
            
            # 检查字段映射
            table_types = self.format_config.get_config('table_types', {})
            for table_type in table_types.keys():
                if not self.field_registry.has_table_mapping(table_type):
                    self.logger.warning(f"🎯 [统一格式管理] 缺少表类型字段映射: {table_type}")
            
            self.logger.debug("🎯 [统一格式管理] 配置验证完成")
            
        except Exception as e:
            self.logger.error(f"🎯 [统一格式管理] 配置验证失败: {e}")
    
    def _setup_default_configuration(self):
        """设置默认配置"""
        try:
            # 默认表类型配置
            default_table_types = {
                'active_employees': {
                    'display_name': '全部在职人员工资表',
                    'description': '包含所有在职员工的工资数据',
                    'priority': 1
                },
                'retired_employees': {
                    'display_name': '离休人员工资表', 
                    'description': '离休员工的工资数据',
                    'priority': 2
                }
            }
            
            # 默认格式配置
            default_formats = {
                'currency': {
                    'decimal_places': 2,
                    'thousand_separator': ',',
                    'symbol': '¥',
                    'negative_format': '-{symbol}{value}'
                },
                'percentage': {
                    'decimal_places': 1,
                    'symbol': '%'
                },
                'date': {
                    'format': 'YYYY-MM-DD',
                    'display_format': 'YYYY年MM月DD日'
                }
            }
            
            # 设置默认配置
            self.format_config.set_config('table_types', default_table_types)
            self.format_config.set_config('default_formats', default_formats)
            self.format_config.save_config()
            
            self.logger.info("🎯 [统一格式管理] 已设置默认配置")
            
        except Exception as e:
            self.logger.error(f"🎯 [统一格式管理] 设置默认配置失败: {e}")
    
    # ================== 核心格式化接口 ==================
    
    def format_headers(self, 
                      raw_headers: List[str], 
                      table_type: str,
                      data_source: str = 'unknown') -> List[str]:
        """
        格式化表头
        
        Args:
            raw_headers: 原始表头列表
            table_type: 表格类型（如 'active_employees'）
            data_source: 数据源标识
            
        Returns:
            格式化后的表头列表
        """
        try:
            # 检查缓存
            # 方案F：使用规范化后的 key，降低“同字段不同形态字符串”导致的缓存分裂
            norm_headers = [str(h).replace('\n', '').strip() for h in raw_headers]
            cache_key = f"headers_{table_type}_{hash(tuple(norm_headers))}"
            cache_hit = False
            
            if self._is_cache_valid(cache_key):
                cached_result = self._format_cache.get(cache_key)
                if cached_result:
                    cache_hit = True
                    self._stats['cache_hits'] += 1
                    self.logger.debug(f"🎯 [统一格式管理] 使用缓存的表头格式: {table_type}")
                    
                    # 🎯 [事件驱动] 发布表头格式化完成事件
                    self.event_bus.publish(Event(event_type="format_headers_completed", data={
                        "table_type": table_type,
                        "data_source": data_source,
                        "headers_count": len(cached_result),
                        "cache_hit": True
                    }))
                    
                    return cached_result
            
            self._stats['cache_misses'] += 1
            
            # 格式化表头
            formatted_headers = []
            for header in norm_headers:
                # 通过字段注册系统获取标准显示名称
                display_name = self.field_registry.get_display_name(header, table_type)
                if display_name:
                    formatted_headers.append(display_name)
                else:
                    # 如果没有找到映射，使用原始名称
                    # 方案E：避免把被截断/换行变体当作源，做最小规范化
                    safe_header = str(header).replace('\n', '').strip()
                    formatted_headers.append(safe_header)
                    self.logger.debug(f"🎯 [统一格式管理] 未找到字段映射: {header} (表类型: {table_type})")
            
            # 缓存结果
            self._format_cache[cache_key] = formatted_headers
            self._cache_timestamps[cache_key] = datetime.now()
            
            # 🎯 [事件驱动] 发布表头格式化完成事件
            self.event_bus.publish(Event(event_type="format_headers_completed", data={
                "table_type": table_type,
                "data_source": data_source,
                "headers_count": len(formatted_headers),
                "raw_headers": raw_headers,
                "formatted_headers": formatted_headers,
                "cache_hit": False
            }))
            
            self.logger.info(f"🎯 [统一格式管理] 表头格式化完成: {table_type}, 字段数: {len(formatted_headers)}")
            return formatted_headers
            
        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(f"🎯 [统一格式管理] 表头格式化失败: {e}")
            
            # 🎯 [事件驱动] 发布错误事件
            self.event_bus.publish(Event(event_type="format_headers_failed", data={
                "table_type": table_type,
                "data_source": data_source,
                "error": str(e),
                "timestamp": time.time()
            }))
            
            # 返回原始表头作为后备方案
            return raw_headers
    
    def format_data(self, 
                   data: Union[pd.DataFrame, List[Dict], List[List]], 
                   table_type: str,
                   headers: Optional[List[str]] = None,
                   data_source: str = 'unknown') -> pd.DataFrame:
        """
        格式化数据
        
        Args:
            data: 原始数据（DataFrame、字典列表或列表的列表）
            table_type: 表格类型
            headers: 表头列表（如果data不包含表头）
            data_source: 数据源标识
            
        Returns:
            格式化后的DataFrame
        """
        try:
            # 转换为DataFrame
            if isinstance(data, pd.DataFrame):
                df = data.copy()
            elif isinstance(data, list) and len(data) > 0:
                if isinstance(data[0], dict):
                    # 字典列表
                    df = pd.DataFrame(data)
                elif isinstance(data[0], (list, tuple)):
                    # 列表的列表
                    if headers:
                        df = pd.DataFrame(data, columns=headers)
                    else:
                        df = pd.DataFrame(data)
                else:
                    raise ValueError(f"不支持的数据格式: {type(data[0])}")
            else:
                raise ValueError(f"不支持的数据类型: {type(data)}")
            
            # 如果DataFrame为空，直接返回
            if df.empty:
                self.logger.warning(f"🎯 [统一格式管理] 数据为空: {table_type}")
                
                # 🎯 [事件驱动] 发布数据为空事件
                self.event_bus.publish(Event(event_type="format_data_empty", data={
                    "table_type": table_type,
                    "data_source": data_source,
                    "timestamp": time.time()
                }))
                
                return df
            
            # 使用格式渲染器格式化数据
            formatted_df = self.format_renderer.render_dataframe(df, table_type)
            
            # 🎯 [事件驱动] 发布数据格式化完成事件
            self.event_bus.publish(Event(event_type="format_data_completed", data={
                "table_type": table_type,
                "data_source": data_source,
                "data_count": len(formatted_df),
                "columns_count": len(formatted_df.columns) if not formatted_df.empty else 0
            }))
            
            self.logger.info(f"🎯 [统一格式管理] 数据格式化完成: {table_type}, 行数: {len(formatted_df)}, 列数: {len(formatted_df.columns)}")
            return formatted_df
            
        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(f"🎯 [统一格式管理] 数据格式化失败: {e}")
            
            # 🎯 [事件驱动] 发布数据格式化失败事件
            self.event_bus.publish(Event(event_type="format_data_failed", data={
                "table_type": table_type,
                "data_source": data_source,
                "error": str(e),
                "timestamp": time.time()
            }))
            
            # 返回原始数据作为后备方案
            if isinstance(data, pd.DataFrame):
                return data
            else:
                return pd.DataFrame(data) if data else pd.DataFrame()
    
    def format_table_data(self, data: Union[pd.DataFrame, List[Dict]], table_type: str, 
                         force_reformat: bool = False) -> Union[pd.DataFrame, List[Dict]]:
        """
        🔧 [兼容接口] 兼容MasterFormatManager的format_table_data接口
        
        这个方法提供与旧MasterFormatManager完全兼容的接口，
        确保迁移过程中不破坏现有代码。
        
        Args:
            data: 原始数据（DataFrame或字典列表）
            table_type: 表格类型
            force_reformat: 是否强制重新格式化
            
        Returns:
            格式化后的数据（保持原始数据类型）
        """
        try:
            # 转换table_type到标准格式
            normalized_table_type = self._normalize_table_type(table_type)
            
            # 使用format_data方法进行格式化
            formatted_df = self.format_data(
                data=data, 
                table_type=normalized_table_type,
                data_source='compatibility_layer'
            )
            
            # 如果原始数据是字典列表，返回字典列表
            if isinstance(data, list) and data and isinstance(data[0], dict):
                return formatted_df.to_dict('records')
            
            # 否则返回DataFrame
            return formatted_df
            
        except Exception as e:
            self.logger.error(f"🔧 [兼容接口] format_table_data失败: {e}")
            return data
    
    def _normalize_table_type(self, table_type: str) -> str:
        """
        标准化表格类型名称
        
        Args:
            table_type: 原始表格类型
            
        Returns:
            标准化后的表格类型
        """
        # 🔧 [P0-CRITICAL修复] 使用与TableDataService完全一致的表名映射逻辑
        # 确保UnifiedFormatManager和TableDataService使用相同的表名解析
        try:
            # 复用TableDataService的映射逻辑，确保完全一致
            table_name_lower = table_type.lower()
            
            if 'active_employees' in table_name_lower or '全部在职人员' in table_type:
                return 'active_employees'
            elif 'retired_employees' in table_name_lower or '离休人员' in table_type:
                return 'retired_employees'
            elif 'pension_employees' in table_name_lower or '退休人员工资表' in table_type or '退休人员' in table_type:
                return 'pension_employees'  # 🎯 [新增] 退休人员使用独立的格式配置
            elif 'part_time' in table_name_lower or '临时工' in table_type:
                return 'part_time_employees'
            elif 'contract' in table_name_lower or '合同工' in table_type:
                return 'contract_employees'
            elif 'a_grade' in table_name_lower or 'A岗' in table_type:
                return 'a_grade_employees'
            else:
                # 🔧 [P0-CRITICAL修复] 如果没有匹配，直接返回原值而不是默认映射
                # 这样可以确保具体表名能够被正确处理
                self.logger.debug(f"🔧 [表名映射] 未找到匹配的表名模式: {table_type}, 使用原值")
                return table_type
                
        except Exception as e:
            self.logger.error(f"🔧 [表名映射] 标准化表格类型失败: {e}")
            return table_type
    
    def format_display_value(self, value: Any, column_name: str, table_type: str = 'unknown') -> str:
        """
        🔧 [兼容接口] 格式化单个显示值
        
        兼容旧的format_display_value方法调用。
        
        Args:
            value: 要格式化的值
            column_name: 列名
            table_type: 表格类型
            
        Returns:
            格式化后的字符串值
        """
        try:
            # 🔧 [根本修复] 优先使用已配置的字段类型，而不是模式推断
            field_type = self._get_configured_field_type(column_name, table_type)
            if not field_type:
                # 如果没有配置，才回退到推断
                field_type = self._infer_field_type_from_column_name(column_name)
            
            # 使用格式渲染器格式化单个值
            return self.format_renderer.render_value(
                value=value,
                field_type=field_type,
                field_name=column_name,
                table_type=table_type
            )
            
        except Exception as e:
            self.logger.error(f"🔧 [兼容接口] format_display_value失败: {e}")
            return str(value) if value is not None else ""
    
    def _get_configured_field_type(self, column_name: str, table_type: str) -> str:
        """
        🔧 [根本修复] 从已配置的字段类型中获取正确的字段类型
        
        Args:
            column_name: 列名
            table_type: 表格类型
            
        Returns:
            配置的字段类型，如果没有配置则返回None
        """
        try:
            # 标准化表格类型
            normalized_table_type = self._normalize_table_type(table_type)
            
            # 获取字段类型配置
            field_types = self.field_registry.get_table_field_types(normalized_table_type)
            
            # 通过中文显示名查找字段类型
            field_mappings = self.field_registry.get_table_mapping(normalized_table_type)
            
            # 直接通过列名查找
            if column_name in field_types:
                return field_types[column_name]
            
            # 通过字段映射反向查找
            for english_name, chinese_name in field_mappings.items():
                if chinese_name == column_name and english_name in field_types:
                    return field_types[english_name]
            
            # 特殊处理：根据用户反馈的问题字段强制返回float类型
            problem_float_fields = [
                '2025年基础性绩效', '2025年奖励性绩效预发', '车补', '代扣代存养老保险'
            ]
            if column_name in problem_float_fields:
                self.logger.debug(f"🔧 [根本修复] 问题字段 {column_name} 强制返回float类型")
                return 'float'
            
            return None  # 没有找到配置
            
        except Exception as e:
            self.logger.error(f"🔧 [根本修复] 获取配置字段类型失败: {e}")
            return None
    
    def _infer_field_type_from_column_name(self, column_name: str) -> str:
        """
        从列名推断字段类型
        
        Args:
            column_name: 列名
            
        Returns:
            推断的字段类型
        """
        column_lower = column_name.lower()
        
        # 货币字段
        if any(keyword in column_lower for keyword in ['工资', '津贴', '补贴', '费', '金', '补发', '借支', '合计']):
            return 'float'
        
        # 代码字段
        if any(keyword in column_lower for keyword in ['工号', '代码', 'id', 'code']):
            return 'string'
        
        # 日期字段
        if any(keyword in column_lower for keyword in ['日期', 'date', '时间', 'time']):
            return 'date'
        
        # 月份字段
        if '月份' in column_lower or 'month' in column_lower:
            return 'month_string'
        
        # 年份字段
        if '年份' in column_lower or 'year' in column_lower:
            return 'year_string'
        
        # 默认为字符串
        return 'string'
    
    def format_table_complete(self, 
                             data: Union[pd.DataFrame, List[Dict], List[List]], 
                             table_type: str,
                             data_source: str = 'unknown') -> Tuple[List[str], pd.DataFrame]:
        """
        完整的表格格式化（表头 + 数据）
        
        Args:
            data: 原始数据
            table_type: 表格类型
            data_source: 数据源标识
            
        Returns:
            (格式化后的表头, 格式化后的数据)
        """
        start_time = time.time()
        
        try:
            self._stats['total_formats'] += 1
            
            # 先格式化数据
            formatted_data = self.format_data(data, table_type, data_source=data_source)
            
            # 格式化表头
            raw_headers = list(formatted_data.columns)
            formatted_headers = self.format_headers(raw_headers, table_type, data_source=data_source)
            
            # 更新DataFrame的列名
            formatted_data.columns = formatted_headers
            
            processing_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 🎯 [事件驱动] 发布完整表格格式化完成事件
            self.event_bus.publish(Event(event_type="format_table_completed", data={
                "table_type": table_type,
                "data_source": data_source,
                "headers_count": len(formatted_headers),
                "data_count": len(formatted_data),
                "processing_time": processing_time,
                "timestamp": time.time()
            }))
            
            # 🎯 [统一状态管理] 更新最近格式化的表格信息
            self.state_manager.set_state("format_management.last_formatted_table", {
                "table_type": table_type,
                "data_source": data_source,
                "headers_count": len(formatted_headers),
                "data_count": len(formatted_data),
                "timestamp": time.time()
            })
            
            self.logger.info(f"🎯 [统一格式管理] 完整表格格式化完成: {table_type}, 耗时: {processing_time:.2f}ms")
            return formatted_headers, formatted_data
            
        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(f"🎯 [统一格式管理] 完整表格格式化失败: {e}")
            
            # 🎯 [事件驱动] 发布完整表格格式化失败事件
            self.event_bus.publish(Event(event_type="format_table_failed", data={
                "table_type": table_type,
                "data_source": data_source,
                "error": str(e),
                "timestamp": time.time()
            }))
            
            # 返回原始数据作为后备方案
            if isinstance(data, pd.DataFrame):
                return list(data.columns), data
            else:
                df = pd.DataFrame(data) if data else pd.DataFrame()
                return list(df.columns), df
    
    # ================== 配置管理接口 ==================
    
    def update_field_mapping(self, 
                            table_type: str, 
                            field_mappings: Dict[str, str]):
        """
        更新字段映射
        
        Args:
            table_type: 表格类型
            field_mappings: 字段映射字典 {数据库字段名: 显示名称}
        """
        try:
            self.field_registry.update_table_mapping(table_type, field_mappings)
            
            # 清除相关缓存
            self._clear_cache_by_table_type(table_type)
            
            # 🎯 [事件驱动] 发布字段映射更新事件
            self.event_bus.publish(Event(event_type="field_mapping_updated", data={
                "table_type": table_type,
                "field_mappings": field_mappings,
                "timestamp": time.time()
            }))
            
            # 🎯 [统一状态管理] 更新状态
            self.state_manager.set_state(f"format_management.field_mappings.{table_type}", field_mappings)
            
            self.logger.info(f"🎯 [统一格式管理] 字段映射更新完成: {table_type}")
            
        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(f"🎯 [统一格式管理] 字段映射更新失败: {e}")
            
            # 🎯 [事件驱动] 发布字段映射更新失败事件
            self.event_bus.publish(Event(event_type="field_mapping_update_failed", data={
                "table_type": table_type,
                "error": str(e),
                "timestamp": time.time()
            }))
    
    def update_format_config(self, 
                           config_key: str, 
                           config_value: Any):
        """
        更新格式配置
        
        Args:
            config_key: 配置键
            config_value: 配置值
        """
        try:
            self.format_config.set_config(config_key, config_value)
            self.format_config.save_config()
            
            # 清除全部缓存（因为格式配置影响所有表）
            self._clear_all_cache()
            
            # 🎯 [事件驱动] 发布格式配置更新事件
            self.event_bus.publish(Event(event_type="format_config_updated", data={
                "config_key": config_key,
                "config_value": config_value,
                "timestamp": time.time()
            }))
            
            # 🎯 [统一状态管理] 更新状态
            self.state_manager.set_state(f"format_management.config.{config_key}", config_value)
            
            self.logger.info(f"🎯 [统一格式管理] 格式配置更新完成: {config_key}")
            
        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(f"🎯 [统一格式管理] 格式配置更新失败: {e}")
            
            # 🎯 [事件驱动] 发布格式配置更新失败事件
            self.event_bus.publish(Event(event_type="format_config_update_failed", data={
                "config_key": config_key,
                "error": str(e),
                "timestamp": time.time()
            }))
    
    def get_supported_table_types(self) -> Dict[str, Dict]:
        """
        获取支持的表格类型
        
        Returns:
            表格类型字典 {类型ID: 类型信息}
        """
        try:
            return self.format_config.get_config('table_types', {})
        except Exception as e:
            self.logger.error(f"🎯 [统一格式管理] 获取支持的表格类型失败: {e}")
            return {}
    
    def get_field_mappings(self, table_type: str) -> Dict[str, str]:
        """
        获取字段映射
        
        Args:
            table_type: 表格类型
            
        Returns:
            字段映射字典
        """
        try:
            return self.field_registry.get_table_mapping(table_type)
        except Exception as e:
            self.logger.error(f"🎯 [统一格式管理] 获取字段映射失败: {e}")
            return {}
    
    # ================== 缓存管理 ==================
    
    def _is_cache_valid(self, cache_key: str, ttl_minutes: int = 30) -> bool:
        """检查缓存是否有效"""
        try:
            if cache_key not in self._cache_timestamps:
                return False
            
            cache_time = self._cache_timestamps[cache_key]
            age_minutes = (datetime.now() - cache_time).total_seconds() / 60
            
            return age_minutes < ttl_minutes
            
        except Exception as e:
            self.logger.error(f"🎯 [统一格式管理] 缓存有效性检查失败: {e}")
            return False
    
    def _clear_cache_by_table_type(self, table_type: str):
        """清除特定表类型的缓存"""
        try:
            keys_to_remove = [key for key in self._format_cache.keys() 
                            if table_type in key]
            
            for key in keys_to_remove:
                self._format_cache.pop(key, None)
                self._cache_timestamps.pop(key, None)
            
            self.logger.debug(f"🎯 [统一格式管理] 已清除表类型缓存: {table_type}")
            
        except Exception as e:
            self.logger.error(f"🎯 [统一格式管理] 清除表类型缓存失败: {e}")
    
    def _clear_all_cache(self):
        """清除所有缓存"""
        try:
            self._format_cache.clear()
            self._cache_timestamps.clear()
            
            self.logger.debug("🎯 [统一格式管理] 已清除所有缓存")
            
        except Exception as e:
            self.logger.error(f"🎯 [统一格式管理] 清除所有缓存失败: {e}")
    
    # ================== 系统状态和统计 ==================
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            系统状态信息
        """
        try:
            system_status = {
                'initialized': True,
                'config_loaded': self.format_config.is_loaded(),
                'field_mappings_loaded': self.field_registry.is_loaded(),
                'supported_table_types': len(self.get_supported_table_types()),
                'cache_entries': len(self._format_cache),
                'last_update': datetime.now().isoformat(),
                'event_bus_connected': self.event_bus is not None,
                'state_manager_connected': self.state_manager is not None,
                'stats': self._stats
            }
            
            # 🎯 [统一状态管理] 更新系统状态
            self.state_manager.set_state("format_management.system_status", system_status)
            
            return system_status
            
        except Exception as e:
            self.logger.error(f"🎯 [统一格式管理] 获取系统状态失败: {e}")
            return {'error': str(e)}
    
    def reload_configuration(self):
        """重新加载配置"""
        try:
            self.format_config.load_config()
            self.field_registry.load_mappings()
            self._clear_all_cache()
            
            # 🎯 [事件驱动] 发布配置重新加载事件
            self.event_bus.publish(Event(event_type="configuration_reloaded", data={
                "config_loaded": self.format_config.is_loaded(),
                "field_mappings_loaded": self.field_registry.is_loaded(),
                "timestamp": time.time()
            }))
            
            # 🎯 [统一状态管理] 更新状态
            self.state_manager.set_state("format_management.config_loaded", self.format_config.is_loaded())
            self.state_manager.set_state("format_management.field_registry_loaded", self.field_registry.is_loaded())
            
            self.logger.info("🎯 [统一格式管理] 配置重新加载完成")
            
        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(f"🎯 [统一格式管理] 配置重新加载失败: {e}")
            
            # 🎯 [事件驱动] 发布配置重新加载失败事件
            self.event_bus.publish(Event(event_type="configuration_reload_failed", data={
                "error": str(e),
                "timestamp": time.time()
            }))
    
    # ================== 新架构集成方法 ==================
    
    def _setup_event_listeners(self):
        """
        设置事件监听器
        """
        # 监听配置文件变更事件
        self.event_bus.subscribe("config_file_changed", self._on_config_file_changed)
        
        # 监听字段映射文件变更事件
        self.event_bus.subscribe("field_mapping_file_changed", self._on_field_mapping_file_changed)
        
        # 🚀 [关键修复] 监听配置更新事件，立即重新加载配置
        self.event_bus.subscribe("config_updated", self._on_config_updated)
        
        # 监听系统状态变更事件
        self.event_bus.subscribe("system_state_changed", self._on_system_state_changed)
        
        self.logger.info("🎯 [事件驱动] 事件监听器设置完成")
    
    def _sync_to_state_manager(self):
        """
        将格式管理器状态同步到统一状态管理器
        """
        # 同步配置状态
        self.state_manager.set_state("format_management.config_loaded", self.format_config.is_loaded())
        
        # 同步字段注册状态
        self.state_manager.set_state("format_management.field_registry_loaded", self.field_registry.is_loaded())
        
        # 同步统计信息
        self.state_manager.set_state("format_management.stats", self._stats)
        
        self.logger.info("🎯 [统一状态管理] 状态同步完成")
    
    def _on_config_file_changed(self, event_data):
        """
        配置文件变更事件处理器
        """
        self.logger.info(f"🎯 [事件驱动] 检测到配置文件变更: {event_data}")
        
        # 重新加载配置
        self.format_config.load_config()
        
        # 清除相关缓存
        self._clear_all_cache()
        
        # 更新状态管理器
        self.state_manager.set_state("format_management.config_loaded", self.format_config.is_loaded())
        
        # 发布配置重新加载完成事件
        self.event_bus.publish(Event(event_type="format_config_reloaded", data={
            "timestamp": time.time(),
            "config_loaded": self.format_config.is_loaded()
        }))
    
    def _on_field_mapping_file_changed(self, event_data):
        """
        字段映射文件变更事件处理器
        """
        self.logger.info(f"🎯 [事件驱动] 检测到字段映射文件变更: {event_data}")
        
        # 重新加载字段映射
        self.field_registry.load_mappings()
        
        # 清除相关缓存
        self._clear_all_cache()
        
        # 更新状态管理器
        self.state_manager.set_state("format_management.field_registry_loaded", self.field_registry.is_loaded())
        
        # 发布字段映射重新加载完成事件
    
    def _on_config_updated(self, event_data):
        """
        🚀 [关键修复] 配置更新事件处理器 - 立即重新加载所有相关配置
        """
        try:
            table_name = event_data.get("table_name", "unknown")
            update_type = event_data.get("update_type", "unknown")
            self.logger.info(f"🚀 [配置同步] 收到配置更新事件: 表={table_name}, 类型={update_type}")
            
            # 强制重新加载字段注册表配置
            old_loaded = self.field_registry.is_loaded()
            self.field_registry.reload_mappings()  # 强制重新加载
            new_loaded = self.field_registry.is_loaded()
            
            # 清除所有缓存，确保新配置生效
            self._clear_all_cache()
            
            # 重新初始化格式渲染器，确保它使用最新的配置
            self.format_renderer = FormatRenderer(self.format_config, self.field_registry)
            
            # 更新状态管理器
            self.state_manager.set_state("format_management.field_registry_loaded", new_loaded)
            self.state_manager.set_state("format_management.last_config_update", datetime.now().isoformat())
            
            self.logger.info(f"🚀 [配置同步] 配置重新加载完成: {table_name}, 加载状态: {old_loaded} -> {new_loaded}")
            
            # 发布配置重新加载完成事件
            self.event_bus.publish(Event(
                type="format_manager_config_reloaded",
                data={
                    "table_name": table_name,
                    "update_type": update_type,
                    "timestamp": datetime.now().isoformat(),
                    "success": True
                }
            ))
            
        except Exception as e:
            self.logger.error(f"🚀 [配置同步] 配置更新事件处理失败: {e}")
            # 发布失败事件
            self.event_bus.publish(Event(
                type="format_manager_config_reload_failed", 
                data={"error": str(e)}
            ))
        self.event_bus.publish(Event(event_type="field_mapping_reloaded", data={
            "timestamp": time.time(),
            "field_registry_loaded": self.field_registry.is_loaded()
        }))
    
    def _on_system_state_changed(self, event_data):
        """
        系统状态变更事件处理器
        """
        self.logger.info(f"🎯 [事件驱动] 检测到系统状态变更: {event_data}")
        
        # 根据状态变更调整格式管理器行为
        if event_data.get("state_key") == "format_management.enabled":
            enabled = event_data.get("state_value", True)
            if not enabled:
                self.logger.warning("🎯 [统一格式管理] 格式管理器已被禁用")
                # 可以在这里添加禁用逻辑
            else:
                self.logger.info("🎯 [统一格式管理] 格式管理器已启用")
                # 可以在这里添加启用逻辑
    
    def clear_cache(self) -> int:
        """
        清除缓存
        
        Returns:
            清除的缓存项数量
        """
        cleared_count = len(self._format_cache)
        self._format_cache.clear()
        self._cache_timestamps.clear()
        
        # 🎯 [事件驱动] 发布缓存清理事件
        self.event_bus.publish(Event(event_type="cache_cleared", data={
            "cleared_count": cleared_count,
            "timestamp": time.time()
        }))
        
        self.logger.info(f"🎯 [统一格式管理] 缓存清理完成: 清理了 {cleared_count} 个缓存项")
        return cleared_count
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        cache_hit_rate = 0
        if self._stats['cache_hits'] + self._stats['cache_misses'] > 0:
            cache_hit_rate = (self._stats['cache_hits'] / 
                            (self._stats['cache_hits'] + self._stats['cache_misses'])) * 100
        
        return {
            'total_formats': self._stats['total_formats'],
            'cache_hits': self._stats['cache_hits'],
            'cache_misses': self._stats['cache_misses'],
            'cache_hit_rate': cache_hit_rate,
            'errors': self._stats['errors'],
            'cache_size': len(self._format_cache)
        }


# ================== 单例模式实现 ==================

import threading
from typing import Optional as OptionalType

class SingletonMeta(type):
    """
    线程安全的单例元类
    """
    _instances = {}
    _lock: threading.Lock = threading.Lock()

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]


class SingletonUnifiedFormatManager(UnifiedFormatManager, metaclass=SingletonMeta):
    """
    🔧 [单例优化] 单例模式的统一格式管理器
    
    确保在整个应用程序生命周期中只有一个格式管理器实例，
    避免重复初始化带来的性能开销。
    """
    
    def __init__(self, *args, **kwargs):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return
        
        super().__init__(*args, **kwargs)
        self._initialized = True
        self.logger.info("🔧 [单例优化] 单例统一格式管理器初始化完成")


# 全局单例实例
_global_unified_format_manager: OptionalType[SingletonUnifiedFormatManager] = None
_global_manager_lock = threading.Lock()


def get_unified_format_manager(
    config_path: OptionalType[str] = None,
    field_mapping_path: OptionalType[str] = None,
    event_bus: OptionalType[EventBus] = None,
    state_manager: OptionalType[UnifiedStateManager] = None
) -> UnifiedFormatManager:
    """
    🔧 [统一接口] 获取统一格式管理器实例
    
    这是获取格式管理器的统一入口，替代所有旧的get_master_format_manager调用。
    
    Args:
        config_path: 格式配置文件路径
        field_mapping_path: 字段映射配置文件路径
        event_bus: 事件总线实例
        state_manager: 状态管理器实例
        
    Returns:
        统一格式管理器实例
    """
    global _global_unified_format_manager
    
    if _global_unified_format_manager is None:
        with _global_manager_lock:
            if _global_unified_format_manager is None:
                _global_unified_format_manager = SingletonUnifiedFormatManager(
                    config_path=config_path,
                    field_mapping_path=field_mapping_path,
                    event_bus=event_bus,
                    state_manager=state_manager
                )
    
    return _global_unified_format_manager


def reset_unified_format_manager():
    """
    🔧 [测试支持] 重置全局格式管理器实例
    
    主要用于测试场景，清理全局状态。
    """
    global _global_unified_format_manager
    
    with _global_manager_lock:
        _global_unified_format_manager = None
        # 清理单例元类的实例缓存
        if SingletonUnifiedFormatManager in SingletonMeta._instances:
            del SingletonMeta._instances[SingletonUnifiedFormatManager]