#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统就绪检测网关

提供统一的系统就绪判定，避免在数据库/元数据未就绪阶段触发导航重建与兜底空表，
引发闪屏、误判与一致性告警。
"""
from typing import Tuple

from src.utils.log_config import setup_logger

logger = setup_logger(__name__)


def is_system_ready(dynamic_table_manager) -> Tuple[bool, str]:
    """判断系统是否就绪

    Returns:
        (ready: bool, reason: str)
    """
    try:
        if dynamic_table_manager is None:
            return False, "DynamicTableManager 未初始化"

        if hasattr(dynamic_table_manager, 'is_database_ready'):
            if not dynamic_table_manager.is_database_ready():
                return False, "数据库未就绪"

        # 尝试获取导航元数据（可选）
        if hasattr(dynamic_table_manager, 'get_navigation_tree_data'):
            try:
                _ = dynamic_table_manager.get_navigation_tree_data()
            except Exception as e:
                logger.debug(f"导航元数据暂不可用: {e}")
                # 元数据暂不可用不直接判失败，由调用侧策略决定

        return True, ""
    except Exception as e:
        logger.warning(f"就绪检测异常: {e}")
        return False, str(e)


