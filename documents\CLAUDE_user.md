## 核心要求
- 全程使用中文对话！
- 每次都用审视的目光，仔看我输入的潜在问题，你要指出我的问题，并给出明显在我思考框架之外的建议。
- 如果你觉得我说的太离谱了，你就骂回来，帮我瞬间清醒。
- 每次项目代码文件修改或新建完成后，都要进行测试！检查是否存在语法错误，是否存在遗漏，是否存在重复等等！
- 重视日志的规范性，日志必须符合项目的日志规范，要进行统一管理，要考虑项目开发环境与生产环境的差异。


## 我的承诺
从现在开始，我将：
  1. 每次修改前都进行全面的影响分析。
  2. 绝不使用未经验证的自动化脚本。
  3. 对每个修改都进行跨平台兼容性检查。
  4. 严格遵循"新架构优先"原则，不做向后兼容。

## 重要教训

### 深刻教训：
  - 永远不要用未经验证的自动化脚本修改生产代码
  - 任何批量操作都必须先在小范围测试
  - 重要修改必须有备份和回滚机制

### 遇到Qt底层错误时必须立即转向架构分析，不能依赖日志，必须深入代码进行系统性修复。

#### Qt底层架构问题分析规范

##### 🚨 遇到Qt控制台错误时的处理原则
1. **立即停止常规分析**：看到 QBasicTimer、QPaintDevice、recursive repaint 等错误，直接按架构问题处理
2. **日志文件无参考价值**：Qt底层问题通常不会留下有用的应用日志
3. **必须深入代码分析**：使用 ultrathink 系统性分析代码，不能只看表面症状

##### 🎯 Qt问题识别关键词
- `QBasicTimer::start: QBasicTimer can only be used with threads started with QThread`
- `QPaintDevice: Cannot destroy paint device that is being painted`
- `QWidget::repaint: Recursive repaint detected`
- 程序异常退出且无明确错误信息

##### 🔧 修复策略
1. **线程安全优先**：检查所有 QTimer 和 Qt 组件的线程使用
2. **禁用危险调用**：用 update() 替代 repaint()，避免递归重绘
3. **状态防护机制**：添加绘制状态标志，防止冲突
4. **架构容错性**：关键组件必须有紧急初始化机制

##### 🔄 预防措施
- 建立Qt安全检查工具自动扫描
- 制定强制性Qt开发规范
- 设立Qt问题预警检查清单
