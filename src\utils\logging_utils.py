"""
日志工具库（不触碰业务路径）

提供统一的日志治理基础能力：
- 节流（throttle）/ 采样（sample）/ 去重（dedup）/ 聚合（aggregate）/ 一次性（once）
- 上下文绑定与敏感信息脱敏
- DEBUG RingBuffer 旁路（用于现场排障，不落地磁盘）

设计目标：高内聚、低侵入、可渐进接入，默认纯函数式返回布尔值以决定是否打印。
"""

from __future__ import annotations

import re
import threading
import time
from collections import deque
from dataclasses import dataclass
from typing import Deque, Dict, Optional, Tuple
from pathlib import Path


# ------------------------------
# 线程安全的内存状态
# ------------------------------
_lock = threading.RLock()

_throttle_state: Dict[str, float] = {}
_sample_state: Dict[str, int] = {}
_dedup_state: Dict[str, Tuple[float, str]] = {}
_once_state: Dict[str, bool] = {}

# 聚合：key -> (window_start_ts, count)
_aggregate_state: Dict[str, Tuple[float, int]] = {}


# ------------------------------
# 核心治理函数
# ------------------------------
def log_throttle(key: str, min_interval_s: float) -> bool:
    """基于 key 的最小间隔节流；返回 True 表示允许本次打印。"""
    now = time.time()
    with _lock:
        last = _throttle_state.get(key, 0.0)
        if now - last >= max(0.0, float(min_interval_s)):
            _throttle_state[key] = now
            return True
        return False


def log_sample(key: str, every_n: int) -> bool:
    """每 N 次打印一次；返回 True 表示允许本次打印。"""
    if every_n <= 1:
        return True
    with _lock:
        count = _sample_state.get(key, 0) + 1
        _sample_state[key] = count
        return count % every_n == 0


def dedup_log(key: str, window_ms: int, message: Optional[str] = None) -> bool:
    """在 window_ms 窗口内对相同 message 去重；返回 True 表示允许本次打印。"""
    now = time.time()
    window_s = max(0.0, float(window_ms) / 1000.0)
    msg = message or "__NO_MESSAGE__"
    with _lock:
        last_ts, last_msg = _dedup_state.get(key, (0.0, ""))
        if msg == last_msg and (now - last_ts) < window_s:
            return False
        _dedup_state[key] = (now, msg)
        return True


def log_once(key: str) -> bool:
    """进程生命周期仅允许一次；返回 True 表示允许本次打印。"""
    with _lock:
        if _once_state.get(key):
            return False
        _once_state[key] = True
        return True


def aggregate_register(key: str) -> None:
    """注册一次聚合事件计数。"""
    now = time.time()
    with _lock:
        start, cnt = _aggregate_state.get(key, (now, 0))
        _aggregate_state[key] = (start, cnt + 1)


def aggregate_should_emit(key: str, window_ms: int) -> Tuple[bool, int]:
    """判断是否到达窗口，若是则返回(True, count)并清零；否则(False, 0)。"""
    now = time.time()
    window_s = max(0.0, float(window_ms) / 1000.0)
    with _lock:
        if key not in _aggregate_state:
            return False, 0
        start, cnt = _aggregate_state[key]
        if (now - start) >= window_s:
            _aggregate_state.pop(key, None)
            return True, cnt
        return False, 0


# ------------------------------
# 脱敏与上下文
# ------------------------------
_re_digits = re.compile(r"(\d{6})\d{4,}(\d{2,})")


def redact(text: str) -> str:
    """对可能的敏感数字做掩码，保留前6后2。"""
    if not isinstance(text, str):
        return text
    return _re_digits.sub(r"\1****\2", text)


def bind_context(logger, **context):
    """返回绑定上下文的 logger（适配 loguru.bind）。"""
    try:
        return logger.bind(**context)
    except Exception:
        return logger


# ------------------------------
# DEBUG RingBuffer（旁路兜底）
# ------------------------------
@dataclass
class DebugLogEntry:
    ts: float
    level: str
    module: str
    message: str


class DebugRingBuffer:
    """线程安全的内存环形缓冲，用于存放最近 N 条 DEBUG 日志。"""

    def __init__(self, capacity: int = 2000) -> None:
        self._capacity = max(100, int(capacity))
        self._buf: Deque[DebugLogEntry] = deque(maxlen=self._capacity)
        self._lock = threading.RLock()

    def add(self, level: str, module: str, message: str) -> None:
        with self._lock:
            self._buf.append(DebugLogEntry(time.time(), level, module, message))

    def snapshot(self) -> Tuple[DebugLogEntry, ...]:
        with self._lock:
            return tuple(self._buf)

    def clear(self) -> None:
        with self._lock:
            self._buf.clear()


# 单例（按需使用）
_global_debug_buffer: Optional[DebugRingBuffer] = None


def get_debug_buffer(capacity: int = 2000) -> DebugRingBuffer:
    global _global_debug_buffer
    if _global_debug_buffer is None:
        _global_debug_buffer = DebugRingBuffer(capacity=capacity)
    return _global_debug_buffer


def create_ring_sink(level: str = "DEBUG"):
    """创建一个可用于 loguru 的 sink，可将日志写入内存 RingBuffer。
    用法（在日志配置处）：logger.add(create_ring_sink(), level="DEBUG")
    """
    try:
        from loguru import logger as _logger  # 延迟导入
    except Exception:
        _logger = None

    buffer = get_debug_buffer()

    def _sink(message):
        try:
            record = message.record
            lvl = record.get("level").name if record.get("level") else level
            module = record.get("name") or record.get("module") or ""
            text = record.get("message") or str(message)
            buffer.add(lvl, module, text)
        except Exception:
            # 不影响主日志链路
            pass

    return _sink


def export_debug_ring_to_file(output_path: Optional[str] = None) -> str:
    """导出当前 RingBuffer 内容到文件，返回导出文件路径。
    默认导出到项目根目录下的 temp/debug_ring_<ts>.log。
    """
    snapshot = get_debug_buffer().snapshot()
    # 计算项目根目录（本文件 -> utils -> src -> project_root）
    project_root = Path(__file__).resolve().parents[2]
    temp_dir = project_root / "temp"
    temp_dir.mkdir(exist_ok=True)
    if output_path:
        out = Path(output_path)
    else:
        out = temp_dir / f"debug_ring_{int(time.time())}.log"

    try:
        with out.open("w", encoding="utf-8") as f:
            for entry in snapshot:
                f.write(f"{entry.ts:.3f}\t{entry.level}\t{entry.module}\t{entry.message}\n")
    except Exception:
        # 兜底失败时返回空路径
        return ""
    return str(out)

