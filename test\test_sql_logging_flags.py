import os
from pathlib import Path

from src.modules.data_storage.database_manager import DatabaseManager


def test_slow_sql_threshold_and_detail_logging(tmp_path):
    # 将慢SQL阈值设为0，详日志打开，确保代码路径可执行且不抛异常
    os.environ["LOG_SLOW_SQL_MS"] = "0"
    os.environ["LOG_SQL_DETAIL"] = "1"

    test_db = tmp_path / "test_logging.db"
    db = DatabaseManager(db_path=str(test_db))

    # 基本建表与插入
    db.execute_update("CREATE TABLE IF NOT EXISTS t (id INTEGER PRIMARY KEY, v TEXT)")
    db.execute_update("INSERT INTO t(v) VALUES (?)", ("abc1234567890z",))

    # 查询（带参数），触发详日志路径和慢SQL路径（阈值为0）
    rows = db.execute_query("SELECT * FROM t WHERE v LIKE ?", ("abc%",))
    assert isinstance(rows, list)

    # 清理环境变量，避免影响其他用例
    os.environ.pop("LOG_SLOW_SQL_MS", None)
    os.environ.pop("LOG_SQL_DETAIL", None)


