"""
月度工资异动处理系统 - 日志配置模块

本模块提供统一的日志配置和管理功能，支持：
- 文件日志和控制台日志
- 日志级别控制
- 日志格式自定义
- 日志文件轮转
- 线程安全的日志记录

依赖库：
- loguru: 现代化日志库，替代标准logging
- pathlib: 路径处理
- os: 环境变量获取

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
更新时间: 2025-01-22 (REFLECT Day 2 - Logger类型修复)
"""

import os
import sys
from pathlib import Path
from typing import Optional, Union, Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from loguru import Logger
else:
    from loguru import logger
    Logger = type(logger)

from loguru import logger

# 移除默认handler，避免重复输出
logger.remove()

# 全局初始化标记，避免函数属性的类型问题
_logger_initialized: bool = False

# 日志目录路径
def get_log_directory() -> Path:
    """
    获取日志目录路径
    
    Returns:
        Path: 日志目录的绝对路径
    """
    # 获取项目根目录
    current_file = Path(__file__)
    project_root = current_file.parent.parent.parent  # 回到项目根目录
    log_dir = project_root / "logs"
    
    # 确保日志目录存在
    log_dir.mkdir(exist_ok=True)
    
    return log_dir

def setup_logger(
    module_name: Optional[str] = None,
    log_level: str = "INFO",
    log_to_file: bool = True,
    log_to_console: bool = True,
    log_file_name: Optional[str] = None,
    max_file_size: str = "10 MB",
    rotation_count: int = 5
) -> Logger:
    """
    配置日志系统，支持模块名作为第一个参数
    
    Args:
        module_name: 模块名称（如果提供，返回带有模块上下文的logger）
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_to_file: 是否输出到文件
        log_to_console: 是否输出到控制台
        log_file_name: 日志文件名，默认为salary_system.log
        max_file_size: 单个日志文件最大大小
        rotation_count: 保留的轮转文件数量
        
    Returns:
        配置好的logger实例
    """
    # 如果已经配置过，直接返回
    if _is_logger_configured(module_name):
        return logger.bind(module=module_name)
    
    # 读取策略与环境变量（在某些导入顺序下，_load_policy 可能尚未定义，做兜底）
    try:
        policy = _load_policy()  # type: ignore[name-defined]
    except NameError:
        policy = {}
    env_console = os.getenv("LOG_CONSOLE_LEVEL")
    env_file = os.getenv("LOG_FILE_LEVEL")

    # 将策略桥接为环境变量（供其他模块读取），仅当未显式设置时
    try:
        if policy:
            if os.getenv("LOG_SLOW_SQL_MS") is None and "slow_sql_ms" in policy:
                os.environ["LOG_SLOW_SQL_MS"] = str(policy.get("slow_sql_ms", 300))
            if os.getenv("LOG_SQL_DETAIL") is None and "sql_detail_enabled" in policy:
                os.environ["LOG_SQL_DETAIL"] = "1" if policy.get("sql_detail_enabled", False) else "0"
            # 同步 perf/sql sink 开关，便于非本模块判断
            if os.getenv("LOG_PERF_ENABLED") is None and "enable_perf" in policy:
                os.environ["LOG_PERF_ENABLED"] = "1" if policy.get("enable_perf", True) else "0"
            if os.getenv("LOG_SQL_ENABLED") is None and "enable_sql" in policy:
                os.environ["LOG_SQL_ENABLED"] = "1" if policy.get("enable_sql", False) else "0"
    except Exception:
        # 桥接失败不影响主流程
        pass
    # 级别（环境变量 > 策略 > 参数）
    console_level = (env_console or policy.get("console_level") or log_level).upper()
    file_level = (env_file or policy.get("file_level") or log_level).upper()

    # 配置控制台和文件日志
    _configure_console_logging(log_to_console, console_level)
    _configure_file_logging(log_to_file, file_level, log_file_name, max_file_size, rotation_count)

    # 记录初始化信息
    _log_initialization_info(file_level, log_to_console, log_to_file, log_file_name)
    
    # 返回带模块上下文的logger（如果需要）
    return logger.bind(module=module_name) if module_name else logger

def _is_logger_configured(module_name: Optional[str]) -> bool:
    """
    检查logger是否已配置
    
    Args:
        module_name: 模块名称
        
    Returns:
        bool: 是否已配置
    """
    return hasattr(logger, '_core') and logger._core.handlers and module_name

def _configure_console_logging(log_to_console: bool, level: str) -> None:
    """
    配置控制台日志
    
    Args:
        log_to_console: 是否输出到控制台
        level: 日志级别
    """
    if not log_to_console:
        return
    
    console_format = _get_console_format()
    
    # 修复Windows控制台编码问题：当包含emoji字符时，跳过控制台输出以避免GBK编码错误
    try:
        # 在CI/pytest环境下，避免与pytest capture冲突：直接禁用控制台sink
        running_pytest = ('pytest' in sys.modules) or bool(os.getenv("PYTEST_CURRENT_TEST"))
        if running_pytest:
            return
        if sys.platform == "win32":
            import io
            # 创建一个UTF-8编码的文本流包装器
            console_stream = io.TextIOWrapper(getattr(sys.stdout, 'buffer', sys.stdout), encoding='utf-8', errors='replace')
            target = console_stream
        else:
            target = sys.stdout

        logger.add(
            target,
            format=console_format,
            level=level,
            colorize=True
        )
    except Exception as e:
        # 如果控制台输出失败，仅记录到文件
        logger.add(
            sys.stderr,
            format="控制台日志配置失败，仅输出到文件: {message}",
            level="WARNING"
        )

def _configure_file_logging(log_to_file: bool, level: str, log_file_name: Optional[str], 
                           max_file_size: str, rotation_count: int) -> None:
    """
    配置文件日志
    
    Args:
        log_to_file: 是否输出到文件
        level: 日志级别
        log_file_name: 日志文件名
        max_file_size: 文件最大大小
        rotation_count: 轮转文件数量
    """
    if not log_to_file:
        return
    
    log_file_path = _get_log_file_path(log_file_name)
    file_format = _get_file_format()
    
    logger.add(
        str(log_file_path),
        format=file_format,
        level=level,
        rotation=max_file_size,
        retention=rotation_count,
        encoding="utf-8",
        backtrace=True,
        diagnose=True
    )

    # 额外 sink：perf/sql（按策略/环境变量）
    try:
        policy = _load_policy()  # type: ignore[name-defined]
    except NameError:
        policy = {}
    perf_enabled = str(os.getenv("LOG_PERF_ENABLED", "1" if policy.get("enable_perf", True) else "0")) == "1"
    sql_enabled = str(os.getenv("LOG_SQL_ENABLED", "1" if policy.get("enable_sql", False) else "0")) == "1"
    if perf_enabled:
        logger.add(
            str(get_log_directory() / "perf.log"),
            format=file_format,
            level="INFO",
            rotation=max_file_size,
            retention=rotation_count,
            encoding="utf-8"
        )
    if sql_enabled:
        logger.add(
            str(get_log_directory() / "sql.log"),
            format=file_format,
            level="DEBUG",
            rotation=max_file_size,
            retention=rotation_count,
            encoding="utf-8"
        )

    # DEBUG RingBuffer sink（按策略）
    try:
        policy = _load_policy()
        enable_ring = bool(policy.get("enable_debug_ring", False))
        if enable_ring:
            from src.utils.logging_utils import create_ring_sink
            logger.add(create_ring_sink(), level="DEBUG")
    except Exception:
        # 不影响主链路
        pass

def _get_console_format() -> str:
    """获取控制台日志格式"""
    return (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )

def _get_file_format() -> str:
    """获取文件日志格式"""
    return (
        "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )

def _get_log_file_path(log_file_name: Optional[str]) -> Path:
    """
    获取日志文件路径
    
    Args:
        log_file_name: 日志文件名
        
    Returns:
        Path: 日志文件完整路径
    """
    log_dir = get_log_directory()
    if log_file_name is None:
        log_file_name = "salary_system.log"
    return log_dir / log_file_name

def _log_initialization_info(level: str, log_to_console: bool, log_to_file: bool, 
                           log_file_name: Optional[str]) -> None:
    """
    记录日志系统初始化信息
    
    Args:
        level: 日志级别
        log_to_console: 是否输出到控制台
        log_to_file: 是否输出到文件
        log_file_name: 日志文件名
    """
    global _logger_initialized
    if _logger_initialized:
        return
    
    logger.info(f"日志系统初始化完成")
    logger.info(f"日志级别: {level}")
    logger.info(f"控制台输出: {log_to_console}")
    logger.info(f"文件输出: {log_to_file}")
    
    if log_to_file:
        log_file_path = _get_log_file_path(log_file_name)
        # 只显示相对路径，避免暴露系统路径
        relative_path = f"logs/{log_file_name or 'salary_system.log'}"
        logger.info(f"日志文件路径: {relative_path}")
    
    _logger_initialized = True

def get_module_logger(module_name: str) -> Logger:
    """
    获取模块专用的logger
    
    Args:
        module_name: 模块名称
        
    Returns:
        配置好的logger实例
    """
    # 为特定模块绑定上下文
    return logger.bind(module=module_name)

def log_function_call(func_name: str, **kwargs) -> None:
    """
    记录函数调用信息
    
    Args:
        func_name: 函数名称
        **kwargs: 函数参数
    """
    logger.debug(f"调用函数: {func_name}")
    for key, value in kwargs.items():
        logger.debug(f"  参数 {key}: {value}")

def log_file_operation(operation: str, file_path: Union[str, Path], **kwargs) -> None:
    """
    记录文件操作信息
    
    Args:
        operation: 操作类型 (read, write, delete, etc.)
        file_path: 文件路径
        **kwargs: 额外信息
    """
    logger.info(f"文件{operation}: {file_path}")
    for key, value in kwargs.items():
        logger.debug(f"  {key}: {value}")

def log_error_with_context(error: Exception, context: Optional[Dict[str, Any]] = None) -> None:
    """
    记录带上下文的错误信息
    
    Args:
        error: 异常对象
        context: 错误上下文信息
    """
    logger.error(f"发生错误: {type(error).__name__}: {str(error)}")
    if context:
        logger.error("错误上下文:")
        for key, value in context.items():
            logger.error(f"  {key}: {value}")
    
    # 记录异常栈
    logger.exception("异常详细信息:")

# 默认初始化
def init_default_logger():
    """
    使用默认配置初始化日志系统
    """
    # 从环境变量获取日志级别，默认为INFO
    log_level = os.getenv("SALARY_SYSTEM_LOG_LEVEL", "INFO")
    
    setup_logger(
        log_level=log_level,
        log_to_file=True,
        log_to_console=True  # 默认开启控制台输出，关闭可减少日志输出
    )

# 自动初始化（当模块被导入时）
if __name__ != "__main__":
    init_default_logger()

# 对外暴露的主要接口
__all__ = [
    "setup_logger",
    "get_module_logger", 
    "log_function_call",
    "log_file_operation",
    "log_error_with_context",
    "init_default_logger",
    "logger"
]

if __name__ == "__main__":
    # 测试代码
    print("测试日志配置模块...")
    
    # 初始化日志
    setup_logger(log_level="DEBUG")
    
    # 测试各种日志级别
    logger.debug("这是DEBUG级别日志")
    logger.info("这是INFO级别日志")
    logger.warning("这是WARNING级别日志")
    logger.error("这是ERROR级别日志")
    
    # 测试模块logger
    module_logger = get_module_logger("test_module")
    module_logger.info("这是模块专用日志")
    
    # 测试文件操作日志
    log_file_operation("read", "test.xlsx", size="1.2MB")
    
    # 测试错误日志
    try:
        1 / 0
    except Exception as e:
        log_error_with_context(e, {"操作": "除法计算", "被除数": 1, "除数": 0})
    
    print("日志配置模块测试完成！") 


# ------------------------------
# 策略加载
# ------------------------------
def _load_policy() -> Dict[str, Any]:
    """从 state/logging_policy.json 读取策略；失败返回空。"""
    try:
        # logs 目录的上级是项目根目录
        policy_path = get_log_directory().parent / "state" / "logging_policy.json"
        if not policy_path.exists():
            return {}
        import json
        with policy_path.open("r", encoding="utf-8") as f:
            data = json.load(f)
            return data or {}
    except Exception:
        return {}


def get_policy_path() -> Path:
    """返回策略文件的完整路径。"""
    return get_log_directory().parent / "state" / "logging_policy.json"


def refresh_logging_policy() -> bool:
    """重新加载策略并更新与策略相关的环境变量；用于手动热刷新。
    注意：现有 loguru sink 不会自动调整级别/开关，如需动态调整需进一步扩展。
    """
    try:
        policy = _load_policy()
        if not policy:
            return False
        if os.getenv("LOG_SLOW_SQL_MS") is None and "slow_sql_ms" in policy:
            os.environ["LOG_SLOW_SQL_MS"] = str(policy.get("slow_sql_ms", 300))
        if os.getenv("LOG_SQL_DETAIL") is None and "sql_detail_enabled" in policy:
            os.environ["LOG_SQL_DETAIL"] = "1" if policy.get("sql_detail_enabled", False) else "0"
        if os.getenv("LOG_PERF_ENABLED") is None and "enable_perf" in policy:
            os.environ["LOG_PERF_ENABLED"] = "1" if policy.get("enable_perf", True) else "0"
        if os.getenv("LOG_SQL_ENABLED") is None and "enable_sql" in policy:
            os.environ["LOG_SQL_ENABLED"] = "1" if policy.get("enable_sql", False) else "0"
        return True
    except Exception:
        return False


def _derive_levels_from_policy(policy: Dict[str, Any], fallback_level: str = "INFO") -> tuple[str, str]:
    """根据策略与环境变量推导控制台与文件级别。"""
    env_console = os.getenv("LOG_CONSOLE_LEVEL")
    env_file = os.getenv("LOG_FILE_LEVEL")
    console_level = (env_console or policy.get("console_level") or fallback_level).upper()
    file_level = (env_file or policy.get("file_level") or fallback_level).upper()
    return console_level, file_level


def rebuild_loggers_from_policy() -> bool:
    """移除现有 sinks，基于当前策略与环境变量重建日志输出（含 RingBuffer）。"""
    try:
        policy = _load_policy()
        # 先桥接到环境变量，确保下游读取一致
        try:
            if policy:
                if os.getenv("LOG_SLOW_SQL_MS") is None and "slow_sql_ms" in policy:
                    os.environ["LOG_SLOW_SQL_MS"] = str(policy.get("slow_sql_ms", 300))
                if os.getenv("LOG_SQL_DETAIL") is None and "sql_detail_enabled" in policy:
                    os.environ["LOG_SQL_DETAIL"] = "1" if policy.get("sql_detail_enabled", False) else "0"
                if os.getenv("LOG_PERF_ENABLED") is None and "enable_perf" in policy:
                    os.environ["LOG_PERF_ENABLED"] = "1" if policy.get("enable_perf", True) else "0"
                if os.getenv("LOG_SQL_ENABLED") is None and "enable_sql" in policy:
                    os.environ["LOG_SQL_ENABLED"] = "1" if policy.get("enable_sql", False) else "0"
        except Exception:
            pass

        console_level, file_level = _derive_levels_from_policy(policy)

        # 全量移除后重建 sinks
        try:
            logger.remove()
        except Exception:
            pass

        _configure_console_logging(True, console_level)
        _configure_file_logging(True, file_level, None, "10 MB", 5)
        _log_initialization_info(file_level, True, True, None)
        return True
    except Exception:
        return False


def refresh_and_rebuild_logging() -> bool:
    """刷新策略并重建日志输出。
    注意：现阶段仅重建 sink，不会影响已绑定的 logger 上下文。
    """
    try:
        _ = refresh_logging_policy()
        return rebuild_loggers_from_policy()
    except Exception:
        return False