"""
🔧 [立即修复] 线程安全的QTimer工具类
解决"QObject::startTimer: Timers can only be used with threads started with QThread"警告问题
"""

from PyQt5.QtCore import QTimer, QThread, QObject, pyqtSignal, QMetaObject, Qt
from PyQt5.QtWidgets import QApplication
from typing import Callable, Optional, Dict, Any
import logging
import threading
import time
import weakref

from src.utils.log_config import setup_logger

logger = setup_logger(__name__)


class TimerProxy(QObject):
    """🔧 [立即修复] 定时器代理对象，确保在主线程中执行"""

    timer_signal = pyqtSignal()

    def __init__(self, callback: Callable):
        super().__init__()
        self.callback = callback
        self.timer_signal.connect(self._execute_callback)

        # 确保代理对象在主线程中
        if not ThreadSafeTimer.is_main_thread():
            self.moveToThread(QApplication.instance().thread())

    def _execute_callback(self):
        """执行回调函数"""
        try:
            if self.callback:
                self.callback()
        except Exception as e:
            logger.error(f"🔧 [定时器回调] 执行失败: {e}", exc_info=True)


class ThreadSafeTimer:
    """🔧 [立即修复] 线程安全的定时器工具类"""

    # 全局定时器注册表，用于跟踪和管理所有定时器
    _timer_registry: Dict[int, weakref.ref] = {}
    _registry_lock = threading.Lock()

    @staticmethod
    def is_main_thread() -> bool:
        """🔧 [立即修复] 检查是否在主线程中"""
        try:
            app = QApplication.instance()
            if not app:
                logger.warning("🔧 [线程检查] QApplication实例不存在")
                return False

            current_thread = QThread.currentThread()
            main_thread = app.thread()

            is_main = current_thread == main_thread
            if not is_main:
                logger.warning(f"🔧 [线程检查] 当前线程不是主线程: {threading.current_thread().name}")

            return is_main
        except Exception as e:
            logger.error(f"🔧 [线程检查] 检查主线程失败: {e}")
            return False
    
    @staticmethod
    def create_timer(parent=None, single_shot: bool = True) -> Optional[QTimer]:
        """🔧 [立即修复] 线程安全地创建QTimer"""
        try:
            if not ThreadSafeTimer.is_main_thread():
                logger.warning("🔧 [线程安全] 尝试在非主线程创建QTimer，将移动到主线程")

                # 如果不在主线程，使用QMetaObject.invokeMethod在主线程中创建
                app = QApplication.instance()
                if app:
                    timer_container = {'timer': None}

                    def create_in_main_thread():
                        timer_container['timer'] = QTimer(parent)
                        timer_container['timer'].setSingleShot(single_shot)

                    # 非阻塞：将创建请求排队到主线程
                    QMetaObject.invokeMethod(app, create_in_main_thread, Qt.QueuedConnection)
                    # 异步创建：调用方应处理 None 情况
                    return timer_container['timer']
                else:
                    logger.error("🔧 [线程安全] QApplication实例不存在，无法创建定时器")
                    return None

            timer = QTimer(parent)
            timer.setSingleShot(single_shot)

            # 注册定时器到全局注册表
            with ThreadSafeTimer._registry_lock:
                timer_id = id(timer)
                ThreadSafeTimer._timer_registry[timer_id] = weakref.ref(timer)

            logger.debug(f"🔧 [线程安全] QTimer创建成功，ID: {id(timer)}")
            return timer

        except Exception as e:
            logger.error(f"🔧 [线程安全] 创建QTimer失败: {e}", exc_info=True)
            return None
    
    @staticmethod
    def safe_start_timer(timer: QTimer, interval_ms: int, callback: Callable = None) -> bool:
        """🔧 [立即修复] 线程安全地启动定时器"""
        try:
            if not timer:
                logger.warning("🔧 [线程安全] 定时器对象为空，无法启动")
                return False

            if not ThreadSafeTimer.is_main_thread():
                logger.warning("🔧 [线程安全] 尝试在非主线程启动QTimer，将移动到主线程执行")

                # 使用QMetaObject.invokeMethod在主线程中启动
                app = QApplication.instance()
                if app:
                    result_container = {'success': False}

                    def start_in_main_thread():
                        try:
                            if callback:
                                # 使用代理对象确保回调在主线程执行
                                proxy = TimerProxy(callback)
                                timer.timeout.connect(proxy.timer_signal.emit)

                            timer.start(interval_ms)
                            result_container['success'] = True
                            logger.debug(f"🔧 [线程安全] 定时器已在主线程启动，间隔: {interval_ms}ms")
                        except Exception as e:
                            logger.error(f"🔧 [线程安全] 在主线程启动定时器失败: {e}")

                    # 使用非阻塞的排队连接，避免调用线程阻塞等待主线程
                    QMetaObject.invokeMethod(app, start_in_main_thread, Qt.QueuedConnection)
                    # 异步启动，乐观返回True，实际失败会记录日志
                    return True
                else:
                    logger.error("🔧 [线程安全] QApplication实例不存在，无法启动定时器")
                    return False

            if callback:
                timer.timeout.connect(callback)

            timer.start(interval_ms)
            logger.debug(f"🔧 [线程安全] 定时器已安全启动，间隔: {interval_ms}ms")
            return True

        except Exception as e:
            logger.error(f"🔧 [线程安全] 启动定时器失败: {e}", exc_info=True)
            return False
    
    @staticmethod
    def safe_single_shot(interval_ms: int, callback: Callable, parent=None) -> bool:
        """🔧 [P0-CRITICAL修复] 完全线程安全的单次定时器"""
        try:
            if not ThreadSafeTimer.is_main_thread():
                logger.warning("🔧 [P0-CRITICAL] 检测到非主线程调用，强制使用主线程执行")

                app = QApplication.instance()
                if not app:
                    logger.error("🔧 [P0-CRITICAL] QApplication实例不存在，无法创建定时器")
                    return False

                # 🔧 [P0-CRITICAL] 完全禁止在非主线程创建QTimer
                # 使用QMetaObject.invokeMethod确保在主线程执行
                success_flag = [False]  # 使用列表避免闭包问题
                
                def create_timer_in_main_thread():
                    """必须在主线程执行的定时器创建函数"""
                    try:
                        # 🔧 [P0-CRITICAL] 再次验证是否在主线程
                        if not ThreadSafeTimer.is_main_thread():
                            logger.error("🔧 [P0-CRITICAL] invokeMethod调用后仍不在主线程")
                            return
                        
                        # 🔧 [P0-CRITICAL] 安全回调包装器
                        def safe_callback():
                            try:
                                callback()
                            except Exception as cb_error:
                                logger.error(f"🔧 [P0-CRITICAL] 定时器回调执行异常: {cb_error}", exc_info=True)
                        
                        # 🔧 [P0-CRITICAL] 在主线程直接创建QTimer
                        QTimer.singleShot(interval_ms, safe_callback)
                        success_flag[0] = True
                        logger.debug(f"🔧 [P0-CRITICAL] 定时器已在主线程安全创建: {interval_ms}ms")
                        
                    except Exception as create_error:
                        logger.error(f"🔧 [P0-CRITICAL] 主线程定时器创建失败: {create_error}", exc_info=True)

                # 🔧 [P0-CRITICAL] 使用同步调用确保操作完成
                try:
                    # 非阻塞地把创建请求投递到主线程
                    QMetaObject.invokeMethod(
                        app,
                        create_timer_in_main_thread,
                        Qt.QueuedConnection
                    )
                except Exception as invoke_error:
                    logger.error(f"🔧 [P0-CRITICAL] invokeMethod执行失败: {invoke_error}")
                    return False
                
                # 异步创建，立即返回True，失败将被回调记录
                return True

            # 🔧 [P0-CRITICAL] 主线程直接创建，添加异常保护
            def safe_callback():
                try:
                    callback()
                except Exception as cb_error:
                    logger.error(f"🔧 [P0-CRITICAL] 主线程定时器回调异常: {cb_error}", exc_info=True)
            
            QTimer.singleShot(interval_ms, safe_callback)
            logger.debug(f"🔧 [P0-CRITICAL] 定时器已在主线程直接创建: {interval_ms}ms")
            return True

        except Exception as e:
            logger.error(f"🔧 [P0-CRITICAL] 定时器创建完全失败: {e}", exc_info=True)
            return False

    @staticmethod
    def cleanup_timers():
        """🔧 [立即修复] 清理已失效的定时器引用"""
        with ThreadSafeTimer._registry_lock:
            expired_ids = []
            for timer_id, timer_ref in ThreadSafeTimer._timer_registry.items():
                if timer_ref() is None:  # 弱引用已失效
                    expired_ids.append(timer_id)

            for timer_id in expired_ids:
                del ThreadSafeTimer._timer_registry[timer_id]

            if expired_ids:
                logger.debug(f"🔧 [清理] 清理已失效的定时器引用: {len(expired_ids)}个")

    @staticmethod
    def get_timer_count() -> int:
        """🔧 [立即修复] 获取当前活跃定时器数量"""
        ThreadSafeTimer.cleanup_timers()
        with ThreadSafeTimer._registry_lock:
            return len(ThreadSafeTimer._timer_registry)


def safe_timer_start(timer: QTimer, interval_ms: int) -> bool:
    """🔧 [立即修复] 便捷函数：线程安全地启动定时器"""
    return ThreadSafeTimer.safe_start_timer(timer, interval_ms)


def safe_single_shot(interval_ms: int, callback: Callable) -> bool:
    """🔧 [立即修复] 便捷函数：线程安全的单次定时器"""
    return ThreadSafeTimer.safe_single_shot(interval_ms, callback)


def create_safe_timer(parent=None, single_shot: bool = True) -> Optional[QTimer]:
    """🔧 [立即修复] 便捷函数：线程安全地创建定时器"""
    return ThreadSafeTimer.create_timer(parent, single_shot)


def force_main_thread_timer(interval_ms: int, callback: Callable, single_shot: bool = True) -> bool:
    """
    🔧 [立即修复] 强制在主线程中创建和启动定时器

    这是解决"QObject::startTimer: Timers can only be used with threads started with QThread"
    警告的最强力方法
    """
    try:
        app = QApplication.instance()
        if not app:
            logger.error("🔧 [强制主线程] QApplication实例不存在")
            return False

        result_container = {'success': False}

        def create_and_start_in_main():
            try:
                timer = QTimer()
                timer.setSingleShot(single_shot)

                # 创建代理对象确保回调在主线程执行
                proxy = TimerProxy(callback)
                timer.timeout.connect(proxy.timer_signal.emit)

                timer.start(interval_ms)
                result_container['success'] = True
                logger.debug(f"🔧 [强制主线程] 定时器已在主线程创建并启动，间隔: {interval_ms}ms")
            except Exception as e:
                logger.error(f"🔧 [强制主线程] 创建定时器失败: {e}")

        # 使用非阻塞调用，避免潜在的主线程等待/死锁
        QMetaObject.invokeMethod(app, create_and_start_in_main, Qt.QueuedConnection)
        # 异步启动，乐观返回True
        return True

    except Exception as e:
        logger.error(f"🔧 [强制主线程] 强制主线程定时器失败: {e}", exc_info=True)
        return False