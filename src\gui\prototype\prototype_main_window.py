"""
PyQt5重构高保真原型 - 原型主窗口

实现高保真HTML原型的完整界面重构，包括：
- Header + Workspace(Left Panel + Main Content) + Footer 三段式布局
- Material Design色彩体系和现代化视觉效果  
- 响应式设计支持1024px-1920px+屏幕尺寸
- 4级树形导航、表格行展开、双击编辑、查询筛选等交互
- 隐藏式菜单栏功能

主要功能:
1. 三段式响应式布局架构
2. Material Design风格Header(蓝绿渐变)
3. 智能左侧导航面板(集成SmartTreeExpansion和SmartSearchDebounce)
4. 主工作区域(控制面板+数据表格)
5. 底部状态栏和信息显示
6. 隐藏式菜单栏系统
"""

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QFrame, QLabel, QPushButton, QLineEdit, QTreeWidget, QTableWidget,
    QTabWidget, QStatusBar, QMenuBar, QToolBar, QHeaderView,
    QTreeWidgetItem, QTableWidgetItem, QSizePolicy, QDialog, QApplication,
    QStackedWidget, QSpacerItem, QAction, QMenu, QMessageBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSize, QRect, pyqtSlot, QThreadPool, QRunnable, QObject, QPropertyAnimation, QEasingCurve, QEvent
from src.utils.thread_safe_timer import safe_single_shot
from PyQt5.QtGui import QFont, QPalette, QColor, QPainter, QLinearGradient, QBrush
from typing import Optional, Dict, Any, List
import logging
import time
import os
from pathlib import Path
import pandas as pd
import sys
import traceback
import re
from datetime import datetime

from src.utils.log_config import setup_logger
from .managers.responsive_layout_manager import ResponsiveLayoutManager
from .managers.communication_manager import ComponentCommunicationManager
from .widgets.enhanced_navigation_panel import EnhancedNavigationPanel
from .widgets.virtualized_expandable_table import (
    VirtualizedExpandableTable, create_sample_data
)
from .widgets.material_tab_widget import MaterialTabWidget
from src.gui.widgets.pagination_widget import PaginationWidget

# 🔧 [P1-移除旧架构] 完全移除旧架构导入

# 导入数据导入相关模块
from src.modules.data_import.excel_importer import ExcelImporter
from src.modules.data_import.data_validator import DataValidator
from src.gui.main_dialogs import DataImportDialog
from src.core.application_service import ApplicationService
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

# 导入项目内部模块
from src.utils.log_config import setup_logger
from src.modules.system_config import ConfigManager
from src.gui.widgets.pagination_cache_manager import PaginationCacheManager

# 表头管理器导入
from src.gui.table_header_manager import get_global_header_manager, TableHeaderManager, reset_global_header_manager

# 排序状态管理器导入
from src.core.table_sort_state_manager import TableSortStateManager

# 🔧 [P2-3] 错误处理管理器导入
from src.core.error_handler_manager import get_error_handler, ErrorCategory, ErrorSeverity

# 🔧 [P1修复] 分页事件优化器导入 - 修复EventType未定义问题
from src.core.pagination_event_optimizer import EventType

import os
import sqlite3

from src.modules.system_config.user_preferences import UserPreferences

# 初始化日志记录器
logger = setup_logger("gui.prototype.prototype_main_window")


class WorkerSignals(QObject):
    """定义Worker线程可以发出的信号"""
    finished = pyqtSignal()
    error = pyqtSignal(tuple)
    result = pyqtSignal(object)
    window_resized = pyqtSignal(QSize)


class Worker(QRunnable):
    """通用工作线程，用于执行耗时操作而不阻塞UI线程"""
    def __init__(self, fn, *args, **kwargs):
        super(Worker, self).__init__()
        self.fn = fn
        self.args = args
        self.kwargs = kwargs
        self.signals = WorkerSignals()

    @pyqtSlot()
    def run(self):
        """运行工作线程中的方法"""
        try:
            result = self.fn(*self.args, **self.kwargs)
            self.signals.result.emit(result)
        except Exception as e:
            logger.error(f"Worker线程执行失败: {e}", exc_info=True)
            self.signals.error.emit((type(e), e, e.__traceback__))
        finally:
            self.signals.finished.emit()


class PaginationWorker(QRunnable):
    """分页数据加载工作线程"""
    def __init__(self, table_manager, table_name, page=1, page_size=50, apply_mapping_func=None, sort_state_manager=None):
        super(PaginationWorker, self).__init__()
        self.table_manager = table_manager
        self.table_name = table_name
        self.page = page
        self.page_size = page_size
        self.apply_mapping_func = apply_mapping_func
        self.sort_state_manager = sort_state_manager
        self.signals = WorkerSignals()
        self.logger = setup_logger(f"PaginationWorker.{table_name}")

    @pyqtSlot()
    def run(self):
        """加载分页数据 - 增强版本，保证数据一致性"""
        try:
            self.logger.info(f"开始加载表 {self.table_name} 第{self.page}页数据，每页{self.page_size}条")

            # 验证表管理器可用性
            if not self.table_manager:
                raise Exception("表管理器不可用")
                
            # 验证表是否存在
            if not hasattr(self.table_manager, 'table_exists') or not self.table_manager.table_exists(self.table_name):
                raise Exception(f"表 {self.table_name} 不存在")

            # 获取排序状态并使用支持排序的查询方法
            sort_columns = []
            if self.sort_state_manager:
                try:
                    sort_state = self.sort_state_manager.restore_sort_state(self.table_name)
                    if sort_state and hasattr(sort_state, 'sort_columns') and sort_state.sort_columns:
                        sort_columns = sort_state.sort_columns
                        self.logger.info(f"应用排序状态: {len(sort_columns)} 列")
                except Exception as e:
                    self.logger.warning(f"获取排序状态失败: {e}")

            # 使用支持排序的分页查询方法
            if hasattr(self.table_manager, 'get_dataframe_paginated_with_sort'):
                df, total_records = self.table_manager.get_dataframe_paginated_with_sort(
                    self.table_name, self.page, self.page_size, sort_columns
                )
                self.logger.info(f"使用排序查询: {len(sort_columns)} 个排序列")
            else:
                # 🔧 [P1-移除旧架构] 移除降级逻辑，直接使用普通分页查询
                df, total_records = self.table_manager.get_dataframe_paginated(
                    self.table_name, self.page, self.page_size
                )
                self.logger.info("🔧 [P1-移除旧架构] 使用普通分页查询（不支持排序）")
            
            # 数据验证
            if df is None:
                self.logger.error(f"表 {self.table_name} 返回的DataFrame为None")
                raise Exception("数据加载失败：返回的数据为空")
                
            if df.empty:
                self.logger.warning(f"表 {self.table_name} 第{self.page}页数据为空")
                result = {
                    'success': True,  # 数据为空也是正常状态
                    'data': df,
                    'total_records': total_records,
                    'current_page': self.page,
                    'page_size': self.page_size,
                    'table_name': self.table_name,
                    'loaded_fields': len(df.columns),
                    'is_empty': True
                }
                self.signals.result.emit(result)
                return
            
            # 记录原始数据信息
            original_columns = list(df.columns)
            original_count = len(df)
            self.logger.info(f"原始数据: {original_count}行, {len(original_columns)}列")
            self.logger.debug(f"原始列名: {original_columns[:10]}...")  # 只显示前10个

            # 应用字段映射（如果有）
            mapped_df = df
            if self.apply_mapping_func:
                try:
                    self.logger.info("开始应用字段映射")
                    mapped_df = self.apply_mapping_func(df.copy(), self.table_name)
                    
                    # 验证映射后的数据完整性
                    if mapped_df is None or mapped_df.empty:
                        self.logger.debug("🎯 [新架构] PaginationWorker - 字段映射后数据为空，使用原始数据")
                        self.logger.error("PaginationWorker - 字段映射后数据为空，使用原始数据")
                        mapped_df = df
                    else:
                        self.logger.debug(f"🎯 [新架构] PaginationWorker - 字段映射成功: {len(df.columns)} -> {len(mapped_df.columns)}列")
                        self.logger.info(f"PaginationWorker - 字段映射成功: {len(df.columns)} -> {len(mapped_df.columns)}列")
                    
                    # 验证映射结果
                    if mapped_df is None:
                        self.logger.warning("字段映射返回空结果，使用原始数据")
                        mapped_df = df
                    elif mapped_df.empty and not df.empty:
                        self.logger.warning("字段映射导致数据为空，使用原始数据")
                        mapped_df = df
                    elif len(mapped_df) != len(df):
                        self.logger.warning(f"字段映射导致数据行数变化: {len(df)} -> {len(mapped_df)}，使用原始数据")
                        mapped_df = df
                    else:
                        mapped_columns = list(mapped_df.columns)
                        self.logger.info(f"字段映射成功: {len(mapped_columns)}列")
                        self.logger.debug(f"映射后列名: {mapped_columns[:10]}...")  # 只显示前10个
                        
                except Exception as e:
                    self.logger.error(f"字段映射失败: {e}，使用原始数据")
                    mapped_df = df

            # 🧹 [代码清理] 数据已通过统一格式化管理器处理，移除冗余调用
            # 数据在导入时已经过统一格式化，此处无需额外处理
            
            # 数据完整性检查
            empty_rows = mapped_df.isnull().all(axis=1).sum()
            if empty_rows > 0:
                self.logger.warning(f"发现 {empty_rows} 行完全为空的数据")
            
            # 检查关键字段是否有数据
            key_fields = ['employee_name', 'employee_id', '姓名', '人员代码', '工号']
            has_key_data = False
            for field in key_fields:
                if field in mapped_df.columns and not mapped_df[field].isnull().all():
                    has_key_data = True
                    break
            
            if not has_key_data:
                self.logger.warning(f"页面数据缺少关键字段信息，可能导致显示问题")
            
            self.logger.info(f"最终数据: {len(mapped_df)}行, {len(mapped_df.columns)}列, 总记录数: {total_records}")

            result = {
                'success': True,
                'data': mapped_df,
                'total_records': total_records,
                'current_page': self.page,
                'page_size': self.page_size,
                'table_name': self.table_name,
                'loaded_fields': len(mapped_df.columns),
                'original_fields': len(original_columns),
                'field_loading_strategy': 'all_fields_with_validation',  # 新策略标识
                'mapping_applied': self.apply_mapping_func is not None
            }
            
            self.signals.result.emit(result)
            
        except Exception as e:
            self.logger.error(f"加载分页数据失败: {e}", exc_info=True)
            result = {
                'success': False,
                'error': str(e),
                'table_name': self.table_name,
                'page': self.page,
                'page_size': self.page_size,
                'error_type': 'pagination_load_error'
            }
            self.signals.result.emit(result)
        finally:
            self.signals.finished.emit()


class MaterialHeaderWidget(QWidget):
    """
    Material Design风格Header组件
    
    实现蓝绿渐变背景、系统标题、用户信息等功能。
    """
    
    # 信号定义
    settings_clicked = pyqtSignal()  # 设置按钮点击信号
    global_sort_toggled = pyqtSignal(bool)  # 🔧 [全局排序] 全局排序开关信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(60)  # 默认高度，响应式时会调整
        self.settings_btn = None  # 设置按钮引用
        self.setup_ui()
        self.logger = setup_logger(__name__ + ".MaterialHeaderWidget")
    
    def setup_ui(self):
        """
        设置Header界面
        """
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(20)
        
        # 系统标题区域
        title_label = QLabel("月度工资异动处理系统")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: white; font-weight: bold;")
        
        # 版本信息
        version_label = QLabel("v2.0 高保真原型")
        version_label.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 12px;")
        
        # 用户信息区域
        user_info_label = QLabel("管理员")
        user_info_label.setStyleSheet("color: white; font-size: 14px;")
        
        # 🔧 [全局排序] 全局排序开关按钮
        self.global_sort_btn = QPushButton("🔀")
        self.global_sort_btn.setFixedSize(40, 40)
        self.global_sort_btn.setToolTip("切换全局排序模式")
        self.global_sort_btn.setCheckable(True)
        self.global_sort_btn.setChecked(True)  # 默认启用
        self.global_sort_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                border: none;
                border-radius: 20px;
                color: white;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
            QPushButton:checked {
                background-color: rgba(76, 175, 80, 0.8);
                color: white;
            }
        """)
        
        # 设置按钮
        self.settings_btn = QPushButton("⚙️")
        self.settings_btn.setFixedSize(40, 40)
        self.settings_btn.setToolTip("点击显示/隐藏菜单栏")
        self.settings_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                border: none;
                border-radius: 20px;
                color: white;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """)
        
        # 连接设置按钮点击事件
        self.settings_btn.clicked.connect(self.settings_clicked.emit)
        
        # 🔧 [全局排序] 连接全局排序按钮
        self.global_sort_btn.toggled.connect(self.global_sort_toggled.emit)
        
        # 布局安排
        layout.addWidget(title_label)
        layout.addWidget(version_label)
        layout.addStretch()
        layout.addWidget(user_info_label)
        layout.addWidget(self.global_sort_btn)  # 🔧 [全局排序] 添加全局排序按钮
        layout.addWidget(self.settings_btn)
    
    def update_settings_button_state(self, menu_visible: bool):
        """
        更新设置按钮状态
        
        Args:
            menu_visible: 菜单栏是否可见
        """
        if self.settings_btn:
            if menu_visible:
                self.settings_btn.setToolTip("点击隐藏菜单栏")
                self.settings_btn.setStyleSheet("""
                    QPushButton {
                        background-color: rgba(255, 255, 255, 0.4);
                        border: none;
                        border-radius: 20px;
                        color: white;
                        font-size: 16px;
                    }
                    QPushButton:hover {
                        background-color: rgba(255, 255, 255, 0.5);
                    }
                    QPushButton:pressed {
                        background-color: rgba(255, 255, 255, 0.3);
                    }
                """)
            else:
                self.settings_btn.setToolTip("点击显示菜单栏")
                self.settings_btn.setStyleSheet("""
                    QPushButton {
                        background-color: rgba(255, 255, 255, 0.2);
                        border: none;
                        border-radius: 20px;
                        color: white;
                        font-size: 16px;
                    }
                    QPushButton:hover {
                        background-color: rgba(255, 255, 255, 0.3);
                    }
                    QPushButton:pressed {
                        background-color: rgba(255, 255, 255, 0.1);
                    }
                """)
    
    def paintEvent(self, event):
        """
        绘制蓝绿渐变背景
        """
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 创建蓝绿渐变
        gradient = QLinearGradient(0, 0, self.width(), 0)
        gradient.setColorAt(0, QColor(33, 150, 243))    # Material Blue
        gradient.setColorAt(1, QColor(0, 150, 136))     # Material Teal
        
        painter.fillRect(self.rect(), QBrush(gradient))
        painter.end()
    
    def handle_responsive_change(self, breakpoint: str, config: Dict[str, Any]):
        """
        处理响应式变化
        
        Args:
            breakpoint: 断点名称
            config: 布局配置
        """
        header_height = config.get('header_height', 60)
        self.setFixedHeight(header_height)
        
        # 字体缩放
        font_scale = config.get('font_scale', 1.0)
        for label in self.findChildren(QLabel):
            font = label.font()
            if "16" in label.styleSheet():  # 标题
                font.setPointSize(int(16 * font_scale))
            elif "12" in label.styleSheet():  # 版本
                font.setPointSize(int(12 * font_scale))
            elif "14" in label.styleSheet():  # 用户信息
                font.setPointSize(int(14 * font_scale))
            label.setFont(font)


class MainWorkspaceArea(QWidget):
    """
    主工作区域
    
    包含控制面板、标签导航、数据表格等核心功能区域。
    """
    
    # 添加信号定义
    import_requested = pyqtSignal()  # 导入数据请求信号
    export_requested = pyqtSignal()  # 导出报告请求信号
    add_record_requested = pyqtSignal()  # 添加记录请求信号
    refresh_requested = pyqtSignal()  # 刷新数据请求信号
    brightness_fix_requested = pyqtSignal()  # 🔧 [P0-修复] 亮度修复请求信号
    
    def __init__(self, table_manager: DynamicTableManager, config_sync_manager=None, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__ + ".MainWorkspaceArea")

        # 管理器
        self.table_manager = table_manager
        self.config_sync_manager = config_sync_manager
        
        # 初始化数据导入器
        self.excel_importer = ExcelImporter()
        self.data_validator = DataValidator()
        self.app_service = ApplicationService()
        
        # 数据状态
        self.current_data = []
        self.current_headers = []
        self.has_real_data = False
        
        # 字段映射相关
        self._current_field_mapping = {}  # 保存当前的字段映射
        self._original_headers = []  # 保存原始表头
        self._chinese_headers = []   # 保存中文表头
        
        # 分页相关属性
        self.pagination_widget = None  # 分页组件
        # 🔧 [P0-修复] current_table_name统一由主窗口管理，此处不重复定义
        self.use_pagination = True     # 是否启用分页模式
        
        # 🔧 [全局排序] 全局排序功能开关和状态
        self.global_sort_enabled = True  # 默认启用全局排序
        self.current_sort_columns = []   # 当前排序列
        self.field_mapping_cache = {}    # 字段映射缓存
        
        # 🔧 [P0-修复] 初始化数据路径相关属性
        self.current_data_path = ""      # 当前数据路径
        self.current_data_source = ""    # 当前数据源描述
        
        self.setup_ui()
        self._connect_control_panel_signals()  # 添加信号连接
    
    def _connect_control_panel_signals(self):
        """连接控制面板按钮信号"""
        try:
            # 连接主控制按钮
            self.btn_add.clicked.connect(self._on_add_record)
            self.btn_import.clicked.connect(self._on_import_data)
            self.btn_export.clicked.connect(self._on_export_report)
            self.btn_refresh.clicked.connect(self._on_refresh_data)
            
            # 连接搜索按钮
            self.btn_search.clicked.connect(self._on_search)
            
            self.logger.info("控制面板按钮信号连接完成")
            
        except Exception as e:
            self.logger.error(f"连接控制面板信号失败: {e}")
    
    def _apply_input_style(self, input_widget):
        """应用Material Design输入框样式"""
        try:
            self.logger.debug("🔧 开始应用输入框样式...")
            
            # 尝试获取样式管理器
            try:
                from src.gui.style_manager import StyleManager
                style_manager = StyleManager.get_instance()
                
                # 应用输入框样式
                if style_manager.apply_component_style(input_widget, "input"):
                    self.logger.debug("✅ 输入框样式应用成功")
                else:
                    self.logger.warning("⚠️ 输入框样式应用失败")
                
                # 设置输入框属性以配合Material Design样式
                if hasattr(input_widget, 'setMinimumWidth'):
                    input_widget.setMinimumWidth(200)
                
                if hasattr(input_widget, 'setMinimumHeight'):
                    input_widget.setMinimumHeight(32)
                    
            except Exception as style_error:
                self.logger.warning(f"⚠️ StyleManager未初始化或不可用: {style_error}")
                # 应用备用样式
                input_widget.setStyleSheet("""
                    QLineEdit {
                        border: 2px solid #e9ecef;
                        border-radius: 6px;
                        padding: 8px 12px;
                        font-size: 14px;
                        background-color: white;
                        min-width: 200px;
                    }
                    QLineEdit:focus {
                        border-color: #1976D2;
                    }
                """)
            
        except Exception as e:
            self.logger.error(f"❌ 输入框样式应用失败: {e}")
    
    def _on_add_record(self):
        """处理添加记录按钮点击"""
        try:
            self.logger.info("添加记录功能被触发")
            self.add_record_requested.emit()
            # TODO: 实现添加记录对话框
            self._show_info_message("添加记录功能", "添加新的工资异动记录功能正在开发中...")
        except Exception as e:
            self.logger.error(f"添加记录处理失败: {e}")
    
    def _on_import_data(self):
        """处理导入数据按钮点击，仅发出信号，由主窗口处理。"""
        self.logger.info("数据导入功能被触发，发出 import_requested 信号。")
        self.import_requested.emit()
    
    def _on_export_report(self):
        """处理导出报告按钮点击"""
        try:
            self.logger.info("导出报告功能被触发")
            self.export_requested.emit()
            
            if not self.has_real_data:
                self._show_warning_message("导出提醒", "当前为示例数据，建议先导入真实数据后再导出报告")
                return
            
            # TODO: 实现报告导出功能
            self._show_info_message("导出报告功能", "报告导出功能正在开发中...")
            
        except Exception as e:
            self.logger.error(f"导出报告处理失败: {e}")
            self._show_error_message("导出失败", f"导出报告失败: {e}")
    
    def _on_refresh_data(self):
        """处理刷新数据请求，发出信号给主窗口处理"""
        try:
            self.logger.info("刷新数据功能被触发，发出 refresh_requested 信号")
            self.refresh_requested.emit()
        except Exception as e:
            self.logger.error(f"刷新数据处理失败: {e}")
    
    def _on_search(self):
        """处理搜索按钮点击"""
        try:
            search_text = self.search_input.text().strip()
            if not search_text:
                self._show_warning_message("搜索提醒", "请输入搜索关键词")
                return
            
            self.logger.info(f"搜索功能被触发，关键词: {search_text}")
            # TODO: 实现搜索功能
            self._show_info_message("搜索功能", f"搜索功能正在开发中...\n搜索关键词: {search_text}")
            
        except Exception as e:
            self.logger.error(f"搜索处理失败: {e}")
    
    def _apply_field_mapping_to_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """🔧 [P0-修复] 字段映射应用 - 使用新架构的ConfigSyncManager"""
        try:
            self.logger.debug(f"🎯 [新架构] MainWorkspaceArea开始应用字段映射: {table_name}")
            
            # 🎯 [新架构] 确保ConfigSyncManager可用
            if not hasattr(self, 'config_sync_manager') or not self.config_sync_manager:
                self.logger.debug("ConfigSyncManager不可用，跳过字段映射")
                return df
            
            # 使用ConfigSyncManager获取字段映射并手动应用
            try:
                field_mapping = self.config_sync_manager.get_field_mapping_for_table(table_name)
                if field_mapping:
                    mapped_df = df.copy()
                    rename_dict = {}
                    for db_field, display_name in field_mapping.items():
                        if db_field in mapped_df.columns:
                            rename_dict[db_field] = display_name
                    
                    if rename_dict:
                        mapped_df = mapped_df.rename(columns=rename_dict)
                        self.logger.debug(f"🎯 [新架构] 成功映射 {len(rename_dict)} 个字段")
                        return mapped_df
                    else:
                        self.logger.debug("没有需要映射的字段")
                        return df
                else:
                    self.logger.debug(f"未找到表 {table_name} 的字段映射配置")
                    return df
            except Exception as e:
                self.logger.warning(f"获取字段映射失败: {e}")
                return df
                
        except Exception as e:
            self.logger.error(f"字段映射应用失败: {e}")
            return df
    
    def set_data(self, df=None, preserve_headers: bool = False, table_name: str = "", current_table_name: str = ""):
        """公共接口：设置表格数据（支持分页模式）。"""
        try:
            # 🔧 [P0修复] 改进数据类型检查 - 防止短时间内重复设置相同数据
            is_valid_data = False
            if df is not None:
                # 🔧 [P0-CRITICAL修复] 安全的数据类型检查
                if hasattr(df, 'empty'):  # DataFrame类型
                    is_valid_data = not df.empty
                elif isinstance(df, list):  # List类型
                    is_valid_data = len(df) > 0
                elif isinstance(df, dict):  # Dict类型
                    is_valid_data = len(df) > 0
                else:
                    is_valid_data = True  # 其他类型默认有效
            
            if is_valid_data:
                import hashlib
                data_hash = hashlib.md5(str(df.values.tobytes() if hasattr(df, 'values') else str(df)).encode()).hexdigest()
                current_time = time.time()

                if hasattr(self, '_last_data_hash') and hasattr(self, '_last_data_time'):
                    if (self._last_data_hash == data_hash and
                        current_time - self._last_data_time < 0.5):  # 500ms内重复数据
                        self.logger.debug(f"🔧 [P3-性能优化] 跳过重复数据设置，数据哈希: {data_hash[:8]}")
                        return

                self._last_data_hash = data_hash
                self._last_data_time = current_time

            # 🔧 [P2-优化] 简化日志记录
            self.logger.debug("开始设置表格数据")
            
            # 🔧 [P2优化] 智能递归调用防护机制 - 减少误判
            if hasattr(self, '_setting_data_lock') and self._setting_data_lock:
                # 分析调用来源，区分真正的递归和合法的连续调用
                if self._is_legitimate_data_update():
                    self.logger.debug("🔧 [P2优化] 检测到合法的数据更新连续调用，允许执行")
                else:
                    self.logger.warning("🔧 [P2优化] 检测到真正的递归设置数据，跳过以防止表头重影")
                    return False

            # 设置递归防护锁
            self._setting_data_lock = True
            self._data_set_start_time = time.time()  # 记录开始时间用于超时检测

            # 🔧 [P2优化] 轻量级堆栈检查，减少性能影响
            import traceback
            stack_depth = len(traceback.extract_stack())
            if stack_depth > 50:  # 异常深度检测
                self.logger.warning(f"🔧 [P2优化] 调用堆栈深度异常: {stack_depth}")

            self.logger.debug(f"🔧 [P2优化] 开始数据设置，堆栈深度: {stack_depth}")

            # 处理不同数据类型
            if df is None:
                try:
                    from src.utils.logging_utils import aggregate_register, aggregate_should_emit
                    aggregate_register('empty-data')
                    _emit, _cnt = aggregate_should_emit('empty-data', 2000)
                    if _emit:
                        self.logger.info(f"空数据输入发生 {_cnt} 次（2s 窗口），将显示空表提示")
                    else:
                        self.logger.debug("空数据输入，将显示提示（已聚合）")
                except Exception:
                    self.logger.debug("空数据输入，将显示提示")
                self._show_empty_table_with_prompt()
                self.has_real_data = False
                # 重置分页状态
                if self.pagination_widget:
                    # 🔧 [幂等保护] 分页重置短窗去重
                    if not hasattr(self, '_page_reset_last_ts'):
                        self._page_reset_last_ts = 0.0
                    import time as _t
                    if _t.time() - self._page_reset_last_ts > 1.0:
                        self.pagination_widget.reset()
                        self._page_reset_last_ts = _t.time()
                return
            
            # 检查是否为空的DataFrame
            if hasattr(df, 'empty') and df.empty:
                try:
                    from src.utils.logging_utils import aggregate_register, aggregate_should_emit
                    aggregate_register('empty-data')
                    _emit, _cnt = aggregate_should_emit('empty-data', 2000)
                    if _emit:
                        self.logger.info(f"空数据输入发生 {_cnt} 次（2s 窗口），将显示空表提示")
                    else:
                        self.logger.debug("空数据输入，将显示提示（已聚合）")
                except Exception:
                    self.logger.debug("空数据输入，将显示提示")
                self._show_empty_table_with_prompt()
                self.has_real_data = False
                # 重置分页状态
                if self.pagination_widget:
                    if not hasattr(self, '_page_reset_last_ts'):
                        self._page_reset_last_ts = 0.0
                    import time as _t
                    if _t.time() - self._page_reset_last_ts > 1.0:
                        self.pagination_widget.reset()
                        self._page_reset_last_ts = _t.time()
                return
            
            # 检查是否为空的list
            if isinstance(df, list) and len(df) == 0:
                try:
                    from src.utils.logging_utils import aggregate_register, aggregate_should_emit
                    aggregate_register('empty-data')
                    _emit, _cnt = aggregate_should_emit('empty-data', 2000)
                    if _emit:
                        self.logger.info(f"空数据输入发生 {_cnt} 次（2s 窗口），将显示空表提示")
                    else:
                        self.logger.debug("空数据输入，将显示提示（已聚合）")
                except Exception:
                    self.logger.debug("空数据输入，将显示提示")
                self._show_empty_table_with_prompt()
                self.has_real_data = False
                # 重置分页状态
                if self.pagination_widget:
                    if not hasattr(self, '_page_reset_last_ts'):
                        self._page_reset_last_ts = 0.0
                    import time as _t
                    if _t.time() - self._page_reset_last_ts > 1.0:
                        self.pagination_widget.reset()
                        self._page_reset_last_ts = _t.time()
                return
            
            # 统一数据格式处理
            if isinstance(df, list):
                # 如果是list，转换为DataFrame
                if len(df) > 0:
                    df_converted = pd.DataFrame(df)
                    self.logger.info(f"通过公共接口设置新的表格数据(list转DataFrame): {len(df_converted)} 行")
                else:
                    self._show_empty_table_with_prompt()
                    return
            elif hasattr(df, 'columns'):
                # 如果已经是DataFrame
                df_converted = df
                self.logger.info(f"通过公共接口设置新的表格数据(DataFrame): {len(df_converted)} 行")
            else:
                self.logger.error(f"不支持的数据类型: {type(df)}")
                self._show_empty_table_with_prompt()
                return
            
            # 分步骤处理：先映射字段名，再过滤字段（DEBUG节流，避免日志风暴）
            try:
                from src.utils.logging_utils import log_throttle
                if log_throttle('set-data-before-fields', 1.0):
                    self.logger.debug(f"🎯 [新架构] 数据设置前字段: {list(df_converted.columns)[:5]}...")
            except Exception:
                self.logger.debug(f"🎯 [新架构] 数据设置前字段: {list(df_converted.columns)[:5]}...")
            
            # 第1步：应用字段映射（英文->中文）
            df_converted = self._apply_field_mapping_to_dataframe(df_converted, current_table_name or table_name)
            try:
                from src.utils.logging_utils import log_throttle
                if log_throttle('set-data-after-mapping', 1.0):
                    self.logger.debug(f"🎯 [新架构] 字段映射后字段: {list(df_converted.columns)[:5]}...")
            except Exception:
                self.logger.debug(f"🎯 [新架构] 字段映射后字段: {list(df_converted.columns)[:5]}...")
            
            # 第2步：过滤系统字段
            df_converted = self._apply_system_field_filtering(df_converted, current_table_name or table_name)
            try:
                from src.utils.logging_utils import log_throttle
                if log_throttle('set-data-after-filtering', 1.0):
                    self.logger.debug(f"🎯 [新架构] 系统字段过滤后字段: {list(df_converted.columns)[:5]}...")
            except Exception:
                self.logger.debug(f"🎯 [新架构] 系统字段过滤后字段: {list(df_converted.columns)[:5]}...")
            
            # 第3步：应用用户偏好过滤（添加异常处理）
            try:
                # 🔧 [P0-修复] 通过主窗口引用调用方法，而非直接调用
                main_window = self._get_main_window()
                if main_window and hasattr(main_window, '_apply_table_field_preference'):
                    df_converted = main_window._apply_table_field_preference(df_converted, current_table_name or table_name)
                    self.logger.debug(f"最终字段: {list(df_converted.columns)[:5]}...")
            except Exception as e:
                self.logger.warning(f"用户偏好过滤失败，使用原始数据: {e}")
                # 继续使用未过滤的数据
            
            # 优先使用current_table_name，如果没有则使用table_name
            actual_table_name = current_table_name or table_name
            # 🔧 [P0-修复] 通过主窗口设置current_table_name，统一状态管理
            main_window = self._get_main_window()
            if main_window:
                main_window.current_table_name = actual_table_name
                self.logger.debug(f"🔧 [P0-修复] 通过主窗口设置表名: {actual_table_name}")
            else:
                self.logger.warning("🔧 [P0-修复] 无法获取主窗口引用，无法设置current_table_name")

            if self.use_pagination and len(df_converted) > 100:  # 当数据量大于100条时启用分页
                self._set_paginated_data(df_converted, preserve_headers, actual_table_name)
            else:
                # 直接显示所有数据
                headers = df_converted.columns.tolist()
                data = df_converted.to_dict('records')
                if preserve_headers:
                    self.current_headers = headers
                # 非分页模式时清除分页模式标志
                self.expandable_table.clear_pagination_mode()
                self.expandable_table.set_data(data, headers, current_table_name=actual_table_name)

                # 设置表格组件的表名，用于字段映射功能
                if actual_table_name:
                    self.expandable_table.set_table_name(actual_table_name)
                    # 🆕 [离休人员表格式化] 同时设置表类型，用于格式化判断
                    table_type = self.expandable_table._extract_table_type_from_name(actual_table_name)
                    self.expandable_table.set_table_type(table_type)
                    self.logger.debug(f"🆕 [格式化] 设置表类型: {table_type} (表名: {actual_table_name})")

                # 🔧 [P0-CRITICAL修复] 强化分页上下文检测，彻底解决按钮变灰问题
                if self.pagination_widget:
                    # 🔧 [P0-CRITICAL修复] 多重检测分页上下文，确保准确判断
                    current_context = getattr(self, '_current_operation_context', {})
                    is_pagination_call = (
                        current_context.get('operation_type') == 'page_change' or
                        current_context.get('persistent') == True or
                        hasattr(self, '_processing_pagination') and self._processing_pagination or
                        getattr(self, '_in_pagination_flow', False)
                    )

                    # 🔧 [P0-CRITICAL修复] 从数据元信息获取真实总记录数
                    metadata = getattr(df_converted, 'attrs', {}) if hasattr(df_converted, 'attrs') else {}
                    if not metadata and hasattr(df_converted, 'metadata'):
                        metadata = df_converted.metadata

                    true_total_records = metadata.get('total_records', 0)
                    current_page = metadata.get('current_page', 1)

                    if is_pagination_call and true_total_records > 0:
                        # 🔧 [P0-CRITICAL修复] 分页操作：使用真实总记录数，绝不使用当前页数据量
                        self.pagination_widget.set_total_records(true_total_records)
                        self.logger.info(f"🔧 [P0-CRITICAL修复] 分页上下文，设置真实总记录数: {true_total_records}，当前第{current_page}页，页面数据量={len(df_converted)}")
                    elif not is_pagination_call:
                        # 只在非分页调用时设置总记录数为当前数据量
                        self.pagination_widget.set_total_records(len(df_converted))
                        self.logger.info(f"🔧 [P0-CRITICAL修复] 非分页上下文，设置total_records={len(df_converted)}")
                    else:
                        # 🔧 [P2-统一状态] 分页操作但缺少元信息，尝试从多个来源获取
                        total_found = False

                        # 首先尝试从分页状态管理器获取
                        from src.core.pagination_state_manager import get_pagination_state_manager
                        pagination_manager = get_pagination_state_manager()
                        cached_total = pagination_manager.get_table_total_records(table_name)

                        if cached_total > 0:
                            self.pagination_widget.set_total_records(cached_total)
                            self.logger.info(f"🔧 [P2-统一状态] 从状态管理器获取总记录数: {cached_total}")
                            total_found = True
                        elif hasattr(self, 'table_data_service') and self.table_data_service and table_name:
                            try:
                                service_total = self.table_data_service.get_table_record_count(table_name)
                                if service_total > 0:
                                    self.pagination_widget.set_total_records(service_total)
                                    # 缓存到状态管理器
                                    pagination_manager.set_table_total_records(table_name, service_total)
                                    self.logger.info(f"🔧 [P2-统一状态] 从服务层获取并缓存总记录数: {service_total}")
                                    total_found = True
                            except Exception as e:
                                self.logger.error(f"🔧 [P2-统一状态] 获取服务层总记录数失败: {e}")

                        if not total_found:
                            self.logger.warning(f"🔧 [P2-统一状态] 无法获取总记录数，保持当前设置")
                    # 🔧 [CRITICAL修复] 更健壮的页码重置逻辑
                    current_context = getattr(self, '_current_operation_context', {})
                    operation_type = current_context.get('operation_type')
                    
                    # 🔧 [方案二修复] 智能获取当前页码，避免参数传递复杂性
                    current_page = None
                    try:
                        current_page = self.pagination_widget.get_current_page()
                    except Exception as e:
                        self.logger.warning(f"获取当前页码失败，使用默认值: {e}")
                        current_page = 1
                    
                    # 如果有明确的当前页码参数，优先使用
                    if current_page and current_page > 0:
                        # 有明确页码时，直接设置不重置
                        if self.pagination_widget.get_current_page() != current_page:
                            self.pagination_widget.set_current_page(current_page)
                            self.logger.info(f"🔧 [页码同步] 设置为参数指定页码: {current_page}")
                    elif operation_type == 'page_change':
                        # 分页操作：使用目标页码
                        target_page = current_context.get('target_page', 1)
                        if self.pagination_widget.get_current_page() != target_page:
                            self.pagination_widget.set_current_page(target_page)
                            self.logger.info(f"🔧 [分页操作] 设置为目标页码: {target_page}")
                    elif operation_type == 'sort_change':
                        # 排序操作：保持当前页码不变
                        self.logger.info(f"🔧 [排序操作] 保持当前页码: {self.pagination_widget.get_current_page()}")
                    elif operation_type not in ['sort_change', 'page_change']:
                        # 其他操作：重置到第1页
                        self.pagination_widget.set_current_page(1)
                        self.logger.info("🔧 [其他操作] 重置页码到第1页")

            self.has_real_data = True

            # 🔧 [P0-修复] 数据设置完成后修复显示亮度 - 通过主窗口调用
            safe_single_shot(100, self._request_brightness_fix)

            # 🔧 [P1-2] 数据设置完成后清理可能的表头重影
            safe_single_shot(150, self._post_data_header_cleanup)

        except Exception as e:
            self.logger.error(f"设置数据失败: {e}", exc_info=True)
            # 🔧 [P0-CRITICAL修复] 避免因为错误而重置样式，仅记录错误不显示空表格
            self.logger.warning(f"🔧 [样式保护] 数据设置失败但保持当前UI状态: {str(e)}")
            # 不调用 _show_empty_table_with_prompt 来避免样式重置
        finally:
            # 🔧 [P2优化] 确保重置递归调用防护锁，记录持续时间
            if hasattr(self, '_setting_data_lock'):
                self._setting_data_lock = False
                if hasattr(self, '_data_set_start_time'):
                    duration = time.time() - self._data_set_start_time
                    self.logger.debug(f"🔧 [P2优化] 递归防护锁已释放，数据设置耗时: {duration:.3f}秒")
                else:
                    self.logger.debug("🔧 [P2优化] 递归防护锁已释放")
    
    def _is_legitimate_data_update(self) -> bool:
        """🔧 [P2优化] 智能分析是否为合法的数据更新连续调用"""
        try:
            # 🔧 [紧急修复] 简化检测逻辑，避免无限递归
            # 检查是否在合理的调用深度内
            import traceback
            stack_depth = len(traceback.extract_stack())
            if stack_depth > 100:  # 如果调用深度过深，认为是真正的递归
                return False
            
            # 🔧 [紧急修复] 简化为基本检查，避免复杂分析导致递归
            # 只进行基本的调用路径检查
            stack_trace = traceback.format_stack()
            stack_str = ''.join(stack_trace)
            
            # 如果堆栈中包含_show_empty_table相关方法，可能是合法调用
            if '_show_empty_table' in stack_str or 'refresh' in stack_str:
                return True
                
            return False
            
        except Exception as e:
            self.logger.warning(f"🔧 [P2优化] 分析调用合法性时出错: {e}")
            return False  # 出错时保守处理，认为不合法
    
    def _set_paginated_data(self, df: pd.DataFrame, preserve_headers: bool = False, current_table_name: str = ""):
        """
        🔧 [P2-1] 增强版设置分页数据，包含完整的状态同步
        """
        try:
            self.logger.info(f"🔧 [P2-1] 开始设置分页数据: {len(df)}条记录")

            # 🔧 [P2-1] 更新分页组件状态
            total_records = len(df)
            if self.pagination_widget:
                # 使用增强的状态同步方法
                current_data_count = None
                self.pagination_widget.sync_with_data_source(total_records, current_data_count)
                state = self.pagination_widget.get_current_state()

                # 🔧 [P2-1] 获取当前页数据
                start_idx = (state.current_page - 1) * state.page_size
                end_idx = start_idx + state.page_size
                page_df = df.iloc[start_idx:end_idx]

                # 🔧 [P2-1] 验证数据量一致性
                actual_data_count = len(page_df)
                expected_count = min(state.page_size, total_records - start_idx)
                if actual_data_count != expected_count:
                    self.logger.warning(f"🔧 [P2-1] 分页数据量不一致: 期望{expected_count}条，实际{actual_data_count}条")

                # 应用统一字段映射到分页数据
                # 🔧 [P0-修复] 从主窗口获取current_table_name
                main_window = self._get_main_window()
                actual_current_table_name = current_table_name or (main_window.current_table_name if main_window else "")
                mapped_page_df = self._apply_unified_field_processing(page_df, actual_current_table_name)

                # 🔧 [P2-1] 更新分页组件的实际数据量
                self.pagination_widget.sync_with_data_source(total_records, len(mapped_page_df))
                
                # 设置表格数据
                headers = mapped_page_df.columns.tolist()
                data = mapped_page_df.to_dict('records')
                if preserve_headers:
                    self.current_headers = headers

                # 使用传入的表名或当前表名
                # 🔧 [P0-修复] 从主窗口获取current_table_name，统一状态管理
                main_window = self._get_main_window()
                table_name_to_use = current_table_name or (main_window.current_table_name if main_window else "")
                self.expandable_table.set_data(data, headers, current_table_name=table_name_to_use)
                
                # 设置分页状态到表格组件，确保行号正确显示
                pagination_state = {
                    'current_page': state.current_page,
                    'page_size': state.page_size,
                    'total_records': total_records,
                    'start_record': state.start_record,
                    'end_record': state.end_record
                }
                self.logger.info(f"🔍 [调试] 即将调用set_pagination_state: 第{pagination_state.get('current_page')}页, 记录{pagination_state.get('start_record')}-{pagination_state.get('end_record')}")
                self.expandable_table.set_pagination_state(pagination_state)
                self.logger.info("🔍 [调试] set_pagination_state调用完成")
                
                self.logger.debug(f"🎯 [新架构] 分页数据字段映射完成: {len(headers)} 个表头")
                self.logger.info(f"分页数据字段映射完成: {len(headers)} 个表头")

                # 设置表格组件的表名，用于字段映射功能
                if table_name_to_use:
                    self.expandable_table.set_table_name(table_name_to_use)
                    # 🆕 [离休人员表格式化] 同时设置表类型，用于格式化判断
                    table_type = self.expandable_table._extract_table_type_from_name(table_name_to_use)
                    self.expandable_table.set_table_type(table_type)
                    self.logger.debug(f"🆕 [格式化-分页] 设置表类型: {table_type} (表名: {table_name_to_use})")

                self.logger.info(f"分页数据设置完成: 第{state.current_page}页, 显示{len(page_df)}条记录, 行号{state.start_record}-{state.end_record}")
        except Exception as e:
            self.logger.error(f"设置分页数据失败: {e}")
    
    def _handle_global_sort_request(self, sort_columns: List[Dict[str, Any]], table_name: str = None):
        """🔧 [全局排序] 处理全局排序请求"""
        try:
            if not self.global_sort_enabled:
                self.logger.info("🔧 [全局排序] 全局排序已禁用，使用本地排序")
                return False
                
            # 🔧 [P0-修复] 从主窗口获取current_table_name，统一状态管理
            main_window = self._get_main_window()
            target_table = table_name or (main_window.current_table_name if main_window else "")
            if not target_table:
                self.logger.warning("🔧 [全局排序] 没有目标表，无法执行全局排序")
                return False
            
            # 转换中文表头到英文数据库字段名
            converted_sort_columns = self._convert_sort_columns_to_db_fields(sort_columns, target_table)
            if not converted_sort_columns:
                self.logger.warning("🔧 [全局排序] 无法转换排序列到数据库字段")
                return False
            
            # 保存当前排序状态
            self.current_sort_columns = converted_sort_columns
            
            # 同步到排序状态管理器
            if hasattr(self, 'sort_state_manager') and self.sort_state_manager:
                try:
                    self.sort_state_manager.save_sort_state(target_table, converted_sort_columns)
                    self.logger.info(f"排序状态已同步到管理器")
                except Exception as e:
                    self.logger.warning(f"同步排序状态失败: {e}")
            
            self.logger.info(f"🔧 [全局排序] 执行全局排序: {target_table}, 排序列: {len(converted_sort_columns)}")
            
            # 重新加载当前页数据，但带上排序条件
            self._reload_current_page_with_sort()
            
            return True
            
        except Exception as e:
            self.logger.error(f"🔧 [全局排序] 处理全局排序失败: {e}")
            return False
    
    def _convert_sort_columns_to_db_fields(self, sort_columns: List[Dict[str, Any]], table_name: str) -> List[Dict[str, Any]]:
        """🔧 [全局排序] 将中文表头转换为英文数据库字段名"""
        try:
            # 获取字段映射（中文->英文反向映射）
            field_mapping = self._get_reverse_field_mapping(table_name)
            if not field_mapping:
                self.logger.warning(f"🔧 [全局排序] 表 {table_name} 没有字段映射")
                return []
            
            converted_columns = []
            for sort_col in sort_columns:
                chinese_column = sort_col.get('column_name', '')
                order = sort_col.get('order', 'ascending')
                
                # 查找对应的英文字段名
                english_column = field_mapping.get(chinese_column)
                if english_column:
                    converted_columns.append({
                        'column_name': english_column,
                        'order': order
                    })
                    self.logger.info(f"🔧 [全局排序] 字段映射: '{chinese_column}' -> '{english_column}'")
                else:
                    self.logger.warning(f"🔧 [全局排序] 无法找到 '{chinese_column}' 的英文字段映射")
            
            return converted_columns
            
        except Exception as e:
            self.logger.error(f"🔧 [全局排序] 转换排序列失败: {e}")
            return []
    
    def _get_table_field_mapping(self, table_name: str) -> Dict[str, str]:
        """获取表的字段映射（英文->中文）"""
        try:
            # 优先级1: 通过config_sync_manager获取字段映射
            if hasattr(self, 'config_sync_manager') and self.config_sync_manager:
                mapping = self.config_sync_manager.load_mapping(table_name)
                if mapping:
                    self.logger.debug(f"从config_sync_manager获取到字段映射: {table_name}, {len(mapping)}个字段")
                    return mapping
                    
            # 优先级2: 从字段映射缓存获取
            if hasattr(self, '_current_field_mapping') and self._current_field_mapping:
                self.logger.debug(f"使用当前字段映射缓存")
                return self._current_field_mapping
            
            # 🔧 [P1-完成] 移除降级机制，只使用新架构ConfigSyncManager
            self.logger.error(f"🔧 [P1-完成] ConfigSyncManager不可用，新架构组件异常")
                            
            self.logger.warning(f"表 {table_name} 没有可用的字段映射")
            return {}
            
        except Exception as e:
            self.logger.error(f"获取表字段映射失败: {e}")
            return {}
    
    def _get_reverse_field_mapping(self, table_name: str) -> Dict[str, str]:
        """🔧 [全局排序] 获取反向字段映射（中文->英文）"""
        try:
            # 检查缓存
            cache_key = f"reverse_{table_name}"
            if cache_key in self.field_mapping_cache:
                return self.field_mapping_cache[cache_key]
            
            # 获取正向映射（英文->中文）
            forward_mapping = self._get_table_field_mapping(table_name)
            if not forward_mapping:
                return {}
            
            # 生成反向映射（中文->英文）
            reverse_mapping = {chinese: english for english, chinese in forward_mapping.items()}
            
            # 缓存结果
            self.field_mapping_cache[cache_key] = reverse_mapping
            
            self.logger.info(f"🔧 [全局排序] 生成反向字段映射: {table_name}, {len(reverse_mapping)}个字段")
            return reverse_mapping
            
        except Exception as e:
            self.logger.error(f"🔧 [全局排序] 获取反向字段映射失败: {e}")
            return {}
    
    def _reload_current_page_with_sort(self):
        """🔧 [全局排序] 重新加载当前页数据（带排序）"""
        try:
            # 🔧 [P0-2] 从主窗口获取current_table_name，统一状态管理
            main_window = self._get_main_window()
            current_table_name = main_window.current_table_name if main_window else ""
            if not current_table_name:
                return
            
            # 获取当前页码，保持分页状态
            current_page = 1
            page_size = 50
            
            # 尝试从多个来源获取当前页码状态
            if hasattr(self, 'state_manager') and self.state_manager:
                # 方式1：直接从self获取state_manager
                try:
                    # 🔧 [P0-修复] 从主窗口获取current_table_name，统一状态管理
                    main_window = self._get_main_window()
                    current_table_name = main_window.current_table_name if main_window else ""
                    pagination_state = self.state_manager.get_table_state(current_table_name)
                    current_page = pagination_state.current_page
                    page_size = pagination_state.page_size
                    self.logger.info(f"从self.state_manager获取页码: {current_page}")
                except Exception as e:
                    self.logger.warning(f"self.state_manager获取失败: {e}")
            
            # 方式2：从分页组件获取当前页码
            if hasattr(self, 'pagination_widget') and self.pagination_widget:
                try:
                    widget_page = getattr(self.pagination_widget, 'current_page', None)
                    if widget_page and widget_page > 0:
                        current_page = widget_page
                        self.logger.info(f"从pagination_widget获取页码: {current_page}")
                except Exception as e:
                    self.logger.warning(f"pagination_widget获取失败: {e}")
            
            # 🔧 [P1-完成] 使用新架构统一状态管理器
            if current_page == 1:
                self.logger.warning("从新架构状态管理器获取页码失败，使用默认页码1")
            
            self.logger.info(f"🔧 [全局排序] 重新加载页面: {current_page}页, 排序列: {len(self.current_sort_columns)}")
            
            # 使用带排序的数据获取方法
            df, total_count = self.table_manager.get_dataframe_paginated_with_sort(
                current_table_name,
                page=current_page,
                page_size=page_size,
                sort_columns=self.current_sort_columns
            )
            
            if df is not None and not df.empty:
                # 应用字段映射（英文->中文表头）
                df = self._apply_field_mapping_to_dataframe(df, current_table_name)

                # 更新表格显示
                headers = df.columns.tolist()
                data = df.to_dict('records')

                self.expandable_table.set_data(data, headers, current_table_name=current_table_name)
                
                # 更新分页状态
                pagination_state = {
                    'current_page': current_page,
                    'page_size': page_size,
                    'total_records': total_count,
                    'start_record': (current_page - 1) * page_size + 1,
                    'end_record': min(current_page * page_size, total_count)
                }
                self.logger.info(f"🔍 [调试] 即将调用set_pagination_state: 第{pagination_state.get('current_page')}页, 记录{pagination_state.get('start_record')}-{pagination_state.get('end_record')}")
                self.expandable_table.set_pagination_state(pagination_state)
                self.logger.info("🔍 [调试] set_pagination_state调用完成")
                
                self.logger.info(f"🔧 [全局排序] 页面重新加载完成: {len(df)}行数据")
            
        except Exception as e:
            self.logger.error(f"🔧 [全局排序] 重新加载页面失败: {e}")
    
    def _try_reinitialize_table_data_service(self) -> bool:
        """🔧 [新架构修复] 尝试重新初始化table_data_service"""
        try:
            self.logger.info("🔧 [新架构修复] 开始重新初始化table_data_service")
            
            # 检查架构工厂是否可用
            if not hasattr(self, 'architecture_factory') or not self.architecture_factory:
                self.logger.error("🔧 [新架构修复] architecture_factory不可用")
                return False
            
            # 检查架构工厂是否已初始化
            if not self.architecture_factory.is_initialized():
                self.logger.error("🔧 [新架构修复] architecture_factory未初始化")
                return False
            
            # 尝试重新获取table_data_service
            try:
                new_service = self.architecture_factory.get_table_data_service()
                if new_service:
                    self.table_data_service = new_service
                    self.logger.info("🔧 [新架构修复] table_data_service重新获取成功")
                    return True
                else:
                    self.logger.error("🔧 [新架构修复] 架构工厂返回None的table_data_service")
                    return False
            except Exception as e:
                self.logger.error(f"🔧 [新架构修复] 从架构工厂获取table_data_service失败: {e}")
                return False
                
        except Exception as e:
            self.logger.error(f"🔧 [新架构修复] 重新初始化table_data_service异常: {e}")
            return False
    
    def toggle_global_sort(self, enabled: bool):
        """🔧 [全局排序] 切换全局排序开关"""
        self.global_sort_enabled = enabled
        self.logger.info(f"🔧 [全局排序] 全局排序已{'启用' if enabled else '禁用'}")
        
        if not enabled:
            # 禁用时清除排序状态
            self.current_sort_columns = []
            
            # 同步清除到排序状态管理器
            if hasattr(self, 'sort_state_manager') and self.sort_state_manager:
                try:
                    current_table_name = getattr(self, 'current_table_name', '')
                    if current_table_name:
                        self.sort_state_manager.clear_sort_state(current_table_name)
                        self.logger.info(f"全局排序禁用，排序状态已清除")
                except Exception as e:
                    self.logger.warning(f"全局排序禁用时清除状态失败: {e}")
            
            self._reload_current_page_with_sort()
    
    def _on_page_changed(self, page: int):
        """🧹 [代码清理] 分页处理已统一由PrototypeMainWindow处理"""
        # 🔧 [修复] 遍历父窗口层次结构找到PrototypeMainWindow
        try:
            # 从当前控件开始，向上遍历父窗口层次结构
            widget = self
            main_window = None
            
            while widget:
                widget = widget.parent()
                if widget and hasattr(widget, '_on_page_changed_new_architecture'):
                    main_window = widget
                    break
            
            if main_window:
                self.logger.debug(f"🔧 [委托] 分页请求委托给主窗口: 第{page}页")
                main_window._on_page_changed_new_architecture(page)
            else:
                self.logger.error(f"🔧 [错误] 无法找到具有_on_page_changed_new_architecture方法的父窗口")
        except Exception as e:
            self.logger.error(f"🔧 [错误] 分页委托失败: {e}")

    def _on_page_size_changed(self, page_size: int):
        """处理页面大小变化事件 - 重置到第一页"""
        try:
            # 🔧 [P0-2] 从主窗口获取current_table_name，统一状态管理
            main_window = self._get_main_window()
            current_table_name = main_window.current_table_name if main_window else ""
            if not current_table_name:
                self.logger.warning("没有当前表名，无法进行分页查询")
                return
            
            # 简化处理 - 直接调用页码变化
            self._on_page_changed(1)  # 重置到第一页
            self.logger.info(f"页面大小变化处理完成: 每页{page_size}条")
            
        except Exception as e:
            self.logger.error(f"处理页面大小变化失败: {e}")
    
    def _on_pagination_refresh(self):
        """分页刷新：仅刷新当前表格的分页/数据，不触发全局刷新或导航变动"""
        try:
            self.logger.info("🔄[scope=page] 分页刷新被触发，开始局部数据刷新")

            table_name = self._get_active_table_name() if hasattr(self, '_get_active_table_name') else getattr(self, 'current_table_name', None)
            if not table_name:
                # 无上下文时给出明确提示，不再渲染空表
                if hasattr(self, '_update_status_label'):
                    self._update_status_label("请先选择具体的数据表后再进行分页刷新", "warning")
                self.logger.info("🔄[scope=page] 无当前表名，终止分页刷新")
                return
            if hasattr(self, 'table_data_service') and self.table_data_service and table_name:
                try:
                    self.table_data_service.refresh_table_data(table_name)
                    if hasattr(self, '_safe_refresh_current_data_display'):
                        self._safe_refresh_current_data_display(table_name)
                    elif hasattr(self, '_refresh_current_data_display'):
                        self._refresh_current_data_display(table_name)
                    self.logger.info(f"🔄[scope=page] 分页刷新完成: {table_name}")
                    return
                except Exception as svc_err:
                    self.logger.warning(f"🔄[scope=page] 服务刷新失败，回退本地刷新: {svc_err}")

            # 回退：仅表格局部刷新（不触达全局）
            if hasattr(self, '_refresh_table_data'):
                self._refresh_table_data()
                self.logger.info("🔄[scope=page] 已完成本地表格刷新（回退路径）")

        except Exception as e:
            self.logger.error(f"🔄[scope=page] 分页刷新失败: {e}")
    
    
    def _apply_unified_field_processing(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """应用统一字段处理到DataFrame - 调用主窗口的实现"""
        try:
            # 更可靠地获取主窗口实例
            from src.gui.prototype.prototype_main_window import PrototypeMainWindow
            main_window = self.parent()
            
            # 向上查找PrototypeMainWindow实例
            attempts = 0
            while main_window and not isinstance(main_window, PrototypeMainWindow) and attempts < 5:
                main_window = main_window.parent()
                attempts += 1
            
            if main_window and isinstance(main_window, PrototypeMainWindow):
                self.logger.debug(f"找到主窗口实例，调用统一字段处理")
                return main_window._apply_unified_field_processing(df, table_name)
            else:
                # 如果找不到主窗口，尝试从全局查找
                from PyQt5.QtWidgets import QApplication
                app = QApplication.instance()
                if app:
                    for widget in app.allWidgets():
                        if isinstance(widget, PrototypeMainWindow):
                            self.logger.debug(f"从全局找到主窗口实例")
                            return widget._apply_unified_field_processing(df, table_name)
                
                self.logger.warning("无法找到主窗口的统一字段处理方法，返回原数据")
                return df
        except Exception as e:
            self.logger.error(f"调用主窗口统一字段处理失败: {e}")
            return df
    
    
    def _apply_system_field_filtering(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """过滤系统字段，隐藏不需要显示的字段"""
        try:
            print(f"MainWorkspaceArea系统字段过滤: {table_name}")
            
            # 定义需要隐藏的系统字段（包括英文和中文名称）
            # 🎯 [用户需求] 根据用户要求，不隐藏年份和月份字段，需要格式化后显示
            SYSTEM_HIDDEN_FIELDS = [
                'created_at', 'updated_at', 'id', 'sequence_number',
                '创建时间', '更新时间', '自增主键', '序号'
                # 移除 'year', 'month' - 用户要求显示并格式化这些字段
            ]
            
            # 过滤掉系统字段
            visible_columns = [col for col in df.columns if col not in SYSTEM_HIDDEN_FIELDS]
            
            if len(visible_columns) != len(df.columns):
                self.logger.info(f"过滤了 {len(df.columns) - len(visible_columns)} 个系统字段")
                print(f"过滤了 {len(df.columns) - len(visible_columns)} 个系统字段")
            
            return df[visible_columns] if visible_columns else df
                
        except Exception as e:
            print(f"系统字段过滤失败: {e}")
            self.logger.error(f"系统字段过滤失败: {e}")
            return df
    
    
    def _load_real_data(self):
        """从真实数据源加载数据(示例)"""
        try:
            # 数据文件路径
            data_dir = Path("data/工资表")
            if not data_dir.exists():
                return False
            
            # 查找Excel文件
            excel_files = list(data_dir.glob("*.xls*"))
            if not excel_files:
                return False
            
            # 选择最新的文件
            latest_file = max(excel_files, key=lambda f: f.stat().st_mtime)
            
            self.logger.info(f"尝试加载数据文件: {latest_file}")
            
            # 使用Excel导入器
            excel_importer = ExcelImporter()
            
            # 获取工作表
            sheet_names = excel_importer.get_sheet_names(str(latest_file))
            target_sheet = None
            
            # 优先选择"全部在职人员工资表"
            for sheet in sheet_names:
                if "全部在职人员" in sheet or "在职人员" in sheet:
                    target_sheet = sheet
                    break
            
            if not target_sheet and sheet_names:
                target_sheet = sheet_names[0]
            
            if not target_sheet:
                return False
            
            # 导入数据（启用过滤功能）
            df = excel_importer.import_data(
                file_path=str(latest_file),
                sheet_name=target_sheet,
                filter_invalid=True  # 启用数据过滤
            )
            
            if df.empty:
                self.logger.warning("Excel文件数据为空或全部记录被过滤")
                return False
            
            # 设置表格数据
            self.expandable_table.set_data(df)
            
            # 更新状态
            self.has_real_data = True
            self.current_data_source = f"Excel文件: {latest_file.name}"
            self.current_data_path = f"工资表>2025年>5月>{target_sheet}"
            
            # 尝试将数据保存到数据库以供后续使用
            self._save_to_database(df, ["工资表", "2025", "05", "全部在职人员"])
            
            self._update_status_label(f"已加载Excel数据: {len(df)}条记录", "success")
            
            self.logger.info(f"Excel数据加载成功: {len(df)}条记录，来源: {latest_file.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"真实数据加载失败: {e}")
            return False
    
    def _load_sample_data(self):
        """加载示例数据"""
        try:
            data, headers = create_sample_data(50)  # 减少示例数据量
            self.expandable_table.set_data(data)
            self.current_data = data
            self.current_headers = headers
            self.has_real_data = False
            
            self._update_status_label("示例数据模式 - 请导入Excel文件加载真实数据", "warning")
            
        except Exception as e:
            self.logger.error(f"加载示例数据失败: {e}")
    
    def _update_status_label(self, message: str, status_type: str = "info"):
        """更新状态标签"""
        try:
            if hasattr(self, 'status_label'):
                self.status_label.setText(message)
                
                # 根据状态类型设置样式
                if status_type == "success":
                    style = """
                        QLabel {
                            color: #4CAF50;
                            font-weight: bold;
                            padding: 8px;
                            background-color: #E8F5E8;
                            border-radius: 4px;
                        }
                    """
                elif status_type == "warning":
                    style = """
                        QLabel {
                            color: #FF9800;
                            font-weight: bold;
                            padding: 8px;
                            background-color: #FFF3E0;
                            border-radius: 4px;
                        }
                    """
                elif status_type == "error":
                    style = """
                        QLabel {
                            color: #F44336;
                            font-weight: bold;
                            padding: 8px;
                            background-color: #FFEBEE;
                            border-radius: 4px;
                        }
                    """
                else:  # info
                    style = """
                        QLabel {
                            color: #2196F3;
                            font-weight: bold;
                            padding: 8px;
                            background-color: #E3F2FD;
                            border-radius: 4px;
                        }
                    """
                
                self.status_label.setStyleSheet(style)
                
        except Exception as e:
            self.logger.error(f"更新状态标签失败: {e}")
    
    def _show_info_message(self, title: str, message: str):
        """显示信息消息"""
        QMessageBox.information(self, title, message)
    
    def _show_warning_message(self, title: str, message: str):
        """显示警告消息"""
        QMessageBox.warning(self, title, message)
    
    def _show_error_message(self, title: str, message: str):
        """显示错误消息"""
        QMessageBox.critical(self, title, message)
    
    def setup_ui(self):
        """
        设置工作区域界面
        """
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 控制面板区域
        self.control_panel = self.create_control_panel()
        layout.addWidget(self.control_panel)
        
        # Material Design标签导航栏
        self.tab_widget = MaterialTabWidget()
        
        # 创建标签页
        self.create_tabs()
        layout.addWidget(self.tab_widget)
    
    def create_control_panel(self) -> QWidget:
        """
        创建控制面板
        
        Returns:
            控制面板部件
        """
        panel = QFrame()
        panel.setObjectName("control_panel")  # 添加对象名称用于样式管理
        panel.setFixedHeight(80)
        panel.setStyleSheet("""
            QFrame#control_panel {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
            }
        """)
        
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(15)
        
        # 操作按钮组
        self.btn_add = QPushButton("+ 新增异动")
        self.btn_import = QPushButton("📥 导入数据")
        self.btn_export = QPushButton("📤 导出报告")
        self.btn_refresh = QPushButton("🔄 全局刷新")
        
        # 设置按钮的响应式类名
        for btn in [self.btn_add, self.btn_import, self.btn_export, self.btn_refresh]:
            btn.setObjectName("control_button")
            btn.setProperty("button_size", "medium")  # 默认中等大小
        
        # 按钮基础样式
        btn_style = """
            QPushButton#control_button {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 500;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton#control_button:hover {
                background-color: #1976D2;
            }
            QPushButton#control_button:pressed {
                background-color: #1565C0;
            }
            QPushButton#control_button[button_size="small"] {
                padding: 8px 16px;
                font-size: 12px;
                min-width: 80px;
            }
            QPushButton#control_button[button_size="large"] {
                padding: 12px 24px;
                font-size: 16px;
                min-width: 120px;
            }
        """
        
        for btn in [self.btn_add, self.btn_import, self.btn_export, self.btn_refresh]:
            btn.setStyleSheet(btn_style)
        
        # 搜索区域
        search_container = QWidget()
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(10)
        
        # 搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setObjectName("search_input")
        self.search_input.setPlaceholderText("搜索员工姓名、部门...")
        self.search_input.setProperty("font_scale", 1.0)  # 用于响应式字体缩放
        
        # 🔧 [P0-修复] 应用Material Design输入框样式
        self._apply_input_style(self.search_input)
        
        # 搜索按钮
        self.btn_search = QPushButton("🔍")
        self.btn_search.setObjectName("icon_button")
        self.btn_search.setFixedSize(40, 40)
        self.btn_search.setProperty("button_size", "medium")
        
        icon_btn_style = """
            QPushButton#icon_button {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 20px;
                font-size: 16px;
            }
            QPushButton#icon_button:hover {
                background-color: #5a6268;
            }
            QPushButton#icon_button[button_size="small"] {
                font-size: 14px;
            }
            QPushButton#icon_button[button_size="large"] {
                font-size: 18px;
            }
        """
        self.btn_search.setStyleSheet(icon_btn_style)
        
        # 布局安排 - 左侧操作按钮，右侧搜索
        layout.addWidget(self.btn_add)
        layout.addWidget(self.btn_import)
        layout.addWidget(self.btn_export)
        layout.addWidget(self.btn_refresh)
        layout.addStretch()  # 弹性空间
        
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.btn_search)
        layout.addWidget(search_container)
        
        # 存储控件引用用于响应式调整
        self.control_panel_widgets = {
            'panel': panel,
            'buttons': [self.btn_add, self.btn_import, self.btn_export, self.btn_refresh],
            'search_input': self.search_input,
            'icon_button': self.btn_search
        }
        
        return panel
    
    def create_tabs(self):
        """
        创建标签页 - 使用Material Design标签组件
        """
        # 数据概览标签
        overview_widget = self.create_overview_tab()
        self.tab_widget.add_material_tab(overview_widget, "数据概览")
        
        # 异动详情标签
        detail_widget = self.create_detail_tab()
        self.tab_widget.add_material_tab(detail_widget, "异动详情")
        
        # 报告预览标签
        report_widget = self.create_report_tab()
        self.tab_widget.add_material_tab(report_widget, "报告预览")
        
        # 连接Material Design标签信号
        self.tab_widget.tab_changed.connect(self._on_material_tab_changed)
    
    def create_overview_tab(self) -> QWidget:
        """
        创建概览标签页 - 集成虚拟化可展开表格
        
        Returns:
            概览标签页部件
        """
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # 表格工具栏
        toolbar_layout = QHBoxLayout()
        
        # 表格操作按钮
        expand_all_btn = QPushButton("展开所有")
        self._apply_button_style(expand_all_btn, "success")
        
        collapse_all_btn = QPushButton("折叠所有")
        self._apply_button_style(collapse_all_btn, "warning")
        
        refresh_btn = QPushButton("表格刷新")
        self._apply_button_style(refresh_btn, "primary")

        # 🔧 [P0-2] 连接刷新按钮信号（仅表格范围）

        toolbar_layout.addWidget(expand_all_btn)
        toolbar_layout.addWidget(collapse_all_btn)
        toolbar_layout.addWidget(refresh_btn)
        toolbar_layout.addStretch()
        
        # 状态标签
        self.status_label = QLabel("就绪 - 数据加载完成")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                font-weight: bold;
                padding: 8px;
                background-color: #E8F5E8;
                border-radius: 4px;
            }
        """)
        toolbar_layout.addWidget(self.status_label)
        
        layout.addLayout(toolbar_layout)
        
        # 创建虚拟化可展开表格
        # 🆕 [新架构] 通过依赖注入传递ConfigSyncManager
        if self.config_sync_manager is None:
            raise ValueError("MainWorkspaceArea.config_sync_manager 为 None，无法创建表格")
        self.expandable_table = VirtualizedExpandableTable(config_sync_manager=self.config_sync_manager)
        
        # 🔧 [P0-修复] 应用Material Design表格样式
        self._apply_table_style(self.expandable_table)
        
        # 初始化时尝试加载真实数据，如果没有则使用示例数据
        self._initialize_table_data()
        
        # 连接信号
        expand_all_btn.clicked.connect(self.expandable_table.expand_all_rows)
        collapse_all_btn.clicked.connect(self.expandable_table.collapse_all_rows)
        refresh_btn.clicked.connect(self._refresh_table_data)
        
        # 表格事件连接
        self.expandable_table.row_expanded.connect(self._on_row_expanded)
        self.expandable_table.row_collapsed.connect(self._on_row_collapsed)
        self.expandable_table.selection_changed.connect(self._on_table_selection_changed)
        
        layout.addWidget(self.expandable_table)
        
        # 添加分页组件
        self.pagination_widget = PaginationWidget()
        self.pagination_widget.page_changed.connect(self._on_page_changed)
        self.pagination_widget.page_size_changed.connect(self._on_page_size_changed)
        self.pagination_widget.refresh_requested.connect(self._on_pagination_refresh)
        
        layout.addWidget(self.pagination_widget)
        
        return widget
    
    def _initialize_table_data(self):
        """初始化表格数据 - 保持空白状态，等待用户手动导入"""
        try:
            # 初始化空表格，显示提示信息
            self._show_empty_table_with_prompt()
            
        except Exception as e:
            self.logger.error(f"初始化表格失败: {e}")
            # 🔧 [P0-修复] 调用现在位于PrototypeMainWindow类中的方法
            pass  # 这里的调用将被移除，因为它应该在正确的类中
    
    def _refresh_table_data(self):
        """刷新表格数据 - 仅刷新已导入的数据，不自动加载或生成示例数据"""
        try:
            if self.has_real_data and self.current_data_path:
                # 仅当有真实数据路径时才重新加载
                path_parts = self.current_data_path.split('>')
                if len(path_parts) >= 4:
                    data_type = path_parts[0].strip()
                    year = path_parts[1].strip().replace('年', '')
                    month = path_parts[2].strip().replace('月', '')
                    category = path_parts[3].strip()
                    
                    # 尝试从数据库重新加载数据
                    from src.gui.prototype.prototype_main_window import PrototypeMainWindow
                    main_window = self.parent()
                    while main_window and not isinstance(main_window, PrototypeMainWindow):
                        main_window = main_window.parent()
                    
                    if main_window and hasattr(main_window, '_load_database_data'):
                        success = main_window._load_database_data(data_type, year, month, category)
                        if success:
                            self._update_status_label("数据刷新成功", "success")
                            return
                
                self._update_status_label("数据刷新失败，请重新导入", "warning")
            else:
                # 没有数据时，保持空白状态
                self._show_empty_table_with_prompt()
                self._update_status_label("无数据可刷新，请先导入Excel文件", "info")
            
        except Exception as e:
            self.logger.error(f"刷新表格数据失败: {e}")
            self._update_status_label(f"刷新失败: {str(e)}", "error")
    
    def _on_row_expanded(self, row: int, detail_data: Dict[str, Any]):
        """处理行展开事件"""
        try:
            self.status_label.setText(f"行 {row+1} 已展开 - 显示详细信息")
            # 这里可以添加更多的展开处理逻辑
            # 比如更新右侧面板、发送通信信号等
            
        except Exception as e:
            print(f"处理行展开事件失败: {e}")
    
    def _on_row_collapsed(self, row: int, main_data: Dict[str, Any]):
        """处理行折叠事件"""
        try:
            self.status_label.setText(f"行 {row+1} 已折叠")
            # 这里可以添加更多的折叠处理逻辑
            
        except Exception as e:
            print(f"处理行折叠事件失败: {e}")
    
    def _on_table_selection_changed(self, selected_rows: List[int]):
        """处理表格选择变化"""
        try:
            if selected_rows:
                self.status_label.setText(f"已选择 {len(selected_rows)} 行")
            else:
                self.status_label.setText("无选择")
                
        except Exception as e:
            print(f"处理表格选择变化失败: {e}")
    
    def create_detail_tab(self) -> QWidget:
        """
        创建异动详情标签页
        
        Returns:
            详情标签页部件
        """
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 详情表格（支持行展开）
        table = QTableWidget(15, 8)
        table.setHorizontalHeaderLabels([
            "类型", "姓名", "工号", "变更前", "变更后", "变更金额", "日期", "备注"
        ])
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #2196F3;
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(table)
        return widget
    
    def create_report_tab(self) -> QWidget:
        """
        创建报告预览标签页
        
        Returns:
            报告标签页部件
        """
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 报告预览区域
        preview_label = QLabel("报告预览区域\n\n点击'生成报告'按钮查看详细报告...")
        preview_label.setAlignment(Qt.AlignCenter)
        preview_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px dashed #ddd;
                color: #666;
                font-size: 16px;
                padding: 50px;
            }
        """)
        
        layout.addWidget(preview_label)
        return widget
    
    def _on_material_tab_changed(self, index: int):
        """处理Material Design标签页切换"""
        try:
            tab_names = ["数据概览", "异动详情", "报告预览"]
            if 0 <= index < len(tab_names):
                print(f"Material标签切换到: {tab_names[index]} (索引: {index})")
                # 这里可以添加更多的标签切换处理逻辑
                
        except Exception as e:
            print(f"处理Material标签切换失败: {e}")
    
    def handle_responsive_change(self, breakpoint: str, config: Dict[str, Any]):
        """
        处理响应式变化
        
        Args:
            breakpoint: 断点名称
            config: 布局配置
        """
        self.logger.info(f"MainWorkspaceArea 响应式适配: {breakpoint}")
        
        # 1. 控制面板响应式优化
        self._apply_control_panel_responsive(breakpoint, config)
        
        # 2. 标签导航响应式适配
        if hasattr(self.tab_widget, 'handle_responsive_change'):
            self.tab_widget.handle_responsive_change(breakpoint, config)
        
        # 3. 表格组件响应式适配
        if hasattr(self, 'table_widget'):
            self._apply_table_responsive(breakpoint, config)
    
    def _apply_control_panel_responsive(self, breakpoint: str, config: Dict[str, Any]):
        """
        应用控制面板的响应式配置
        
        Args:
            breakpoint: 当前断点
            config: 响应式配置
        """
        if not hasattr(self, 'control_panel_widgets'):
            return
        
        # 获取配置参数
        button_size = config.get('button_size', 'medium')
        font_scale = config.get('font_scale', 1.0)
        padding_scale = config.get('padding_scale', 1.0)
        
        # 控制面板高度调整
        panel = self.control_panel_widgets['panel']
        base_height = 80
        new_height = int(base_height * padding_scale)
        panel.setFixedHeight(new_height)
        
        # 按钮大小响应式调整
        buttons = self.control_panel_widgets['buttons']
        for btn in buttons:
            btn.setProperty("button_size", button_size)
            # 刷新样式
            btn.style().unpolish(btn)
            btn.style().polish(btn)
        
        # 搜索输入框响应式调整
        search_input = self.control_panel_widgets['search_input']
        search_input.setProperty("font_scale", font_scale)
        
        # 根据断点调整搜索框的最小宽度
        search_widths = {
            'xs': 150,   # 超小屏幕，紧凑搜索框
            'sm': 180,   # 小屏幕
            'md': 200,   # 中等屏幕，默认宽度
            'lg': 250,   # 大屏幕，更宽的搜索框
            'xl': 300    # 超大屏幕，最宽搜索框
        }
        search_width = search_widths.get(breakpoint, 200)
        
        # 动态更新搜索框样式
        search_style = f"""
            QLineEdit#search_input {{
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: {int(8 * padding_scale)}px {int(12 * padding_scale)}px;
                font-size: {int(14 * font_scale)}px;
                background-color: white;
                min-width: {search_width}px;
            }}
        """
        search_input.setStyleSheet(search_style)
        
        # 图标按钮响应式调整
        icon_button = self.control_panel_widgets['icon_button']
        icon_button.setProperty("button_size", button_size)
        icon_button.style().unpolish(icon_button)
        icon_button.style().polish(icon_button)
        
        # xs断点下的特殊处理
        if breakpoint == 'xs':
            # 超小屏幕下隐藏部分按钮，只保留核心功能
            self.btn_export.setVisible(False)
            self.btn_refresh.setVisible(False)
            # 搜索框占位符简化
            search_input.setPlaceholderText("搜索...")
        else:
            # 其他断点下显示所有按钮
            self.btn_export.setVisible(True)
            self.btn_refresh.setVisible(True)
            search_input.setPlaceholderText("搜索员工姓名、部门...")
        
        # 布局间距调整
        if hasattr(self.control_panel, 'layout'):
            layout = self.control_panel.layout()
            if layout:
                base_spacing = 15
                new_spacing = int(base_spacing * padding_scale)
                layout.setSpacing(new_spacing)
                
                # 边距调整
                base_margins = [20, 10, 20, 10]
                new_margins = [int(m * padding_scale) for m in base_margins]
                layout.setContentsMargins(*new_margins)
        
        self.logger.debug(f"控制面板响应式适配完成: {breakpoint}, 按钮大小: {button_size}")
    
    def _apply_table_responsive(self, breakpoint: str, config: Dict[str, Any]):
        """
        应用表格的响应式配置
        
        Args:
            breakpoint: 当前断点
            config: 响应式配置
        """
        table_density = config.get('table_density', 'normal')
        
        # 如果有表格实例，应用密度设置
        if hasattr(self, 'table_widget') and hasattr(self.table_widget, 'set_density'):
            self.table_widget.set_density(table_density)
            self.logger.debug(f"表格密度调整: {table_density}")
    
    def get_current_responsive_state(self) -> Dict[str, Any]:
        """
        获取当前组件的响应式状态
        
        Returns:
            当前响应式状态信息
        """
        return {
            'component': 'MainWorkspaceArea',
            'control_panel_height': self.control_panel.height() if hasattr(self, 'control_panel') else None,
            'search_width': self.search_input.minimumWidth() if hasattr(self, 'search_input') else None,
            'buttons_visible': len([btn for btn in self.control_panel_widgets.get('buttons', []) if btn.isVisible()]) if hasattr(self, 'control_panel_widgets') else None
        }
    
    def _get_main_window(self):
        """🔧 [P0-修复] 获取主窗口引用"""
        try:
            # 向上查找主窗口
            parent = self.parent()
            while parent:
                if hasattr(parent, 'dynamic_table_manager') and hasattr(parent, '_apply_table_field_preference'):
                    return parent
                parent = parent.parent()
            return None
        except Exception as e:
            self.logger.error(f"获取主窗口引用失败: {e}")
            return None
    
    def _show_empty_table_with_prompt(self, prompt: str = None):
        """
        显示带有标准表头的空表格
        
        委托给主窗口的相应方法处理
        """
        try:
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, '_show_empty_table_with_prompt'):
                main_window._show_empty_table_with_prompt(prompt)
                self.logger.debug("已委托主窗口显示空表格提示")
            else:
                # 备用方案：直接处理
                self.logger.warning("无法获取主窗口引用，使用备用方案显示空表格")
                self._show_empty_table_fallback(prompt)
        except Exception as e:
            self.logger.error(f"显示空表格提示失败: {e}")
            # 最后的备用方案
            self._show_empty_table_fallback(prompt)
    
    def _show_empty_table_fallback(self, prompt: str = None):
        """备用方案：直接显示空表格 - 使用22个标准在职人员表头"""
        try:
            if hasattr(self, 'expandable_table'):
                # 🔧 [P0-修复] 使用完整的22个标准在职人员表头，与用户要求的标准保持一致
                standard_headers = [
                    "工号", "姓名", "部门名称", "人员类别代码", "人员类别",
                    "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴",
                    "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴",
                    "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发",
                    "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"
                ]
                empty_data = []
                self.expandable_table.set_data(empty_data, standard_headers)
                self.has_real_data = False
                self.logger.info(f"🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: {len(standard_headers)}个")
        except Exception as e:
            self.logger.error(f"备用方案显示空表格失败: {e}")

    def _request_brightness_fix(self):
        """🔧 [P0-修复] 请求亮度修复，发出信号给主窗口处理"""
        try:
            self.logger.debug("🔧 [P0-修复] MainWorkspaceArea请求亮度修复")
            self.brightness_fix_requested.emit()
        except Exception as e:
            self.logger.error(f"🔧 [P0-修复] 请求亮度修复失败: {e}")

    def _post_data_header_cleanup(self):
        """
        🔧 [P1-2] 数据设置完成后的表头重影清理

        在数据设置完成后检查并清理可能的表头重影问题
        """
        try:
            self.logger.debug("🔧 [P1-2] 开始数据设置后表头重影清理")

            # 获取当前表格组件
            current_table = None
            if hasattr(self, 'expandable_table') and self.expandable_table:
                current_table = self.expandable_table

            if not current_table:
                self.logger.debug("🔧 [P1-2] 未找到当前表格，跳过表头清理")
                return

            # 检测表头重影
            current_labels = []
            try:
                for col in range(current_table.columnCount()):
                    item = current_table.horizontalHeaderItem(col)
                    if item:
                        current_labels.append(item.text())
                    else:
                        current_labels.append("")
            except Exception as e:
                self.logger.debug(f"🔧 [P1-2] 获取表头标签失败: {e}")
                return

            # 获取主窗口的表头管理器
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'header_manager') and main_window.header_manager:
                duplicates = main_window.header_manager._detect_header_shadows(current_labels)
                if duplicates:
                    self.logger.warning(f"🔧 [P1-2] 数据设置后检测到表头重影: {duplicates}")

                    # 执行清理
                    table_id = f"post_data_{id(current_table)}"
                    main_window.header_manager.register_table(table_id, current_table)
                    main_window.header_manager.clear_table_header_immediately(table_id)

                    self.logger.info(f"🔧 [P1-2] 已执行数据设置后表头重影清理")
                else:
                    self.logger.debug("🔧 [P1-2] 数据设置后未检测到表头重影")

        except Exception as e:
            self.logger.error(f"🔧 [P1-2] 数据设置后表头重影清理失败: {e}")

    def _apply_button_style(self, button, button_type="primary"):
        """应用Material Design按钮样式"""
        try:
            self.logger.debug(f"[成功] 开始应用按钮样式: {button_type}")
            
            # 尝试获取样式管理器
            try:
                from src.gui.style_manager import StyleManager
                style_manager = StyleManager.get_instance()
                
                # 应用按钮样式
                style_key = f"button_{button_type}"
                if style_manager.apply_component_style(button, style_key):
                    # 应用Material Design阴影效果
                    try:
                        from src.gui.modern_style import ModernShadowEffects
                        shadow = ModernShadowEffects.create_button_shadow()
                        button.setGraphicsEffect(shadow)
                        self.logger.debug(f"[成功] 按钮样式和阴影应用成功: {button_type}")
                    except Exception as shadow_error:
                        self.logger.debug(f"[警告] 按钮阴影应用失败: {shadow_error}")
                else:
                    self.logger.warning(f"[警告] 按钮样式应用失败: {button_type}")
                
                # 设置按钮属性以配合Material Design样式
                if hasattr(button, 'setCursor'):
                    from PyQt5.QtCore import Qt
                    button.setCursor(Qt.PointingHandCursor)
                
            except Exception as style_error:
                self.logger.warning(f"[警告] StyleManager未初始化或不可用: {style_error}")
                # 应用备用样式
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #1976D2;
                        border: none;
                        border-radius: 6px;
                        padding: 8px 16px;
                        color: white;
                        font-size: 14px;
                        font-weight: 500;
                        min-width: 80px;
                    }
                    QPushButton:hover {
                        background-color: #1565C0;
                    }
                    QPushButton:pressed {
                        background-color: #0D47A1;
                    }
                """)
                
        except Exception as e:
            self.logger.error(f"[错误] 按钮样式应用失败 [{button_type}]: {e}")

    def _apply_table_style(self, table_widget):
        """应用Material Design表格样式"""
        try:
            self.logger.debug("[成功] 开始应用表格样式...")
            
            # 尝试获取样式管理器
            try:
                from src.gui.style_manager import StyleManager
                style_manager = StyleManager.get_instance()
                
                # 应用表格样式
                if style_manager.apply_component_style(table_widget, "table"):
                    self.logger.debug("[成功] 表格样式应用成功")
                
                # 如果表格有表头，也应用表头样式
                if hasattr(table_widget, 'horizontalHeader'):
                    header = table_widget.horizontalHeader()
                    if header:
                        style_manager.apply_component_style(header, "table_header")
                        self.logger.debug("[成功] 表头样式应用成功")
                
                # 设置表格属性以配合Material Design样式
                if hasattr(table_widget, 'setAlternatingRowColors'):
                    table_widget.setAlternatingRowColors(True)
                
                if hasattr(table_widget, 'setSelectionBehavior'):
                    from PyQt5.QtWidgets import QAbstractItemView
                    table_widget.setSelectionBehavior(QAbstractItemView.SelectRows)
                
                self.logger.debug("[成功] 表格Material Design样式应用完成")
                
            except Exception as style_error:
                self.logger.warning(f"[警告] StyleManager未初始化或不可用: {style_error}")
                # 应用备用表格样式
                table_widget.setStyleSheet("""
                    QTableWidget {
                        background-color: white;
                        border: 1px solid #e0e0e0;
                        border-radius: 4px;
                        selection-background-color: #e3f2fd;
                        alternate-background-color: #f5f5f5;
                    }
                    QTableWidget::item {
                        padding: 8px;
                        border-bottom: 1px solid #e0e0e0;
                    }
                    QTableWidget::item:selected {
                        background-color: #1976D2;
                        color: white;
                    }
                    QHeaderView::section {
                        background: qlineargradient(
                            x1: 0, y1: 0, x2: 0, y2: 1,
                            stop: 0 #FAFAFA,
                            stop: 0.5 #F5F5F5,
                            stop: 1 #E8E8E8
                        );
                        border: none;
                        border-bottom: 2px solid #E0E0E0;
                        border-right: 1px solid #E0E0E0;
                        padding: 10px 14px;
                        font-weight: 600;
                        color: #212121;
                        text-align: left;
                    }
                    QHeaderView::section:hover {
                        background: qlineargradient(
                            x1: 0, y1: 0, x2: 0, y2: 1,
                            stop: 0 rgba(66, 165, 245, 0.2),
                            stop: 0.5 rgba(66, 165, 245, 0.1),
                            stop: 1 rgba(66, 165, 245, 0.05)
                        );
                        color: #1976D2;
                        border-bottom: 2px solid #42A5F5;
                    }
                """)
                
        except Exception as e:
            self.logger.error(f"[错误] 表格样式应用失败: {e}")


class MaterialFooterWidget(QWidget):
    """
    Material Design风格Footer组件
    
    显示系统状态、版权信息等。
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(50)  # 默认高度
        self.setup_ui()
    
    def setup_ui(self):
        """
        设置Footer界面
        """
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)

        # 版权信息
        copyright_label = QLabel("© 2025 月度工资异动处理系统 - 高保真原型版本")
        copyright_label.setStyleSheet("color: #666; font-size: 12px;")

        # 导航路径信息（替代原来的"系统运行正常"）
        self.navigation_label = QLabel("就绪")
        self.navigation_label.setStyleSheet("color: #4CAF50; font-size: 12px; font-weight: bold;")

        layout.addWidget(copyright_label)
        layout.addStretch()
        layout.addWidget(self.navigation_label)
    
    def show_message(self, message: str, timeout: int = 0):
        """
        在状态栏显示消息。
        
        Args:
            message: 要显示的消息。
            timeout: 消息显示时间（毫秒），0表示永久显示直到下一次更新。
        """
        if hasattr(self, 'status_label'):
            self.status_label.setText(message)
            self.status_label.setStyleSheet("color: #2196F3; font-size: 12px; font-weight: bold;") # Info color
            
            # 如果设置了超时，则在超时后恢复默认消息
            if timeout > 0:
                # 确保在主线程中执行
                safe_single_shot(timeout, lambda: self._safe_clear_message())

    def clear_message(self):
        """清除消息，恢复默认状态。"""
        self._safe_clear_message()

    def _safe_clear_message(self):
        """线程安全的清除消息方法"""
        try:
            if hasattr(self, 'navigation_label'):
                self.navigation_label.setText("就绪")
                self.navigation_label.setStyleSheet("color: #4CAF50; font-size: 12px; font-weight: bold;") # Success color
        except Exception as e:
            self.logger.error(f"清除消息失败: {e}")

    def update_navigation_path(self, path: str):
        """
        更新导航路径显示

        Args:
            path: 当前导航路径，如 "工资表 > 2025年 > 06月 > 全部在职人员"
        """
        if hasattr(self, 'navigation_label'):
            if path:
                # 显示当前导航路径
                self.navigation_label.setText(f"📍 {path}")
                self.navigation_label.setStyleSheet("color: #2196F3; font-size: 12px; font-weight: bold;")
            else:
                # 没有路径时显示就绪状态
                self.navigation_label.setText("就绪")
                self.navigation_label.setStyleSheet("color: #4CAF50; font-size: 12px; font-weight: bold;")

    def handle_responsive_change(self, breakpoint: str, config: Dict[str, Any]):
        """
        处理响应式变化
        
        Args:
            breakpoint: 断点名称
            config: 布局配置
        """
        footer_height = config.get('footer_height', 50)
        self.setFixedHeight(footer_height)


class MenuBarManager(QObject):
    """
    菜单栏管理器
    
    负责管理隐藏式菜单栏的显示/隐藏、创建菜单结构、保存状态等功能
    """
    
    # 信号定义
    visibility_changed = pyqtSignal(bool)  # 菜单栏可见性改变信号
    menu_action_triggered = pyqtSignal(str)  # 菜单动作触发信号
    
    def __init__(self, main_window: QMainWindow, config_manager: 'ConfigManager'):
        super().__init__()
        self.main_window = main_window
        self.config_manager = config_manager
        self.logger = setup_logger(__name__ + ".MenuBarManager")
        
        # 菜单栏实例
        self.menu_bar = None
        self.is_visible = False
        
        # 动画
        self.show_animation = None
        self.hide_animation = None
        
        # 初始化菜单栏
        self._create_menu_bar()
        self._load_visibility_state()
        # 日志策略自动热更新（可选，需环境变量启用）
        self._setup_logging_policy_watch()
        
        self.logger.info("菜单栏管理器初始化完成")
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        try:
            self.menu_bar = self.main_window.menuBar()
            
            # 清空现有菜单以防止重复
            self.menu_bar.clear()
            
            # 创建菜单
            self._create_file_menu()
            self._create_data_menu()
            self._create_change_menu()
            self._create_report_menu()
            self._create_settings_menu()
            self._create_tools_menu()
            self._create_help_menu()
            
            # 设置菜单栏样式
            self._set_menu_bar_style()
            
            # 默认隐藏菜单栏
            self.menu_bar.setVisible(False)
            
            self.logger.info("菜单栏创建完成")
            
        except Exception as e:
            self.logger.error(f"创建菜单栏失败: {e}")
    
    def _create_file_menu(self):
        """创建文件菜单"""
        file_menu = self.menu_bar.addMenu('文件(&F)')
        
        # 导入数据
        import_action = QAction('导入数据(&I)', self.main_window)
        import_action.setShortcut('Ctrl+I')
        import_action.setStatusTip('导入Excel或CSV数据文件')
        import_action.triggered.connect(lambda: self._emit_menu_action('import_data'))
        file_menu.addAction(import_action)
        
        # 导出数据
        export_action = QAction('导出数据(&E)', self.main_window)
        export_action.setShortcut('Ctrl+E')
        export_action.setStatusTip('导出当前数据')
        export_action.triggered.connect(lambda: self._emit_menu_action('export_data'))
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 导入历史
        import_history_action = QAction('导入历史(&H)', self.main_window)
        import_history_action.setStatusTip('查看数据导入历史记录')
        import_history_action.triggered.connect(lambda: self._emit_menu_action('import_history'))
        file_menu.addAction(import_history_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出(&Q)', self.main_window)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.setStatusTip('退出系统')
        exit_action.triggered.connect(lambda: self._emit_menu_action('exit'))
        file_menu.addAction(exit_action)
    
    def _create_data_menu(self):
        """创建数据菜单"""
        data_menu = self.menu_bar.addMenu('数据(&D)')
        
        # 基准数据管理
        baseline_action = QAction('基准数据管理(&B)', self.main_window)
        baseline_action.setStatusTip('管理基准数据')
        baseline_action.triggered.connect(lambda: self._emit_menu_action('baseline_management'))
        data_menu.addAction(baseline_action)
        
        # 数据验证
        validate_action = QAction('数据验证(&V)', self.main_window)
        validate_action.setShortcut('Ctrl+Shift+V')
        validate_action.setStatusTip('验证数据的完整性和准确性')
        validate_action.triggered.connect(lambda: self._emit_menu_action('data_validation'))
        data_menu.addAction(validate_action)
        
        data_menu.addSeparator()

        # 表级字段偏好设置
        header_custom_action = QAction('表级字段偏好设置(&H)', self.main_window)
        header_custom_action.setShortcut('Ctrl+H')
        header_custom_action.setStatusTip('设置当前表的字段显示偏好')
        header_custom_action.triggered.connect(lambda: self._emit_menu_action('customize_headers'))
        data_menu.addAction(header_custom_action)

        # 重置表头
        reset_headers_action = QAction('重置表头(&R)', self.main_window)
        reset_headers_action.setStatusTip('重置表头为默认显示')
        reset_headers_action.triggered.connect(lambda: self._emit_menu_action('reset_headers'))
        data_menu.addAction(reset_headers_action)

        data_menu.addSeparator()

        # 数据备份
        backup_action = QAction('数据备份(&K)', self.main_window)
        backup_action.setStatusTip('备份当前数据')
        backup_action.triggered.connect(lambda: self._emit_menu_action('data_backup'))
        data_menu.addAction(backup_action)

        # 系统日志
        log_action = QAction('系统日志(&L)', self.main_window)
        log_action.setStatusTip('查看系统运行日志')
        log_action.triggered.connect(lambda: self._emit_menu_action('system_log'))
        data_menu.addAction(log_action)
    
    def _create_change_menu(self):
        """创建异动菜单"""
        change_menu = self.menu_bar.addMenu('异动(&C)')
        
        # 异动检测
        detect_action = QAction('异动检测(&D)', self.main_window)
        detect_action.setShortcut('F5')
        detect_action.setStatusTip('开始异动检测')
        detect_action.triggered.connect(lambda: self._emit_menu_action('change_detection'))
        change_menu.addAction(detect_action)
        
        change_menu.addSeparator()
        
        # 新增人员
        add_person_action = QAction('新增人员(&A)', self.main_window)
        add_person_action.setStatusTip('新增人员记录')
        add_person_action.triggered.connect(lambda: self._emit_menu_action('add_person'))
        change_menu.addAction(add_person_action)
        
        # 离职人员
        remove_person_action = QAction('离职人员(&R)', self.main_window)
        remove_person_action.setStatusTip('处理离职人员')
        remove_person_action.triggered.connect(lambda: self._emit_menu_action('remove_person'))
        change_menu.addAction(remove_person_action)
        
        # 工资调整
        salary_adjust_action = QAction('工资调整(&S)', self.main_window)
        salary_adjust_action.setStatusTip('处理工资调整')
        salary_adjust_action.triggered.connect(lambda: self._emit_menu_action('salary_adjustment'))
        change_menu.addAction(salary_adjust_action)
        
        change_menu.addSeparator()
        
        # 检测结果
        result_action = QAction('检测结果(&T)', self.main_window)
        result_action.setShortcut('Ctrl+R')
        result_action.setStatusTip('查看异动检测结果')
        result_action.triggered.connect(lambda: self._emit_menu_action('detection_results'))
        change_menu.addAction(result_action)
    
    def _create_report_menu(self):
        """创建报告菜单"""
        report_menu = self.menu_bar.addMenu('报告(&R)')
        
        # 生成Word报告
        word_report_action = QAction('生成Word报告(&W)', self.main_window)
        word_report_action.setShortcut('F6')
        word_report_action.setStatusTip('生成Word格式的异动报告')
        word_report_action.triggered.connect(lambda: self._emit_menu_action('generate_word_report'))
        report_menu.addAction(word_report_action)
        
        # 生成Excel分析表
        excel_report_action = QAction('生成Excel分析表(&E)', self.main_window)
        excel_report_action.setShortcut('F7')
        excel_report_action.setStatusTip('生成Excel格式的数据分析表')
        excel_report_action.triggered.connect(lambda: self._emit_menu_action('generate_excel_report'))
        report_menu.addAction(excel_report_action)
        
        report_menu.addSeparator()
        
        # 自定义报告
        custom_report_action = QAction('自定义报告(&C)', self.main_window)
        custom_report_action.setStatusTip('创建自定义报告')
        custom_report_action.triggered.connect(lambda: self._emit_menu_action('custom_report'))
        report_menu.addAction(custom_report_action)
        
        # 报告模板管理
        template_action = QAction('报告模板管理(&T)', self.main_window)
        template_action.setStatusTip('管理报告模板')
        template_action.triggered.connect(lambda: self._emit_menu_action('report_templates'))
        report_menu.addAction(template_action)
        
        report_menu.addSeparator()
        
        # 报告管理窗口
        manage_action = QAction('报告管理窗口(&M)', self.main_window)
        manage_action.setShortcut('F8')
        manage_action.setStatusTip('打开报告管理窗口')
        manage_action.triggered.connect(lambda: self._emit_menu_action('report_management'))
        report_menu.addAction(manage_action)
    
    def _create_settings_menu(self):
        """创建设置菜单"""
        settings_menu = self.menu_bar.addMenu('设置(&S)')
        
        # 用户设置
        user_settings_action = QAction('用户设置(&U)', self.main_window)
        user_settings_action.setStatusTip('用户个人设置')
        user_settings_action.triggered.connect(lambda: self._emit_menu_action('user_settings'))
        settings_menu.addAction(user_settings_action)
        
        # 系统设置
        system_settings_action = QAction('系统设置(&S)', self.main_window)
        system_settings_action.setStatusTip('系统配置和设置')
        system_settings_action.triggered.connect(lambda: self._emit_menu_action('system_settings'))
        settings_menu.addAction(system_settings_action)
        
        settings_menu.addSeparator()
        
        # 偏好设置
        preferences_action = QAction('偏好设置(&P)', self.main_window)
        preferences_action.setShortcut('Ctrl+,')
        preferences_action.setStatusTip('系统偏好设置')
        preferences_action.triggered.connect(lambda: self._emit_menu_action('preferences'))
        settings_menu.addAction(preferences_action)
        
        settings_menu.addSeparator()
        
        # 配置管理器
        config_manager_action = QAction('配置管理器(&C)', self.main_window)
        config_manager_action.setShortcut('Ctrl+Alt+C')
        config_manager_action.setStatusTip('管理系统配置文件和重置设置')
        config_manager_action.triggered.connect(self._open_config_manager)
        settings_menu.addAction(config_manager_action)

    def _create_tools_menu(self):
        """创建工具菜单（日志策略刷新与调试快照导出）"""
        tools_menu = self.menu_bar.addMenu('工具(&T)')

        action_refresh_logs = QAction('刷新日志策略并重建', self.main_window)
        def _refresh_logs():
            try:
                from src.utils.log_config import refresh_and_rebuild_logging, get_policy_path
                ok = refresh_and_rebuild_logging()
                msg = '成功' if ok else '失败'
                if self.main_window.statusBar():
                    self.main_window.statusBar().showMessage(f'日志策略刷新与重建：{msg}（{get_policy_path()}）', 5000)
            except Exception as e:
                if self.main_window.statusBar():
                    self.main_window.statusBar().showMessage(f'日志策略刷新异常：{e}', 5000)
        action_refresh_logs.triggered.connect(_refresh_logs)
        tools_menu.addAction(action_refresh_logs)

        action_export_ring = QAction('导出调试日志快照', self.main_window)
        def _export_ring():
            try:
                from src.utils.logging_utils import export_debug_ring_to_file
                path = export_debug_ring_to_file()
                if self.main_window.statusBar():
                    if path:
                        self.main_window.statusBar().showMessage(f'已导出调试快照: {path}', 8000)
                    else:
                        self.main_window.statusBar().showMessage('导出调试快照失败', 5000)
            except Exception as e:
                if self.main_window.statusBar():
                    self.main_window.statusBar().showMessage(f'导出调试快照异常：{e}', 5000)
        action_export_ring.triggered.connect(_export_ring)
        tools_menu.addAction(action_export_ring)

        # 打开调试日志查看器
        action_view_ring = QAction('打开调试日志查看器', self.main_window)
        def _open_ring_viewer():
            try:
                from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout
                from src.utils.logging_utils import get_debug_buffer
                class DebugRingViewer(QDialog):
                    def __init__(self, parent=None):
                        super().__init__(parent)
                        self.setWindowTitle('调试日志（内存 RingBuffer）')
                        self.resize(900, 600)
                        layout = QVBoxLayout(self)
                        self.text = QTextEdit(self)
                        self.text.setReadOnly(True)
                        layout.addWidget(self.text)
                        btns = QHBoxLayout()
                        export_btn = QPushButton('导出到文件', self)
                        refresh_btn = QPushButton('刷新', self)
                        close_btn = QPushButton('关闭', self)
                        btns.addWidget(export_btn)
                        btns.addWidget(refresh_btn)
                        btns.addStretch(1)
                        btns.addWidget(close_btn)
                        layout.addLayout(btns)
                        def _export():
                            try:
                                from src.utils.logging_utils import export_debug_ring_to_file
                                path = export_debug_ring_to_file()
                                if path and self.parent() and self.parent().statusBar():
                                    self.parent().statusBar().showMessage(f'已导出调试快照: {path}', 8000)
                            except Exception:
                                pass
                        def _refresh():
                            try:
                                lines = get_debug_buffer().snapshot()
                                self.text.setPlainText('\n'.join(lines))
                            except Exception:
                                self.text.setPlainText('[无法读取 RingBuffer]')
                        export_btn.clicked.connect(_export)
                        refresh_btn.clicked.connect(_refresh)
                        close_btn.clicked.connect(self.accept)
                        _refresh()
                viewer = DebugRingViewer(self.main_window)
                viewer.exec_()
            except Exception as e:
                if self.main_window.statusBar():
                    self.main_window.statusBar().showMessage(f'打开调试日志查看器异常：{e}', 5000)
        action_view_ring.triggered.connect(_open_ring_viewer)
        tools_menu.addAction(action_view_ring)
    
    def _open_config_manager(self):
        """打开配置管理器对话框"""
        try:
            from src.gui.dialogs.config_manager_dialog import ConfigManagerDialog
            
            dialog = ConfigManagerDialog(self.main_window)
            dialog.config_changed.connect(self._on_config_changed)
            
            if dialog.exec_() == QDialog.Accepted:
                # 配置已保存，可以选择重启应用或重新加载配置
                reply = QMessageBox.question(
                    self.main_window,
                    "配置已更新",
                    "配置已成功保存。\n某些配置更改需要重启应用才能生效。\n\n是否现在重启应用？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    # 重启应用
                    import sys
                    import os
                    os.execl(sys.executable, sys.executable, *sys.argv)
                else:
                    # 尝试重新加载部分配置
                    self._reload_configs()
                    
        except Exception as e:
            self.logger.error(f"打开配置管理器失败: {e}")
            QMessageBox.warning(
                self.main_window,
                "错误",
                f"无法打开配置管理器：{e}"
            )
    
    def _on_config_changed(self, config_name: str, config_data: dict):
        """处理配置更改事件"""
        try:
            self.logger.info(f"配置 {config_name} 已更改")
            
            # 根据配置类型进行相应的处理
            if config_name == 'main_config':
                self._apply_main_config_changes(config_data)
            elif config_name == 'user_preferences':
                self._apply_user_preferences_changes(config_data)
            elif config_name == 'field_mappings':
                self._apply_field_mappings_changes(config_data)
                
        except Exception as e:
            self.logger.error(f"应用配置更改失败: {e}")
    
    def _apply_main_config_changes(self, config_data: dict):
        """应用主配置更改"""
        try:
            # 更新日志级别
            if 'log_level' in config_data:
                # 这里可以更新日志级别
                pass
                
            # 更新UI设置
            if 'ui' in config_data:
                ui_config = config_data['ui']
                if 'window_width' in ui_config and 'window_height' in ui_config:
                    self.main_window.resize(ui_config['window_width'], ui_config['window_height'])
                    
        except Exception as e:
            self.logger.error(f"应用主配置更改失败: {e}")
    
    def _apply_user_preferences_changes(self, config_data: dict):
        """应用用户偏好更改"""
        try:
            # 更新界面偏好
            if 'ui' in config_data:
                ui_prefs = config_data['ui']
                
                # 更新字体
                if 'font_family' in ui_prefs and 'font_size' in ui_prefs:
                    from PyQt5.QtGui import QFont
                    font = QFont(ui_prefs['font_family'], ui_prefs['font_size'])
                    self.main_window.setFont(font)
                    
                # 更新窗口大小
                if 'window_size' in ui_prefs:
                    size = ui_prefs['window_size']
                    self.main_window.resize(size['width'], size['height'])
                    
        except Exception as e:
            self.logger.error(f"应用用户偏好更改失败: {e}")
    
    def _apply_field_mappings_changes(self, config_data: dict):
        """应用字段映射更改"""
        try:
            # 清除字段映射缓存
            if hasattr(self, '_field_processing_cache'):
                self._field_processing_cache.clear()
                
            # 重新加载当前显示的数据以应用新的字段映射
            # 🔧 [P0-2] 从主窗口获取current_table_name，统一状态管理
            main_window = self._get_main_window()
            current_table_name = main_window.current_table_name if main_window else ""
            if current_table_name:
                # 可以选择重新加载当前表格数据
                self.logger.info("字段映射已更新，建议重新加载数据以查看更改")
                
        except Exception as e:
            self.logger.error(f"应用字段映射更改失败: {e}")
    
    def _reload_configs(self):
        """重新加载配置文件"""
        try:
            # 重新加载配置管理器
            if hasattr(self, 'config_manager'):
                self.config_manager.load_config()
                
            # 重新加载用户偏好
            if hasattr(self, 'user_preferences'):
                self.user_preferences.load_preferences()
                
            self.logger.info("配置文件重新加载完成")
            
        except Exception as e:
            self.logger.error(f"重新加载配置失败: {e}")
    
    def _create_help_menu(self):
        """创建帮助菜单"""
        help_menu = self.menu_bar.addMenu('帮助(&H)')
        
        # 用户手册
        manual_action = QAction('用户手册(&M)', self.main_window)
        manual_action.setShortcut('F1')
        manual_action.setStatusTip('查看用户手册')
        manual_action.triggered.connect(lambda: self._emit_menu_action('user_manual'))
        help_menu.addAction(manual_action)
        
        # 日志策略与调试指南
        logging_guide_action = QAction('日志策略与调试指南', self.main_window)
        logging_guide_action.setStatusTip('查看日志治理策略、慢SQL与RingBuffer使用说明')
        def _open_logging_guide():
            try:
                import os as _os
                from pathlib import Path as _Path
                # 计算项目根目录（本文件 -> prototype -> gui -> src -> project_root）
                project_root = _Path(__file__).resolve().parents[3]
                guide_path = project_root / 'docs' / 'logging_policy_guide.md'
                if guide_path.exists():
                    try:
                        _os.startfile(str(guide_path))  # Windows
                    except Exception:
                        # 其他平台兜底
                        import subprocess
                        subprocess.Popen(['xdg-open', str(guide_path)])
                else:
                    self.logger.warning(f"日志策略指南不存在: {guide_path}")
            except Exception as e:
                self.logger.error(f"打开日志策略指南失败: {e}")
        logging_guide_action.triggered.connect(_open_logging_guide)
        help_menu.addAction(logging_guide_action)
        
        help_menu.addSeparator()
        
        # 关于
        about_action = QAction('关于(&A)', self.main_window)
        about_action.setStatusTip('关于本系统')
        about_action.triggered.connect(lambda: self._emit_menu_action('about'))
        help_menu.addAction(about_action)
    
    def _setup_logging_policy_watch(self):
        """可选启用：监控日志策略文件变化并自动重建 sinks。
        通过环境变量 LOG_POLICY_WATCH=1 启用，默认关闭以避免生产频繁重建。
        """
        try:
            import os as _os
            from PyQt5.QtCore import QFileSystemWatcher
            if str(_os.getenv('LOG_POLICY_WATCH', '0')) != '1':
                return
            from src.utils.log_config import get_policy_path, refresh_and_rebuild_logging
            policy_path = str(get_policy_path())
            if not policy_path:
                return
            self._policy_watcher = QFileSystemWatcher(self)
            self._policy_watcher.addPath(policy_path)
            def _on_changed(_):
                try:
                    ok = refresh_and_rebuild_logging()
                    if self.main_window.statusBar():
                        self.main_window.statusBar().showMessage(
                            f'检测到日志策略变更，已自动重建：{"成功" if ok else "失败"}', 5000)
                except Exception:
                    pass
            self._policy_watcher.fileChanged.connect(_on_changed)
            self.logger.info(f"日志策略文件监听已启用: {policy_path}")
        except Exception as e:
            self.logger.debug(f"日志策略监听未启用或失败: {e}")
    def _set_menu_bar_style(self):
        """设置菜单栏样式"""
        if self.menu_bar:
            style = """
                QMenuBar {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    padding: 4px;
                    font-size: 14px;
                }
                
                QMenuBar::item {
                    background-color: transparent;
                    padding: 8px 12px;
                    border-radius: 4px;
                    margin: 0px 2px;
                }
                
                QMenuBar::item:selected {
                    background-color: #1976D2;
                }
                
                QMenuBar::item:pressed {
                    background-color: #1565C0;
                }
                
                QMenu {
                    background-color: white;
                    color: #212121;
                    border: 1px solid #E0E0E0;
                    border-radius: 4px;
                    padding: 4px;
                }
                
                QMenu::item {
                    padding: 8px 24px;
                    border-radius: 4px;
                    margin: 1px;
                }
                
                QMenu::item:selected {
                    background-color: #E3F2FD;
                    color: #1976D2;
                }
                
                QMenu::separator {
                    height: 1px;
                    background-color: #E0E0E0;
                    margin: 4px 8px;
                }
            """
            self.menu_bar.setStyleSheet(style)
    
    def _emit_menu_action(self, action_name: str):
        """发出菜单动作信号"""
        self.logger.debug(f"菜单动作触发: {action_name}")
        self.menu_action_triggered.emit(action_name)
    
    def toggle_visibility(self):
        """切换菜单栏显示状态"""
        if self.is_visible:
            self.hide_menu_bar()
        else:
            self.show_menu_bar()
    
    def show_menu_bar(self):
        """
        显示菜单栏
        """
        try:
            if self.menu_bar:
                self.menu_bar.setVisible(True)
                self.is_visible = True
                
                # 更新Header中设置按钮的状态
                if hasattr(self.main_window, 'header_widget'):
                    self.main_window.header_widget.update_settings_button_state(True)
                
                # 保存状态
                self._save_visibility_state()
                
                # 刷新主窗口布局 - 关键步骤
                self._refresh_main_window_layout()
                
                # 发射信号
                self.visibility_changed.emit(True)
                
                self.logger.info("菜单栏已显示")
                
        except Exception as e:
            self.logger.error(f"显示菜单栏失败: {e}")

    def hide_menu_bar(self):
        """
        隐藏菜单栏
        """
        try:
            if self.menu_bar:
                self.menu_bar.setVisible(False)
                self.is_visible = False
                
                # 更新Header中设置按钮的状态
                if hasattr(self.main_window, 'header_widget'):
                    self.main_window.header_widget.update_settings_button_state(False)
                
                # 保存状态
                self._save_visibility_state()
                
                # 刷新主窗口布局 - 关键步骤
                self._refresh_main_window_layout()
                
                # 发射信号
                self.visibility_changed.emit(False)
                
                self.logger.info("菜单栏已隐藏")
                
        except Exception as e:
            self.logger.error(f"隐藏菜单栏失败: {e}")

    def _refresh_main_window_layout(self):
        """
        刷新主窗口布局
        
        确保菜单栏显示/隐藏后，所有组件都能正确适应新的空间分配
        """
        try:
            if self.main_window:
                # 1. 暂停响应式管理器以避免冲突
                if hasattr(self.main_window, 'responsive_manager') and self.main_window.responsive_manager:
                    if hasattr(self.main_window.responsive_manager, 'pause_updates'):
                        self.main_window.responsive_manager.pause_updates()
                
                # 2. 清除表头缓存，防止按钮重影
                self._clear_table_header_cache()
                
                # 3. 强制更新主窗口布局
                self.main_window.update()
                
                # 4. 延迟执行详细布局刷新，确保组件稳定
                # 改为合并调度
                self._schedule_ui_fix(200)
                
                # 5. 重新启动响应式管理器
                if hasattr(self.main_window, 'responsive_manager') and self.main_window.responsive_manager:
                    if hasattr(self.main_window.responsive_manager, 'resume_updates'):
                        safe_single_shot(150, self.main_window.responsive_manager.resume_updates)
                    elif hasattr(self.main_window.responsive_manager, 'force_update'):
                        safe_single_shot(150, self.main_window.responsive_manager.force_update)
                
                self.logger.debug("主窗口布局刷新完成")
                
        except Exception as e:
            self.logger.error(f"刷新主窗口布局失败: {e}")
    
    def _clear_table_header_cache(self):
        """
        清除表头缓存，解决按钮重影问题
        """
        try:
            # 查找所有表格组件并清除表头缓存
            tables = self.main_window.findChildren(QTableWidget)
            
            for table in tables:
                # 清除表头缓存
                h_header = table.horizontalHeader()
                v_header = table.verticalHeader()
                
                if h_header:
                    # 优化：只需要update()，让Qt决定重绘时机
                    h_header.updateGeometry()
                    h_header.update()
                    
                    # 清除选择状态避免重影
                    if hasattr(h_header, 'clearSelection'):
                        h_header.clearSelection()
                
                if v_header:
                    v_header.updateGeometry()
                    v_header.update()
                
                # 清除表格视口缓存
                viewport = table.viewport()
                if viewport:
                    viewport.update()
                
                self.logger.debug(f"已清除表格 {table.objectName()} 的表头缓存")
                
        except Exception as e:
            self.logger.error(f"清除表头缓存失败: {e}")
    
    def _delayed_layout_refresh(self):
        """
        延迟布局刷新，确保组件稳定后再更新
        """
        try:
            # 1. 更新中央部件
            central_widget = self.main_window.centralWidget()
            if central_widget:
                central_widget.updateGeometry()
                central_widget.update()
            
            # 2. 更新主要组件（避免重复更新）
            components_to_update = [
                ('main_workspace', getattr(self.main_window, 'main_workspace', None)),
                ('navigation_panel', getattr(self.main_window, 'navigation_panel', None)),
            ]
            
            for name, component in components_to_update:
                if component:
                    component.updateGeometry()
                    component.update()
                    
                    # 特殊处理表格组件
                    if hasattr(component, 'expandable_table'):
                        table = component.expandable_table
                        if table and isinstance(table, QTableWidget):
                            self._refresh_single_table(table)
                    
                    self.logger.debug(f"已更新组件: {name}")
            
            # 3. 最后清理一次表头
            # 合并进统一调度，由表头前置检测决定是否清理
            self._schedule_ui_fix(220)
            
        except Exception as e:
            self.logger.error(f"延迟布局刷新失败: {e}")
    
    def _refresh_single_table(self, table):
        """
        刷新单个表格组件
        
        Args:
            table: 表格组件
        """
        try:
            # 1. 更新表格本身
            table.updateGeometry()
            table.update()
            
            # 2. 重新设置表头模式以避免重影
            h_header = table.horizontalHeader()
            if h_header:
                # 保存当前设置
                current_resize_mode = QHeaderView.Stretch
                if table.columnCount() > 0:
                    try:
                        current_resize_mode = h_header.sectionResizeMode(0)
                    except (AttributeError, RuntimeError):
                        current_resize_mode = QHeaderView.Stretch
                
                # 重新应用设置
                h_header.setSectionResizeMode(current_resize_mode)
                h_header.setStretchLastSection(True)
                
                # 强制更新
                h_header.updateGeometry()
                h_header.update()
            
            # 3. 更新视口
            viewport = table.viewport()
            if viewport:
                viewport.update()
            
            self.logger.debug("单个表格刷新完成")
            
        except Exception as e:
            self.logger.error(f"刷新单个表格失败: {e}")
    
    def _final_header_cleanup(self):
        """
        最终表头清理，确保没有重影
        """
        try:
            tables = self.main_window.findChildren(QTableWidget)
            
            for table in tables:
                # 最后一次强制重绘
                h_header = table.horizontalHeader()
                if h_header:
                    h_header.repaint()
                
                v_header = table.verticalHeader()
                if v_header:
                    v_header.repaint()
                
                # 确保视口清洁
                viewport = table.viewport()
                if viewport:
                    viewport.repaint()
            
            self.logger.debug("最终表头清理完成")
            
        except Exception as e:
            self.logger.error(f"最终表头清理失败: {e}")
    
    def _save_visibility_state(self):
        """保存菜单栏显示状态"""
        try:
            # 保存到用户偏好设置
            from src.modules.system_config.user_preferences import UserPreferences
            user_prefs = UserPreferences(self.config_manager)
            user_prefs.set_preference("ui.show_menubar", self.is_visible)
            
            self.logger.debug(f"菜单栏显示状态已保存: {self.is_visible}")
            
        except Exception as e:
            self.logger.error(f"保存菜单栏状态失败: {e}")
    
    def _load_visibility_state(self):
        """加载菜单栏显示状态"""
        try:
            # 从用户偏好设置加载
            from src.modules.system_config.user_preferences import UserPreferences
            user_prefs = UserPreferences(self.config_manager)
            self.is_visible = user_prefs.get_preference("ui.show_menubar", False)  # 默认隐藏
            
            if self.menu_bar:
                self.menu_bar.setVisible(self.is_visible)
            
            self.logger.debug(f"菜单栏显示状态已加载: {self.is_visible}")
            
        except Exception as e:
            self.logger.error(f"加载菜单栏状态失败: {e}")
            self.is_visible = False  # 默认隐藏


# 🔧 [CRITICAL修复] 导入事件循环保护
from functools import wraps
import time
from typing import Dict, Set

class EventLoopGuard:
    """防止无限事件循环的保护机制"""
    
    def __init__(self):
        self._processing_events: Set[str] = set()
        self._call_counts: Dict[str, int] = {}
        self._last_call_time: Dict[str, float] = {}
        self._max_calls_per_second = 10
    
    def can_process(self, event_key: str) -> bool:
        """检查是否可以处理事件"""
        current_time = time.time()
        
        # 检查是否正在处理
        if event_key in self._processing_events:
            return False
        
        # 检查调用频率
        last_time = self._last_call_time.get(event_key, 0)
        if current_time - last_time < 1.0:  # 1秒内
            count = self._call_counts.get(event_key, 0)
            if count >= self._max_calls_per_second:
                return False
            self._call_counts[event_key] = count + 1
        else:
            self._call_counts[event_key] = 1
            
        self._last_call_time[event_key] = current_time
        return True
    
    def start_processing(self, event_key: str):
        """开始处理事件"""
        self._processing_events.add(event_key)
    
    def end_processing(self, event_key: str):
        """结束处理事件"""
        self._processing_events.discard(event_key)

_event_guard = EventLoopGuard()

def prevent_loop(event_key: str):
    """防止事件循环的装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not _event_guard.can_process(event_key):
                return None
            _event_guard.start_processing(event_key)
            try:
                return func(*args, **kwargs)
            finally:
                _event_guard.end_processing(event_key)
        return wrapper
    return decorator


class PrototypeMainWindow(QMainWindow):
    """
    PyQt5重构高保真原型主窗口
    
    实现完整的三段式布局、响应式设计、Material Design风格。
    """
    
    # 窗口信号
    window_resized = pyqtSignal(QSize)

    # 核心服务信号
    import_requested = pyqtSignal(str) # 参数为建议的目标路径

    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, dynamic_table_manager: DynamicTableManager):
        super().__init__()
        self.logger = setup_logger(__name__)
        
        # --- 管理器注入 ---
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.dynamic_table_manager = dynamic_table_manager
        self.table_manager = dynamic_table_manager  # 🔧 [全局排序] 为了兼容性，添加简短别名
        
        # 初始化排序状态管理器
        self.sort_state_manager = TableSortStateManager()

        # 🔧 [P2-3] 初始化错误处理管理器
        self.error_handler = get_error_handler()
        self._setup_error_handling()

        # 🔧 [P1-2] 初始化递归调用防护机制
        self._init_recursive_call_protection()

        # --- 🆕 新架构核心组件初始化 ---
        try:
            # 导入新架构组件
            from src.core.architecture_factory import get_architecture_factory
            
            # 初始化架构工厂
            self.architecture_factory = get_architecture_factory(
                self.dynamic_table_manager, 
                self.config_manager
            )
            
            # 🆕 [新架构] 初始化新架构系统（必须成功）
            if not self.architecture_factory.initialize_architecture():
                raise RuntimeError("新架构初始化失败，系统无法启动")

            # 获取新架构组件
            self.table_data_service = self.architecture_factory.get_table_data_service()
            self.event_bus = self.architecture_factory.get_event_bus()
            self.state_manager = self.architecture_factory.get_unified_state_manager()
            self.data_request_manager = self.architecture_factory.get_unified_data_request_manager()
            self.config_sync_manager = self.architecture_factory.get_config_sync_manager()

            # 🚀 [性能优化] 初始化性能管理器
            from src.gui.prototype.performance import get_performance_manager
            self.performance_manager = get_performance_manager()
            self.logger.info("🚀 性能管理器已集成")

            self.logger.info("✅ 新架构集成成功！")

            # 设置事件监听器
            self._setup_new_architecture_listeners()

        except Exception as e:
            self.logger.error(f"❌ 新架构集成失败: {e}")
            raise RuntimeError(f"新架构集成失败，系统无法启动: {e}")
        
        # --- 🔧 [P1-完成] 旧架构依赖已完全移除 ---
        
        # 初始化分页缓存管理器
        self.pagination_cache = PaginationCacheManager(
            max_cache_entries=50,
            max_memory_mb=50,
            ttl_seconds=300
        )
        
        # 🔧 [全局排序] 全局排序功能开关和状态
        self.global_sort_enabled = True  # 默认启用全局排序
        self.current_sort_columns = []   # 当前排序列
        self.field_mapping_cache = {}    # 字段映射缓存（中文->英文）

        # 确保current_table_name属性正确初始化
        self.current_table_name = ""     # 当前表名，用于排序和分页功能
        
        # 设置线程池
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(QThreadPool.globalInstance().maxThreadCount())

        # 设置管理器
        self._setup_managers()

        # 🔧 [P0-修复] 应用窗口级Material Design样式
        self._apply_window_style()
        
        # 设置UI
        self._setup_ui()
        
        # 设置信号连接
        self._setup_connections()

        # 加载初始数据
        self._load_field_mappings_from_file()
        
        # 初始加载数据
        if not self.main_workspace.set_data(None):
            self._show_empty_table_with_prompt("欢迎使用，请从左侧导航选择或导入数据")

        # 🔧 [P1-移除旧架构] 完全移除兼容性代码
        # 删除：self._setup_legacy_compatibility()
        
        self.logger.info("原型主窗口初始化完成")
    
    def _setup_new_architecture_listeners(self):
        """设置新架构的事件监听器"""
        try:
            if not self.event_bus:
                self.logger.warning("事件总线不可用，跳过事件监听器设置")
                return
            
            # 重新注入ConfigSyncManager到已创建的VirtualizedExpandableTable实例
            self._inject_config_sync_manager_to_existing_tables()
            
            # 监听数据更新事件
            self.event_bus.subscribe(
                "data_updated",
                self._on_new_data_updated,
                async_handler=False
            )
            
            # 监听错误事件
            self.event_bus.subscribe(
                "error",
                self._on_new_error_event,
                async_handler=False
            )
            
            self.logger.info("✅ 新架构事件监听器设置完成")
            
        except Exception as e:
            self.logger.error(f"设置新架构事件监听器失败: {e}")
    
    def _inject_config_sync_manager_to_existing_tables(self):
        """向已创建的表格实例重新注入ConfigSyncManager"""
        try:
            if not hasattr(self, 'config_sync_manager') or not self.config_sync_manager:
                self.logger.warning("ConfigSyncManager未初始化，跳过重新注入")
                return
            
            # 查找并更新所有VirtualizedExpandableTable实例
            tables_updated = 0
            
            # 检查expandable_table（如果存在）
            if hasattr(self, 'expandable_table') and self.expandable_table:
                self.expandable_table.config_sync_manager = self.config_sync_manager
                # 同时更新header_edit_manager
                self.expandable_table.header_edit_manager.config_sync_manager = self.config_sync_manager
                tables_updated += 1
                self.logger.debug("✅ expandable_table的ConfigSyncManager已更新")
            
            # 检查main_workspace.data_table（如果存在）
            if hasattr(self, 'main_workspace') and hasattr(self.main_workspace, 'data_table') and self.main_workspace.data_table:
                self.main_workspace.data_table.config_sync_manager = self.config_sync_manager
                if hasattr(self.main_workspace.data_table, 'header_edit_manager'):
                    self.main_workspace.data_table.header_edit_manager.config_sync_manager = self.config_sync_manager
                tables_updated += 1
                self.logger.debug("✅ main_workspace.data_table的ConfigSyncManager已更新")
            
            # 递归查找其他可能的表格实例
            tables_updated += self._recursive_find_and_update_tables(self)
            
            self.logger.info(f"ConfigSyncManager重新注入完成，已更新{tables_updated}个表格实例")
            
        except Exception as e:
            self.logger.error(f"重新注入ConfigSyncManager失败: {e}")
    
    def _recursive_find_and_update_tables(self, widget, level=0):
        """递归查找并更新VirtualizedExpandableTable实例"""
        try:
            updated_count = 0
            max_depth = 5  # 限制递归深度避免性能问题
            
            if level > max_depth:
                return updated_count
            
            # 检查当前widget是否是VirtualizedExpandableTable
            from .widgets.virtualized_expandable_table import VirtualizedExpandableTable
            if isinstance(widget, VirtualizedExpandableTable):
                if hasattr(widget, 'config_sync_manager'):
                    widget.config_sync_manager = self.config_sync_manager
                    if hasattr(widget, 'header_edit_manager'):
                        widget.header_edit_manager.config_sync_manager = self.config_sync_manager
                    updated_count += 1
                    self.logger.debug(f"✅ 发现并更新了VirtualizedExpandableTable实例 (层级{level})")
            
            # 递归检查子组件
            if hasattr(widget, 'children'):
                for child in widget.children():
                    if hasattr(child, '__class__'):
                        updated_count += self._recursive_find_and_update_tables(child, level + 1)
            
            return updated_count
            
        except Exception as e:
            self.logger.error(f"递归查找表格实例失败: {e}")
            return 0
    
    @prevent_loop("data_updated")
    def _on_new_data_updated(self, event):
        """🆕 处理新架构数据更新事件"""
        try:
            # 防止事件循环
            if hasattr(self, '_processing_data_update') and self._processing_data_update:
                self.logger.debug("正在处理数据更新，跳过重复事件")
                return

            self._processing_data_update = True
            
            # 🔧 [CRITICAL修复] 初始化操作上下文
            if not hasattr(self, '_current_operation_context'):
                self._current_operation_context = {}

            # 使用正确的属性访问方式
            table_name = getattr(event, 'table_name', '')
            data = getattr(event, 'data', [])
            metadata = getattr(event, 'metadata', {})

            self.logger.info(f"🆕 接收到新架构数据更新事件: {table_name}")
            
            # 安全地获取数据尺寸信息
            data_len = 0
            data_cols = 0
            if data is not None:
                # 检查DataFrame
                if hasattr(data, 'shape'):
                    data_len, data_cols = data.shape
                # 检查list
                elif isinstance(data, list):
                    data_len = len(data)
                    if len(data) > 0:
                        first_row = data[0]
                        if isinstance(first_row, dict):
                            data_cols = len(first_row)
                        elif hasattr(first_row, '__len__'):
                            data_cols = len(first_row)
                # 其他情况尝试获取长度
                else:
                    try:
                        data_len = len(data)
                    except (AttributeError, TypeError):
                        data_len = 0
            
            self.logger.info(f"数据内容: {data_len}行 x {data_cols}列")
            
            # 🔧 [分页修复] 获取真实的总记录数
            total_records = metadata.get('total_records', 0)
            current_page = metadata.get('current_page', 1)
            page_size = metadata.get('page_size', 50)
            
            # 如果metadata中没有总记录数，从服务层获取
            if total_records == 0 and table_name:
                try:
                    total_records = self.table_data_service.get_table_record_count(table_name)
                    self.logger.info(f"🔧 [分页修复] 从服务层获取总记录数: {total_records}")
                except Exception as e:
                    self.logger.warning(f"🔧 [分页修复] 获取总记录数失败: {e}")
                    total_records = data_len  # 后备方案
            
            # 🔧 [CRITICAL修复] 设置操作上下文，用于区分操作类型
            request_type = metadata.get('request_type', '')
            self._current_operation_context = {'operation_type': request_type}
            
            is_pagination_or_sort = request_type in ['page_change', 'sort_change']
            
            if is_pagination_or_sort:
                self.logger.info(f"🔧 [关键修复] 接收到{request_type}操作的数据更新事件，开始UI更新: {table_name}, {data_len}行")
                # 🔧 [关键修复] 事件驱动的更新应该被允许，不跳过
            else:
                self.logger.info(f"🔧 [数据更新] 接收到{request_type}操作的数据更新事件: {table_name}, {data_len}行")
            
            # 🔧 [统一数据设置] 统一的数据设置路径，确保所有操作都能正确更新UI
            try:
                # 应用字段映射和过滤
                if hasattr(data, 'columns'):
                    # DataFrame格式
                    df_processed = data
                else:
                    # 转换为DataFrame
                    import pandas as pd
                    df_processed = pd.DataFrame(data)
                
                if not df_processed.empty:
                    # 应用字段映射
                    df_mapped = self._apply_field_mapping_to_dataframe(df_processed, table_name)
                    df_filtered = self._apply_system_field_filtering(df_mapped, table_name)
                    
                    try:
                        df_final = self._apply_table_field_preference(df_filtered, table_name)
                    except Exception as e:
                        self.logger.warning(f"应用表格字段偏好失败，使用过滤后数据: {e}")
                        df_final = df_filtered
                    
                    # 🚀 [排序优化] 检测排序操作，标记同步优化
                    if is_pagination_or_sort and request_type == 'sort_change':
                        self.logger.info("🚀 [排序优化] 检测到排序操作，标记为高优先级同步更新")
                        # 设置排序操作标记，供表格组件快速检测
                        if hasattr(self.main_workspace, 'expandable_table'):
                            setattr(self.main_workspace.expandable_table, '_current_sort_operation', True)
                    
                    # 🔧 [P1-1修复] 设置到主工作区，确保使用新映射的表头
                    self.main_workspace.set_data(
                        df_final, 
                        preserve_headers=False,  # 🔧 [P1-1修复] 不保留旧表头，使用新映射的中文表头
                        table_name=table_name, 
                        current_table_name=table_name
                    )
                    
                    # 🚀 [排序优化] 清除排序操作标记
                    if is_pagination_or_sort and request_type == 'sort_change':
                        if hasattr(self.main_workspace, 'expandable_table'):
                            setattr(self.main_workspace.expandable_table, '_current_sort_operation', False)
                    
                    self.logger.info(f"🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: {len(df_final)}行, {len(df_final.columns)}列")
                    
                    # 🔧 [P0-新5修复] 强制刷新表头显示，传递必需参数
                    try:
                        if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                            if hasattr(self.main_workspace.expandable_table, 'header_update_manager'):
                                # 🔧 [P0-新5修复] 准备必需参数
                                headers = list(df_final.columns)
                                start_record = 1  # 非分页模式起始记录为1
                                count = len(df_final)
                                
                                self.main_workspace.expandable_table.header_update_manager.force_update_all(
                                    headers=headers, 
                                    start_record=start_record, 
                                    count=count
                                )
                                self.logger.info(f"🔧 [P0-新5修复] 表头强制刷新完成: {table_name}, 传递参数: {len(headers)}个表头")
                    except Exception as header_error:
                        self.logger.warning(f"🔧 [P0-新5修复] 表头刷新失败: {header_error}")
                    
                    self.logger.info(f"🔧 [统一数据设置] 数据已成功设置到UI: {len(df_final)}行, {len(df_final.columns)}列")
                    
            except Exception as processing_error:
                self.logger.error(f"🔧 [统一数据设置] 数据处理失败: {processing_error}")
                # 🔧 [P1-1修复] fallback：也要尝试应用字段映射
                try:
                    if hasattr(data, 'columns'):
                        fallback_mapped = self._apply_field_mapping_to_dataframe(data, table_name)
                        self.main_workspace.set_data(fallback_mapped, preserve_headers=False, table_name=table_name, current_table_name=table_name)
                        self.logger.info(f"🔧 [P1-1修复] fallback模式应用字段映射成功")
                    else:
                        self.main_workspace.set_data(data, table_name=table_name, current_table_name=table_name)
                except Exception as fallback_error:
                    self.logger.error(f"🔧 [P1-1修复] fallback映射也失败: {fallback_error}")
                    self.main_workspace.set_data(data, table_name=table_name, current_table_name=table_name)
            
            # 🔧 [分页修复] 正确设置分页组件状态
            if total_records > 0 and hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                pagination_widget = self.main_workspace.pagination_widget
                
                # 设置总记录数
                if pagination_widget.state.total_records != total_records:
                    pagination_widget.set_total_records(total_records)
                    self.logger.info(f"🔧 [分页修复] 数据更新事件设置总记录数: {total_records}")
                
                # 🔧 [CRITICAL修复] 对于排序操作，不要重置页码！保持用户当前页面
                request_type = metadata.get('request_type', '')
                if request_type == 'sort_change':
                    # 排序操作：保持当前页码不变，只显示排序后当前页的数据
                    actual_current_page = pagination_widget.state.current_page
                    self.logger.info(f"🔧 [CRITICAL修复] 排序操作保持当前页码: {actual_current_page}，不进行页码重置")
                else:
                    # 其他操作：正常同步页码
                    if current_page > 0 and pagination_widget.state.current_page != current_page:
                        # 使用新的静默更新方法，避免触发信号循环
                        pagination_widget.silent_set_current_page(current_page)
                        self.logger.info(f"🔧 [关键修复] 静默同步分页状态: {current_page}（已修复信号循环）")
                
                # 强制更新UI状态
                pagination_widget._update_ui_state()
                
                # 记录分页状态信息
                self.logger.info(f"🔧 [分页修复] 分页状态: 当前页={pagination_widget.state.current_page}, "
                               f"总页数={pagination_widget.state.total_pages}, 总记录数={pagination_widget.state.total_records}")
                
                # 检查按钮状态
                can_next = pagination_widget.state.current_page < pagination_widget.state.total_pages
                can_prev = pagination_widget.state.current_page > 1
                self.logger.info(f"🔧 [分页修复] 按钮可用性: 下一页={can_next}, 上一页={can_prev}")
                
                # 🔧 [关键修复] 在分页状态完全设置正确后，设置到表格组件确保行号正确显示
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    pagination_state = {
                        'current_page': pagination_widget.state.current_page,
                        'page_size': pagination_widget.state.page_size,
                        'total_records': pagination_widget.state.total_records,
                        'start_record': (pagination_widget.state.current_page - 1) * pagination_widget.state.page_size + 1,
                        'end_record': min(pagination_widget.state.current_page * pagination_widget.state.page_size, pagination_widget.state.total_records)
                    }
                    self.logger.info(f"🔍 [FINAL修复] 最终调用set_pagination_state: 第{pagination_state.get('current_page')}页, 记录{pagination_state.get('start_record')}-{pagination_state.get('end_record')}")
                    self.main_workspace.expandable_table.set_pagination_state(pagination_state)
                    self.logger.info("🔍 [FINAL修复] set_pagination_state最终调用完成")
            
            # 更新当前表名
            self.current_table_name = table_name
            
        except Exception as e:
            self.logger.error(f"🆕 处理新架构数据更新事件失败: {e}", exc_info=True)
        finally:
            # 🔧 [CRITICAL修复] 确保处理标志和操作上下文被清除
            self._processing_data_update = False
            self._current_operation_context = {}
            
            # 🆕 [新架构] 完成渐进式状态迁移
            self.complete_navigation_transition()
            self.logger.info("🆕 [新架构] 数据更新完成，已完成渐进式状态迁移")
    
    def _on_new_error_event(self, event):
        """处理新架构的错误事件"""
        try:
            error_msg = getattr(event, 'error_message', '未知错误')
            self.logger.error(f"🆕 新架构错误事件: {error_msg}")
            self._update_status_label(f"错误: {error_msg}", "error")
            
        except Exception as e:
            self.logger.error(f"处理新架构错误事件失败: {e}")
    
    # 移除pyqtSlot装饰器，让PyQt自动处理参数类型匹配
    def _on_sort_indicator_changed(self, logical_index: int, order):
        """处理排序指示器变化事件 - 新架构版本"""
        try:
            # 🔧 [防重复] 防止重复处理相同的排序请求
            sort_key = f"{logical_index}_{order}"
            current_time = time.time()

            # 检查是否在很短时间内有相同的排序请求
            if hasattr(self, '_last_sort_request'):
                if (self._last_sort_request.get('key') == sort_key and
                    current_time - self._last_sort_request.get('time', 0) < 2.0):  # 增加到2秒防止循环
                    self.logger.debug(f"🔧 [防重复] 忽略重复排序请求: {sort_key}")
                    return

            self._last_sort_request = {'key': sort_key, 'time': current_time}

            # 🔧 [防循环] 检查是否正在处理排序
            if hasattr(self, '_sorting_in_progress') and self._sorting_in_progress:
                self.logger.warning(f"🔧 [防循环] 排序正在进行中，忽略新的排序请求: {sort_key}")
                return

            # 使用线程安全的排序状态管理
            self._sorting_in_progress = True

            # 移除排序超时机制 - 优化排序算法确保同步完成
            # 排序应该在毫秒内完成，不需要10秒超时保护
            
            # 导入Qt来处理SortOrder枚举
            from PyQt5.QtCore import Qt
            
            # 安全地转换Qt::SortOrder到字符串
            try:
                if hasattr(order, 'value'):
                    # 如果order是枚举对象
                    order_str = 'ASC' if order == Qt.AscendingOrder else 'DESC'
                elif isinstance(order, int):
                    # 如果order是整数值
                    order_str = 'ASC' if order == 0 else 'DESC'  # Qt.AscendingOrder = 0
                else:
                    # 其他情况，尝试直接比较
                    order_str = 'ASC' if order == Qt.AscendingOrder else 'DESC'
            except Exception as convert_error:
                self.logger.warning(f"排序顺序转换失败: {convert_error}, 使用默认ASC")
                order_str = 'ASC'
            
            # 🔧 [全局排序] 使用全局排序功能处理排序请求
            current_table_name = getattr(self, 'current_table_name', None)
            if not current_table_name:
                current_table_name = getattr(self.main_workspace, 'current_table_name', None)

            if current_table_name:
                self.logger.info(f"🔧 [全局排序] 排序请求: 列{logical_index}, 顺序{order_str}")

                # 获取列名
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    try:
                        header_item = self.main_workspace.expandable_table.horizontalHeaderItem(logical_index)
                        if header_item:
                            column_name = header_item.text()

                            # 转换为标准格式
                            sort_order = 'ascending' if order_str == 'ASC' else 'descending'

                            # 构建排序列信息
                            sort_columns = [{
                                'column_name': column_name,
                                'order': sort_order
                            }]

                            # 🔧 [新架构排序] 保存排序状态并重新加载数据
                            self.current_sort_columns = sort_columns
                            
                            # 🔧 [关键修复] 立即清除排序处理标志，允许事件更新UI
                            self._sorting_in_progress = False
                            self.logger.info(f"🔧 [关键修复] 排序处理标志已清除，允许UI更新")
                            
                            # 同步到排序状态管理器
                            if hasattr(self, 'sort_state_manager') and self.sort_state_manager:
                                try:
                                    current_table_name = getattr(self, 'current_table_name', '')
                                    if current_table_name:
                                        self.sort_state_manager.save_sort_state(current_table_name, sort_columns)
                                        self.logger.info(f"新架构排序状态已同步到管理器")
                                except Exception as e:
                                    self.logger.warning(f"新架构同步排序状态失败: {e}")

                            # 🔧 [CRITICAL修复] 获取真实的当前页码，排序时必须保持在当前页
                            current_page = 1
                            if (hasattr(self.main_workspace, 'pagination_widget') and
                                self.main_workspace.pagination_widget):
                                try:
                                    # 使用state属性获取真实的当前页码
                                    current_page = self.main_workspace.pagination_widget.state.current_page
                                    self.logger.info(f"🔧 [CRITICAL修复] 排序时获取真实页码: {current_page}")
                                except Exception as e:
                                    self.logger.warning(f"获取当前页码失败: {e}")
                                    current_page = 1

                            # 使用事件系统发布排序请求，保持当前页码
                            self._publish_sort_request_event(current_table_name, sort_columns, current_page)

                            # 排序完成后保持当前页码
                            if hasattr(self, 'pagination_widget') and self.pagination_widget:
                                actual_current_page = self.pagination_widget.get_current_page()
                                self.logger.info(f"🔧 [新架构排序] 排序完成: {column_name} {sort_order}, 保持第{actual_current_page}页")
                            else:
                                self.logger.info(f"🔧 [新架构排序] 排序完成: {column_name} {sort_order}, 保持第{current_page}页")
                            self._reset_sorting_state()
                            return
                        else:
                            self.logger.warning(f"🔧 [异常处理] 无法获取列{logical_index}的表头信息")
                    except Exception as header_error:
                        self.logger.error(f"🔧 [异常处理] 获取表头信息失败: {header_error}")
                else:
                    self.logger.warning("🔧 [异常处理] 表格组件不可用，无法处理排序")
            else:
                self.logger.warning("🔧 [异常处理] 当前表名为空，无法处理排序请求")

        except Exception as e:
            self.logger.error(f"🔧 [全局排序] 排序处理失败: {e}")

        # 确保无论如何都重置排序状态
        self._reset_sorting_state()

    def _reset_sorting_state(self):
        """安全重置排序状态"""
        try:
            self._sorting_in_progress = False
            self.logger.debug("排序状态已重置")
        except Exception as e:
            self.logger.error(f"重置排序状态失败: {e}")

    def _reload_current_page_with_sort(self):
        """🔧 [新架构排序] 重新加载当前页数据（带排序）"""
        try:
            # 优先使用主窗口的current_table_name，然后是工作区的
            current_table_name = getattr(self, 'current_table_name', None)
            if not current_table_name:
                current_table_name = getattr(self.main_workspace, 'current_table_name', None)

            if not current_table_name:
                self.logger.warning("🔧 [新架构排序] 没有当前表名，无法执行排序")
                return

            self.logger.info(f"🔧 [新架构排序] 开始重新加载数据: {current_table_name}, 排序列: {len(self.current_sort_columns)}")

            # 🆕 使用新架构处理排序
            self._reload_with_new_architecture_sort(current_table_name)

        except Exception as e:
            self.logger.error(f"🔧 [新架构排序] 重新加载页面失败: {e}")

    def _reload_with_new_architecture_sort(self, table_name: str):
        """使用新架构重新加载带排序的数据"""
        try:
            # 获取当前分页状态
            current_page = 1
            page_size = 50

            # 从分页组件获取当前页码
            if (hasattr(self.main_workspace, 'pagination_widget') and
                self.main_workspace.pagination_widget):
                try:
                    current_page = getattr(self.main_workspace.pagination_widget, 'current_page', 1)
                    page_size = getattr(self.main_workspace.pagination_widget, 'page_size', 50)
                    self.logger.info(f"🔧 [新架构排序] 从分页组件获取状态: 第{current_page}页, {page_size}条/页")
                except Exception as e:
                    self.logger.warning(f"🔧 [新架构排序] 获取分页状态失败: {e}")

            # 转换排序列格式为新架构格式
            sort_columns = []
            if self.current_sort_columns:
                self.logger.info(f"🔧 [CRITICAL调试] 原始排序列: {self.current_sort_columns}")
                for sort_col in self.current_sort_columns:
                    column_name = sort_col.get('column_name', '')
                    order = sort_col.get('order', 'ascending')
                    self.logger.info(f"🔧 [CRITICAL调试] 处理排序列: {column_name} -> {order}")

                    # 转换中文列名为英文字段名
                    db_field_name = self._convert_column_name_to_db_field(column_name, table_name)
                    self.logger.info(f"🔧 [CRITICAL调试] 字段转换结果: {column_name} -> {db_field_name}")
                    if db_field_name:
                        sort_columns.append({
                            'column_name': db_field_name,
                            'order': order,
                            'priority': sort_col.get('priority', 0)
                        })
                    else:
                        self.logger.error(f"🔧 [CRITICAL错误] 字段转换失败，排序将失效: {column_name}")

            self.logger.info(f"🔧 [CRITICAL调试] 最终排序参数: {sort_columns}")
            print(f"🔧 [CRITICAL调试] 最终排序参数: {sort_columns}")

            # 使用新架构服务加载数据
            response = self.table_data_service.load_table_data(
                table_name=table_name,
                page=current_page,
                page_size=page_size,
                sort_columns=sort_columns,
                force_reload=False  # 🚀 [性能修复] 启用缓存
            )

            if response.success:
                self.logger.info(f"🔧 [新架构排序] 数据加载成功: {len(response.data)}行")
                # 数据更新会通过事件总线自动处理UI更新
            else:
                self.logger.error(f"🔧 [新架构排序] 数据加载失败: {response.error}")
                # 🔧 [P1-移除旧架构] 移除降级逻辑，抛出异常
                raise RuntimeError(f"🔧 [P1-移除旧架构] 新架构排序数据加载失败: {response.error}")

        except Exception as e:
            self.logger.error(f"🔧 [新架构排序] 处理失败: {e}")
            # 使用事件系统发布排序请求
            self._publish_sort_request_event(table_name, self.current_sort_columns, current_page)

    def _publish_sort_request_event(self, table_name: str, sort_columns: List[Dict[str, Any]], current_page: int = 1):
        """通过事件系统发布排序请求"""
        self.logger.info(f"🔧 [排序调试] 准备发布排序请求: {table_name}, {sort_columns}")
        
        # 验证参数
        if not table_name:
            self.logger.error("🔧 [排序调试] 表名为空，无法发布排序请求")
            return
            
        if not sort_columns:
            self.logger.warning("🔧 [P1-修复] 排序列为空，尝试从状态管理器恢复")
            
            # 🔧 [CRITICAL修复] 尝试从排序状态管理器恢复排序状态
            if hasattr(self, 'sort_state_manager') and self.sort_state_manager and table_name:
                try:
                    restored_state = self.sort_state_manager.restore_sort_state(table_name)
                    if restored_state and hasattr(restored_state, 'sort_columns') and restored_state.sort_columns:
                        sort_columns = restored_state.sort_columns
                        self.logger.info(f"🔧 [P1-修复] 成功从状态管理器恢复排序列: {len(sort_columns)}个")
                        # 同步更新当前排序状态
                        self.current_sort_columns = sort_columns
                    else:
                        self.logger.info("🔧 [P1-修复] 状态管理器中无排序状态，将发布清空排序请求")
                except Exception as e:
                    self.logger.warning(f"🔧 [P1-修复] 排序状态恢复失败: {e}")
            
            # 如果仍然为空，允许发布清空排序的请求（不再直接返回）
            if not sort_columns:
                self.logger.info("🔧 [P1-修复] 发布清空排序请求，这是正常操作")
                # 继续执行，允许清空排序
            
        try:
            # 🔧 [数据流追踪] 记录排序请求处理开始
            self.logger.info(f"[数据流追踪] 开始处理排序请求: 表={table_name}, 排序列数={len(sort_columns) if sort_columns else 0}, 页码={current_page}")
            
            # 🔧 [P0-性能优化] 大幅简化防重复机制，提升响应速度
            current_time = time.time()
            
            # 🔧 [P0-CRITICAL修复] 移除过度复杂的防重复检查，仅保留最基本的保护
            if getattr(self, '_processing_sort_request', False):
                # 如果上次处理时间超过1秒，认为可能卡住了，重置状态
                if hasattr(self, '_last_sort_request_time') and (current_time - self._last_sort_request_time) > 1.0:
                    self.logger.warning("🔧 [超时重置] 排序请求处理超时，重置状态")
                    self._processing_sort_request = False
                else:
                    return  # 静默跳过正在处理的请求
            
            # 设置简化的处理标志
            self._processing_sort_request = True
            self._last_sort_request_time = current_time
            
            # 🆕 [性能修复] 设置排序操作上下文，防止页码重置
            self._current_operation_context = {
                'operation_type': 'sort_change',
                'sort_columns': sort_columns,
                'current_page': current_page,
                'timestamp': current_time
            }

            # 多层级获取表名
            current_table_name = table_name or getattr(self, 'current_table_name', None)

            # 如果还是没有表名，尝试从工作区获取
            if not current_table_name and hasattr(self, 'main_workspace'):
                current_table_name = getattr(self.main_workspace, 'current_table_name', None)

            # 如果还是没有表名，尝试从表格组件获取
            if not current_table_name and hasattr(self, 'main_workspace') and hasattr(self.main_workspace, 'expandable_table'):
                current_table_name = getattr(self.main_workspace.expandable_table, 'current_table_name', None)

            if not current_table_name:
                self.logger.error("🔧 [排序] 表名为空，无法发布排序请求")
                self.logger.debug(f"🔧 [排序] 调试信息: table_name={table_name}, self.current_table_name={getattr(self, 'current_table_name', None)}")
                return

            # 获取实际的当前页码
            actual_current_page = current_page
            if hasattr(self, 'main_workspace') and hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                actual_current_page = self.main_workspace.pagination_widget.get_current_page()
                self.logger.info(f"🔧 [排序] 使用实际页码: {actual_current_page} (传入页码: {current_page})")
            else:
                self.logger.info(f"🔧 [排序] 使用传入页码: {current_page}")

            if hasattr(self, 'event_bus') and self.event_bus:
                from src.core.event_bus import SortRequestEvent

                # 🔧 [性能优化] 快速字段转换，减少日志开销
                converted_sort_columns = []
                for sort_col in sort_columns:
                    column_name = sort_col.get('column_name', '')
                    order = sort_col.get('order', 'ascending')

                    # 快速字段转换（缓存优化）
                    db_field_name = self._convert_column_name_to_db_field(column_name, current_table_name)
                    if db_field_name:
                        converted_sort_columns.append({
                            'column_name': db_field_name,
                            'order': order,
                            'priority': sort_col.get('priority', 0)
                        })
                        # 减少日志输出，只在debug模式显示
                        self.logger.debug(f"字段转换: {column_name} -> {db_field_name}")

                # 🔧 [P1-修复] 允许空排序列发布事件（清空排序功能）
                if not converted_sort_columns:
                    self.logger.info("🔧 [P1-修复] 转换后排序列为空，将发布清空排序事件")
                    # 不再返回，允许发布空排序事件

                # 创建排序请求事件
                sort_event = SortRequestEvent(
                    event_type="sort_request",
                    table_name=current_table_name,
                    sort_columns=converted_sort_columns,
                    current_page=actual_current_page
                )

                # 🔧 [P1-修复] 发布排序事件并添加数据流追踪
                self.event_bus.publish(sort_event)
                self.logger.info(f"[数据流追踪] 排序事件已发布: 表={current_table_name}, 排序列数={len(converted_sort_columns)}, 页码={actual_current_page}")
            else:
                self.logger.warning("🔧 [排序] 事件总线不可用，尝试备用方法")

                # 尝试使用备用方法
                if hasattr(self, 'table_data_service') and self.table_data_service:
                    self.logger.info(f"[数据流追踪] 使用备用方法加载数据: 表={current_table_name}, 排序列数={len(converted_sort_columns) if converted_sort_columns else 0}")
                    response = self.table_data_service.load_table_data(
                        table_name=current_table_name,
                        page=actual_current_page,
                        sort_columns=converted_sort_columns,
                        force_reload=False  # 🚀 [性能修复] 启用缓存
                    )
                    if response.success:
                        self.logger.info(f"[数据流追踪] 备用方法加载成功: {len(response.data)}行数据")

        except Exception as e:
            self.logger.error(f"🔧 [事件排序] 发布排序请求事件失败: {e}")
        finally:
            # 确保处理标志被清除
            self._processing_sort_request = False
            
            # 🆕 [性能修复] 延迟清理操作上下文，确保数据设置完成
            from PyQt5.QtCore import QTimer
            safe_single_shot(500, lambda: setattr(self, '_current_operation_context', {}))

    # 旧架构排序方法已完全移除，使用新架构事件系统

    def _convert_column_name_to_db_field(self, column_name: str, table_name: str) -> str:
        """🔧 [P2修复] 将中文列名转换为数据库字段名"""
        try:
            # 1. 首先检查是否已经是英文字段名
            if self._is_db_field_name(column_name):
                self.logger.debug(f"🔧 [P2修复] 已经是数据库字段名: {column_name}")
                return column_name
            
            # 2. 获取反向字段映射（中文->英文）
            reverse_mapping = self._get_reverse_field_mapping(table_name)
            if reverse_mapping and column_name in reverse_mapping:
                db_field = reverse_mapping[column_name]
                self.logger.debug(f"🔧 [P2修复] 映射转换: {column_name} -> {db_field}")
                return db_field
            
            # 3. 使用内置的字段映射表
            built_in_mapping = self._get_built_in_field_mapping()
            if column_name in built_in_mapping:
                db_field = built_in_mapping[column_name]
                self.logger.debug(f"🔧 [P2修复] 内置映射: {column_name} -> {db_field}")
                return db_field
            
            # 4. 如果还是找不到，直接使用列名
            self.logger.warning(f"🔧 [P2修复] 未找到字段映射，使用原名: {column_name}")
            return column_name
            
        except Exception as e:
            self.logger.error(f"🔧 [P2修复] 字段转换失败: {e}")
            return column_name
    
    def _is_db_field_name(self, field_name: str) -> bool:
        """🔧 [P2修复] 判断是否为数据库字段名（英文）"""
        # 数据库字段名通常是英文、数字和下划线组成
        import re
        return bool(re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', field_name))
    
    def _get_built_in_field_mapping(self) -> Dict[str, str]:
        """🔧 [P2修复] 获取内置字段映射表"""
        return {
            "2025年薪级工资": "grade_salary_2025",
            "2025年岗位工资": "position_salary_2025",
            "工号": "employee_id",
            "姓名": "employee_name",
            "部门名称": "department",
            "人员类别": "employee_type",
            "人员类别代码": "employee_type_code",
            "津贴": "allowance",
            "结余津贴": "balance_allowance",
            "2025年基础性绩效": "basic_performance_2025",
            "卫生费": "health_fee",
            "交通补贴": "transport_allowance",
            "物业补贴": "property_allowance",
            "通讯补贴": "communication_allowance",
            "2025年奖励性绩效预发": "performance_bonus_2025",
            "2025公积金": "provident_fund_2025",
            "住房补贴": "housing_allowance",
            "车补": "car_allowance",
            "补发": "supplement",
            "借支": "advance",
            "应发工资": "total_salary",
            "代扣代存养老保险": "pension_insurance"
        }

    def _get_table_field_mapping(self, table_name: str) -> Dict[str, str]:
        """获取表的字段映射（英文->中文）"""
        try:
            # 检查缓存
            if table_name in self.field_mapping_cache:
                return self.field_mapping_cache[table_name]

            # 从配置同步管理器加载映射
            if hasattr(self, 'config_sync_manager') and self.config_sync_manager:
                mapping = self.config_sync_manager.load_mapping(table_name)
                if mapping:
                    self.field_mapping_cache[table_name] = mapping
                    self.logger.info(f"🔧 [字段映射] 加载映射成功: {table_name}, {len(mapping)}个字段")
                    return mapping

            # 如果没有找到映射，返回空字典
            self.logger.warning(f"🔧 [字段映射] 未找到表映射: {table_name}")
            return {}

        except Exception as e:
            self.logger.error(f"🔧 [字段映射] 获取表映射失败: {e}")
            return {}

    def _get_reverse_field_mapping(self, table_name: str) -> dict:
        """获取反向字段映射（中文->英文）"""
        try:
            # 检查缓存
            cache_key = f"reverse_{table_name}"
            if cache_key in self.field_mapping_cache:
                return self.field_mapping_cache[cache_key]

            # 获取正向映射（英文->中文）
            forward_mapping = self._get_table_field_mapping(table_name)
            if not forward_mapping:
                return {}

            # 生成反向映射（中文->英文）
            reverse_mapping = {chinese: english for english, chinese in forward_mapping.items()}

            # 缓存结果
            self.field_mapping_cache[cache_key] = reverse_mapping

            self.logger.debug(f"🔧 [字段映射] 生成反向映射: {table_name}, {len(reverse_mapping)}个字段")
            return reverse_mapping

        except Exception as e:
            self.logger.error(f"🔧 [字段映射] 获取反向映射失败: {e}")
            return {}

    # 🔧 [P1-移除旧架构] 删除 _legacy_handle_global_sort_request 方法

    def toggle_global_sort(self, enabled: bool):
        """🔧 [全局排序] 切换全局排序开关 - PrototypeMainWindow版本"""
        try:
            self.logger.info(f"🔧 [全局排序] 主窗口全局排序开关: {'启用' if enabled else '禁用'}")
            
            # 将开关状态传递给主工作区域
            if hasattr(self, 'main_workspace') and hasattr(self.main_workspace, 'toggle_global_sort'):
                self.main_workspace.toggle_global_sort(enabled)
            else:
                self.logger.warning("🔧 [全局排序] 主工作区域不支持全局排序开关")
                
        except Exception as e:
            self.logger.error(f"🔧 [全局排序] 切换排序开关失败: {e}")
    
    def _handle_global_sort_request(self, sort_columns: List[Dict[str, Any]], table_name: str = None):
        """代理全局排序请求到MainWorkspaceArea"""
        try:
            if hasattr(self, 'main_workspace') and hasattr(self.main_workspace, '_handle_global_sort_request'):
                return self.main_workspace._handle_global_sort_request(sort_columns, table_name)
            else:
                self.logger.warning("主工作区域不支持全局排序请求")
                return False
        except Exception as e:
            self.logger.error(f"代理全局排序请求失败: {e}")
            return False

    @prevent_loop("sort_applied")
    def _handle_sort_applied(self, logical_index: int, column_name: str, sort_state: str):
        """🔧 [P0-CRITICAL] 简化的排序处理（线程安全版本）"""
        # 🔧 [P0-CRITICAL] 确保在主线程执行
        from src.utils.thread_safe_timer import ThreadSafeTimer
        from PyQt5.QtCore import QMetaObject, Qt
        from PyQt5.QtWidgets import QApplication
        
        if not ThreadSafeTimer.is_main_thread():
            self.logger.warning("🔧 [P0-CRITICAL] 排序处理在非主线程，强制转移到主线程")
            app = QApplication.instance()
            if app:
                # 🔧 [P0-CRITICAL修复] 使用QTimer来确保在主线程执行
                from PyQt5.QtCore import QTimer
                
                def execute_sort_in_main_thread():
                    """在主线程执行的排序处理"""
                    try:
                        self._handle_sort_applied_impl(logical_index, column_name, sort_state)
                    except Exception as main_error:
                        self.logger.error(f"🔧 [P0-CRITICAL] 主线程排序处理失败: {main_error}", exc_info=True)
                
                # 使用QTimer.singleShot确保在主线程执行
                safe_single_shot(0, execute_sort_in_main_thread)
                return
            else:
                self.logger.error("🔧 [P0-CRITICAL] QApplication不存在，无法转移排序到主线程")
                return
        
        # 🔧 [P0-CRITICAL] 在主线程执行实际的排序处理
        self._handle_sort_applied_impl(logical_index, column_name, sort_state)
    
    def _handle_sort_applied_impl(self, logical_index: int, column_name: str, sort_state: str):
        """🔧 [P0-CRITICAL] 简化的排序处理实现（主线程版本）"""
        try:
            self.logger.info(f"🔧 [P0-CRITICAL] 简化排序处理: 列{logical_index}, {column_name}, {sort_state}")

            # 🔧 [P0-CRITICAL] 简化排序状态管理
            sort_columns = [{
                'column_name': column_name,
                'order': sort_state,
                'priority': 0
            }]
            self.current_sort_columns = sort_columns
            
            # 🔧 [P0-CRITICAL] 简化状态同步（移除复杂的中间层）
            table_name = getattr(self, 'current_table_name', None)
            if not table_name and hasattr(self, 'main_workspace'):
                table_name = getattr(self.main_workspace, 'current_table_name', None)

            # 🔧 [P0-CRITICAL] 获取当前页码
            current_page = self._get_current_page_number()
            
            # 发布排序请求事件（使用当前页码）
            self._publish_sort_request_event(table_name, sort_columns, current_page)

        except Exception as e:
            self.logger.error(f"🆕 [新架构排序] 处理排序应用失败: {e}")

    def _handle_sort_cleared(self):
        """🆕 [新架构排序] 处理排序清除事件"""
        try:
            self.logger.info("🆕 [新架构排序] 处理排序清除")

            # 清除排序状态
            self.current_sort_columns = []
            
            # 同步清除到排序状态管理器
            if hasattr(self, 'sort_state_manager') and self.sort_state_manager:
                try:
                    current_table_name = getattr(self, 'current_table_name', '')
                    if current_table_name:
                        self.sort_state_manager.clear_sort_state(current_table_name)
                        self.logger.info(f"排序状态已从管理器中清除")
                except Exception as e:
                    self.logger.warning(f"清除排序状态失败: {e}")

            # 获取当前页码
            current_page = self._get_current_page_number()
            self.logger.info(f"清除排序，当前页码: {current_page}")

            # 获取表名，确保不为空
            table_name = getattr(self, 'current_table_name', None)
            if not table_name and hasattr(self, 'main_workspace'):
                table_name = getattr(self.main_workspace, 'current_table_name', None)

            # 发布排序清除事件（空的排序列表）
            self._publish_sort_request_event(table_name, [], current_page)

        except Exception as e:
            self.logger.error(f"🆕 [新架构排序] 处理排序清除失败: {e}")

    def _get_current_page_number(self) -> int:
        """获取当前页码"""
        try:
            # 方法1: 从分页组件的方法获取
            if hasattr(self, 'main_workspace') and hasattr(self.main_workspace, 'pagination_widget'):
                pagination_widget = self.main_workspace.pagination_widget
                if hasattr(pagination_widget, 'get_current_page'):
                    current_page = pagination_widget.get_current_page()
                    self.logger.debug(f"从分页组件方法获取页码: {current_page}")
                    return current_page
                elif hasattr(pagination_widget, 'current_page'):
                    current_page = pagination_widget.current_page
                    self.logger.debug(f"从分页组件属性获取页码: {current_page}")
                    return current_page
                elif hasattr(pagination_widget, 'state') and hasattr(pagination_widget.state, 'current_page'):
                    current_page = pagination_widget.state.current_page
                    self.logger.debug(f"从分页组件状态获取页码: {current_page}")
                    return current_page

            # 方法2: 从实例变量获取
            if hasattr(self, 'current_page'):
                current_page = self.current_page
                self.logger.debug(f"从实例变量获取页码: {current_page}")
                return current_page

            # 方法3: 默认值
            self.logger.debug("使用默认页码: 1")
            return 1

        except Exception as e:
            self.logger.error(f"获取当前页码失败: {e}")
            return 1

    def _get_table_type_from_name(self, table_name: str) -> str:
        """从表名获取表类型"""
        try:
            if not table_name:
                return "active_employees"  # 默认类型
            
            # 表名格式通常为: salary_data_年份_月份_表类型
            parts = table_name.split('_')
            if len(parts) >= 5:
                table_type = parts[-1]  # 最后一部分是表类型
                return table_type
            
            # 如果无法解析，尝试从表名中匹配
            if 'active' in table_name.lower():
                return "active_employees"
            elif 'retired' in table_name.lower():
                return "retired_employees"
            elif 'pension' in table_name.lower():
                return "pension_employees"
            elif 'part_time' in table_name.lower():
                return "part_time_employees"
            
            # 默认返回active_employees
            return "active_employees"
            
        except Exception as e:
            self.logger.error(f"获取表类型失败: {e}")
            return "active_employees"

    def _get_current_field_mapping(self, table_name: str) -> dict:
        """获取当前表的字段映射"""
        try:
            # 🔧 [紧急修复] 完整的字段映射，包含所有4类表格的字段
            default_mapping = {
                # 通用字段
                "sequence_number": "序号",
                "employee_id": "工号",
                "employee_name": "姓名",
                "department": "部门名称",
                "employee_type_code": "人员类别代码",
                "employee_type": "人员类别",
                
                # 在职人员字段
                "position_salary_2025": "2025年岗位工资",
                "grade_salary_2025": "2025年薪级工资",
                "seniority_salary_2025": "2025年校龄工资",  # 🔧 A岗职工特有 - 关键修复
                "allowance": "津贴",
                "balance_allowance": "结余津贴",
                "basic_performance_2025": "2025年基础性绩效",
                "health_fee": "卫生费",
                "living_allowance_2025": "2025年生活补贴",  # 🔧 A岗职工特有 - 关键修复
                "transport_allowance": "交通补贴",
                "property_allowance": "物业补贴",
                "housing_allowance": "住房补贴",
                "car_allowance": "车补",
                "communication_allowance": "通讯补贴",
                "performance_bonus_2025": "2025年奖励性绩效预发",
                "supplement": "补发",
                "advance": "借支",
                "total_salary": "应发工资",
                "provident_fund_2025": "2025公积金",
                "insurance_deduction": "保险扣款",  # 🔧 A岗职工特有
                "pension_insurance": "代扣代存养老保险",
                
                # 离退休人员字段
                "basic_retirement_salary": "基本离休费",  # 离休人员
                "living_allowance": "生活补贴",  # 离休人员
                "retirement_allowance": "离休补贴",  # 离休人员
                "nursing_fee": "护理费",
                "one_time_living_allowance": "增发一次性生活补贴",  # 离休人员
                "total": "合计",  # 离休人员
                "retirement_living_allowance": "离退休生活补贴",  # 🔧 退休人员特有 - 关键修复
                "salary_advance": "增资预付",  # 退休人员
                "adjustment_2016": "2016待遇调整",  # 退休人员
                "adjustment_2017": "2017待遇调整",
                "adjustment_2018": "2018待遇调整", 
                "adjustment_2019": "2019待遇调整",
                "adjustment_2020": "2020待遇调整",
                "adjustment_2021": "2021待遇调整",
                "adjustment_2022": "2022待遇调整",
                "adjustment_2023": "2023待遇调整",
                "provident_fund": "公积",  # 退休人员
                "remarks": "备注"
            }
            
            # 🔧 [紧急修复] 优先从统一配置文件获取字段映射
            try:
                import json
                from pathlib import Path
                
                config_path = Path(__file__).parent.parent.parent.parent / "state" / "data" / "field_mappings.json"
                if config_path.exists():
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    # 根据表名获取对应的字段映射
                    table_mappings = config_data.get('table_mappings', {})
                    if table_name in table_mappings:
                        field_mappings = table_mappings[table_name].get('field_mappings', {})
                        if field_mappings:
                            self.logger.debug(f"🔧 [紧急修复] 从配置文件获取字段映射: {table_name}, {len(field_mappings)} 个字段")
                            return field_mappings
                    
                    # 如果没有找到特定表的映射，尝试从模板获取
                    field_templates = config_data.get('field_templates', {})
                    for template_name, template_mapping in field_templates.items():
                        if any(keyword in table_name.lower() for keyword in template_name.lower().split()):
                            self.logger.debug(f"🔧 [紧急修复] 从模板获取字段映射: {template_name}, {len(template_mapping)} 个字段")
                            return template_mapping
                            
            except Exception as e:
                self.logger.warning(f"🔧 [紧急修复] 从配置文件获取字段映射失败: {e}")
            
            # 尝试从配置管理器获取字段映射（后备方案）
            if hasattr(self, 'config_manager') and self.config_manager:
                try:
                    mapping = self.config_manager.get_field_mapping(table_name)
                    if mapping:
                        return mapping
                except Exception as e:
                    self.logger.debug(f"从配置管理器获取字段映射失败: {e}")
            
            # 返回默认映射
            self.logger.debug(f"🔧 [紧急修复] 使用默认字段映射: {len(default_mapping)} 个字段")
            return default_mapping
            
        except Exception as e:
            self.logger.error(f"获取字段映射失败: {e}")
            # 返回包含关键字段的最小映射作为fallback
            return {
                "employee_id": "工号",
                "employee_name": "姓名", 
                "position_salary_2025": "2025年岗位工资",
                "grade_salary_2025": "2025年薪级工资",
                "seniority_salary_2025": "2025年校龄工资",  # 🔧 A岗职工关键字段
                "housing_allowance": "住房补贴",  # 🔧 离退休关键字段
                "retirement_living_allowance": "离退休生活补贴"  # 🔧 退休人员关键字段
            }

    @pyqtSlot(int)
    @prevent_loop("page_changed")
    def _on_page_changed_new_architecture(self, page: int):
        """🔧 [立即修复] 处理分页变化事件 - 强化去重版本"""
        try:
            # 🔧 [立即修复] 使用原子操作管理分页处理标志
            import threading
            if not hasattr(self, '_pagination_lock'):
                self._pagination_lock = threading.RLock()

            # 🔧 [P0-CRITICAL修复] 立即设置分页流程标志，确保后续上下文检测正确
            self._in_pagination_flow = True
            self._pagination_start_time = time.time()

            # 🔧 [立即修复] 使用强化的分页状态管理器和事件优化器
            from src.core.pagination_state_manager import get_pagination_state_manager
            from src.core.pagination_event_optimizer import get_pagination_event_optimizer, EventType
            pagination_manager = get_pagination_state_manager()
            event_optimizer = get_pagination_event_optimizer()

            # 快速获取表名用于状态检查
            current_table_name = getattr(self, 'current_table_name') or getattr(self.main_workspace, 'current_table_name', None)
            if not current_table_name:
                self.logger.warning(f"🔧 [分页请求] 无法获取当前表名，跳过分页: 第{page}页")
                self._in_pagination_flow = False  # 清除标志
                return

            # 获取排序状态
            sort_columns = []
            if hasattr(self, 'current_sort_columns') and self.current_sort_columns:
                sort_columns = self.current_sort_columns

            # 🔧 [立即修复] 检查是否可以开始分页操作
            if not pagination_manager.can_start_pagination(current_table_name, page, sort_columns):
                self.logger.info(f"🚫 [立即修复] 分页状态管理器拒绝请求: 第{page}页")
                return

            # 🔧 [立即修复] 使用事件优化器处理分页请求
            def execute_pagination():
                """实际执行分页操作的函数"""
                try:
                    # 开始分页操作
                    request_id = pagination_manager.start_pagination(current_table_name, page, sort_columns)

                    with self._pagination_lock:
                        # 设置传统的处理标志（向后兼容）
                        self._processing_pagination = True

                    # 执行实际的分页数据加载
                    self._execute_pagination_data_load(current_table_name, page, sort_columns, pagination_manager, event_optimizer)

                except Exception as e:
                    self.logger.error(f"🔧 [立即修复] 分页执行失败: {e}", exc_info=True)

            # 🔧 [立即修复] 将分页请求添加到事件优化器
            event_added = event_optimizer.add_event(
                event_type=EventType.PAGE_CHANGE,
                table_name=current_table_name,
                page=page,
                data={'sort_columns': sort_columns},
                callback=execute_pagination,
                priority=5  # 中等优先级
            )

            if not event_added:
                self.logger.info(f"🔧 [立即修复] 分页事件被去重优化: 第{page}页")
                return
            
            # 🔧 [P0-1修复] 分页前保存当前表格状态（列宽、排序等）
            current_table_name = getattr(self, 'current_table_name') or getattr(self.main_workspace, 'current_table_name', None)
            if current_table_name and hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                self._save_current_table_ui_state(current_table_name)
                
            # 🔧 [彻底修复] 设置分页处理标志，防止其他操作干扰
            self._processing_pagination = True
            
            # 🔧 [CRITICAL修复] 强化分页操作上下文，确保持久化
            self._current_operation_context = {
                'operation_type': 'page_change', 
                'target_page': page,
                'timestamp': time.time(),
                'persistent': True  # 标记为持久化上下文，延迟清理
            }
            
            # 快速获取表名
            current_table_name = getattr(self, 'current_table_name') or getattr(self.main_workspace, 'current_table_name', None)

            self.logger.info(f"🔧 [分页请求] 第{page}页, 表: {current_table_name}, 上下文已设置")

            # 使用新架构处理分页
            if self.table_data_service and current_table_name:
                # 快速获取分页参数
                page_size = getattr(self.main_workspace.pagination_widget, 'page_size', 50) if hasattr(self.main_workspace, 'pagination_widget') else 50
                sort_columns = self.current_sort_columns if (hasattr(self, 'global_sort_enabled') and self.global_sort_enabled and hasattr(self, 'current_sort_columns')) else []
                
                # 🔧 [立即修复] 使用强化的分页去重检查
                from src.core.request_deduplication_manager import RequestDeduplicationManager
                dedup_manager = RequestDeduplicationManager()

                # 🔧 [立即修复] 使用专门的分页去重方法防止循环调用
                if not dedup_manager.should_allow_pagination_request(current_table_name, page, sort_columns):
                    self.logger.info(f"🚫 [立即修复] 分页请求被去重管理器拒绝（防循环）: 第{page}页")
                    return

                self.logger.info(f"🔧 [立即修复] 分页请求通过强化去重检查: {current_table_name}, 第{page}页")
                
                # 🔧 [性能优化] 简化重载策略判断
                is_sort_operation = self._current_operation_context.get('operation_type') == 'sort_change'
                force_reload_flag = is_sort_operation  # 只在排序操作时强制重载
                
                response = self.table_data_service.load_table_data(
                    table_name=current_table_name,  # 使用安全获取的表名
                    page=page,
                    page_size=page_size,
                    sort_columns=sort_columns,  # 传递排序状态
                    force_reload=force_reload_flag  # 智能重载策略
                )
                
                # 🚫 [用户要求] 彻底删除所有加载条！不显示任何加载指示器
                self.logger.info(f"🚫 [用户要求] 不显示加载条，直接处理数据: {current_table_name} 第{page}页")
                
                if response.success:
                    self.logger.info(f"🔧 [根本修复] 分页数据获取成功: 第{page}页, {len(response.data)}行")
                    
                    # 🔧 [根本修复] 验证获取的数据
                    if hasattr(response.data, 'head') and len(response.data) > 0:
                        # DataFrame模式
                        first_ids = response.data.iloc[:3]['employee_id'].tolist() if 'employee_id' in response.data.columns else ['无工号']
                        self.logger.info(f"🔧 [根本修复] 获取数据的前3个工号: {first_ids}")
                    elif isinstance(response.data, list) and len(response.data) > 0:
                        # List模式
                        first_ids = [row.get('employee_id', row.get('工号', f"行{i}")) for i, row in enumerate(response.data[:3])]
                        self.logger.info(f"🔧 [根本修复] 获取数据的前3个工号: {first_ids}")
                    
                                    # 🔧 [简化分页处理] 移除手动数据设置，依赖事件机制更新UI
                if response.success:
                    self.logger.info(f"[数据流追踪] 分页数据请求成功: 第{page}页, {len(response.data)}行")
                    
                    # 🔧 [立即修复] 标记分页请求完成
                    dedup_manager.mark_pagination_request_completed(current_table_name, page, sort_columns)
                    pagination_manager.complete_pagination(current_table_name, page, sort_columns, success=True)

                    # 🔧 [关键修复] 立即清除处理标志，允许事件更新UI
                    with self._pagination_lock:
                        self._processing_pagination = False
                    self.logger.info(f"🔧 [立即修复] 分页处理标志已清除，允许UI更新: 第{page}页")
                    
                    # 🧹 [P2清理] 移除冗余200ms加载反馈，数据已返回应立即更新UI
                    # 加载指示器已在第4051行智能处理，此处无需重复调用
                    cache_hit = getattr(response, 'cache_hit', False)
                    self.logger.info(f"🧹 [智能修复] 数据返回完成，缓存状态: {cache_hit}, 第{page}页")
                    
                    # 🔧 [分页状态同步] 更新分页组件状态，确保UI同步
                    if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                        pagination_widget = self.main_workspace.pagination_widget
                        
                        # 设置总记录数
                        if hasattr(response, 'total_records') and response.total_records > 0:
                            pagination_widget.set_total_records(response.total_records)
                        
                        # 🔧 [静默模式修复] 使用新的静默更新方法，避免触发信号循环
                        if pagination_widget.state.current_page != page:
                            pagination_widget.silent_set_current_page(page)
                            self.logger.info(f"🔧 [分页状态同步] 静默更新页码: {page}（已修复信号循环）")
                    
                    # 🔧 [P0-2修复] 分页数据获取成功后，按正确顺序更新UI
                    try:
                        if hasattr(self.main_workspace, 'set_data') and response.data is not None:
                            # 🔧 [P0-2修复] 第一步：先应用字段映射，确保表头显示中文
                            mapped_data = self._apply_field_mapping_to_dataframe(response.data, current_table_name)
                            self.logger.info(f"🔧 [P0-2修复] 字段映射应用完成: {len(mapped_data.columns)}列")
                            
                            # 🔧 [P0-2修复] 第二步：设置映射后的数据到UI，明确不保留旧表头
                            self.main_workspace.set_data(
                                df=mapped_data, 
                                preserve_headers=False,  # 关键：不保留旧表头，使用新映射的表头
                                table_name=current_table_name,
                                current_table_name=current_table_name
                            )
                            self.logger.info(f"🔧 [P0-2修复] 分页UI更新成功: 第{page}页, {len(mapped_data)}行数据")
                            
                            # 🔧 [P0-2修复] 第三步：恢复表格UI状态（列宽、排序等）
                            self._restore_table_ui_state(current_table_name)
                            
                            # 🔧 [P0-1修复] 第四步：强制刷新表头显示，传递正确参数
                            if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                                if hasattr(self.main_workspace.expandable_table, 'header_update_manager'):
                                    # 🔧 [P0-1修复] 计算正确的表头更新参数
                                    headers = list(mapped_data.columns)  # 获取映射后的中文表头
                                    start_record = (page - 1) * 50 + 1   # 计算起始记录号
                                    count = len(mapped_data)             # 当前页记录数
                                    
                                    self.main_workspace.expandable_table.header_update_manager.force_update_all(
                                        headers=headers, 
                                        start_record=start_record, 
                                        count=count
                                    )
                                    self.logger.info(f"🔧 [P0-1修复] 强制刷新表头完成: {len(headers)}个中文表头, 记录{start_record}-{start_record+count-1}")
                            
                            # 🔧 [P1-2修复] 第五步：验证UI同步状态
                            self._verify_pagination_ui_sync(page, current_table_name, mapped_data)
                            
                        else:
                            self.logger.warning(f"🔧 [P0-2修复] 无法更新UI: 工作区或数据不可用")
                            
                    except Exception as ui_error:
                        self.logger.error(f"🔧 [P0-2修复] 分页UI更新失败: {ui_error}", exc_info=True)
                    
                    self.logger.info(f"🔧 [P0-2修复] 分页处理完成，UI已更新: 第{page}页")
                else:
                    self.logger.error(f"🔧 [根本修复] 新架构分页失败: {response.error}")
                    
                    # 🔧 [用户体验修复] 失败时也要清除加载状态
                    if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                        # 🚫 [用户要求] 不使用加载条，删除set_loading_state调用
                        pass
                        self.logger.info(f"🔄 [用户体验] 失败时加载状态已清除")
                
                # 🚫 [根本修复] 成功处理分页，直接返回，不再执行后续代码
                return
            else:
                self.logger.error("表格数据服务不可用或表名为空，无法处理分页")
                print(f"🔧 [紧急修复] 无法处理分页: 表名={current_table_name}, 服务可用={bool(self.table_data_service)}")
                
                # 尝试使用工作区的分页处理作为后备方案
                if hasattr(self.main_workspace, '_on_page_changed'):
                    self.main_workspace._on_page_changed(page)
                
        except Exception as e:
            self.logger.error(f"🚫 [立即修复] 分页处理失败: {e}", exc_info=True)

            # 🔧 [立即修复] 异常时标记分页操作失败
            try:
                pagination_manager.complete_pagination(current_table_name, page, sort_columns, success=False)
            except:
                pass  # 忽略清理时的异常

            # 发生异常时，确保用户能看到错误
            if hasattr(self.main_workspace, 'set_error_message'):
                self.main_workspace.set_error_message(f"分页处理失败: {str(e)}")
        finally:
            # 🔧 [立即修复] 使用原子操作确保处理标志被清除
            with self._pagination_lock:
                self._processing_pagination = False
                self.logger.info(f"🔧 [立即修复] 分页处理标志已原子清除: 第{page}页")

            # 🔧 [立即修复] 确保分页状态管理器状态清理
            try:
                if 'pagination_manager' in locals():
                    pagination_manager.complete_pagination(current_table_name, page, sort_columns, success=False)
            except:
                pass  # 忽略清理时的异常

            # 🔧 [立即修复] 使用线程安全的定时器清理操作上下文
            if hasattr(self, '_current_operation_context'):
                from src.utils.thread_safe_timer import safe_single_shot
                def delayed_context_clear():
                    if hasattr(self, '_current_operation_context'):
                        self._current_operation_context = {}
                        self.logger.debug(f"🔧 [延迟清理] 分页操作上下文已清理: 第{page}页")

                safe_single_shot(1000, delayed_context_clear)  # 使用线程安全定时器

            # 🔧 [P0-CRITICAL修复] 清除分页流程标志
            self._in_pagination_flow = False
            if hasattr(self, '_pagination_start_time'):
                duration = time.time() - self._pagination_start_time
                self.logger.debug(f"🔧 [P0-CRITICAL修复] 分页流程结束，耗时: {duration:.3f}秒")

            self.logger.info(f"🔧 [立即修复] 分页处理完成，标志已清除: 第{page}页")

    def _execute_pagination_data_load(self, table_name: str, page: int, sort_columns: list,
                                     pagination_manager, event_optimizer):
        """🔧 [立即修复] 执行实际的分页数据加载"""
        try:
            self.logger.info(f"🔧 [事件优化] 开始执行分页数据加载: 第{page}页")

            # 获取分页参数
            page_size = getattr(self.main_workspace.pagination_widget, 'page_size', 50) if hasattr(self.main_workspace, 'pagination_widget') else 50

            # 🔧 [立即修复] 使用强化的分页去重检查
            from src.core.request_deduplication_manager import RequestDeduplicationManager
            dedup_manager = RequestDeduplicationManager()

            # 🔧 [立即修复] 使用专门的分页去重方法防止循环调用
            if not dedup_manager.should_allow_pagination_request(table_name, page, sort_columns):
                self.logger.info(f"🚫 [立即修复] 分页请求被去重管理器拒绝（防循环）: 第{page}页")
                return

            # 标记分页请求开始
            dedup_manager.mark_pagination_request_started(table_name, page, sort_columns)

            try:
                # 调用数据服务获取分页数据
                response = self.table_data_service.get_paginated_data(
                    table_name=table_name,
                    page=page,
                    page_size=page_size,
                    sort_columns=sort_columns
                )

                if response.success and response.data is not None:
                    # 🔧 [事件优化] 使用事件优化器处理UI更新
                    def update_ui():
                        self._update_pagination_ui(response.data, page, table_name)

                    event_optimizer.add_event(
                        event_type=EventType.UI_UPDATE,
                        table_name=table_name,
                        page=page,
                        data={'response_data': response.data},
                        callback=update_ui,
                        priority=8  # 高优先级UI更新
                    )

                    self.logger.info(f"🔧 [事件优化] 分页数据加载成功，UI更新已加入队列: 第{page}页")
                else:
                    self.logger.error(f"🔧 [事件优化] 分页数据加载失败: {response.error if response else '无响应'}")

            finally:
                # 标记分页请求完成
                dedup_manager.mark_pagination_request_completed(table_name, page, sort_columns)
                pagination_manager.complete_pagination(table_name, page, sort_columns)

                # 清理处理标志
                with self._pagination_lock:
                    self._processing_pagination = False

        except Exception as e:
            self.logger.error(f"🔧 [事件优化] 分页数据加载执行失败: {e}", exc_info=True)

            # 确保清理状态
            try:
                pagination_manager.complete_pagination(table_name, page, sort_columns)
                with self._pagination_lock:
                    self._processing_pagination = False
            except:
                pass

    def _update_pagination_ui(self, data, page: int, table_name: str):
        """🔧 [渐进式修复] 更新分页UI"""
        try:
            # 🔧 [阶段2: 核心实现] 使用UnifiedFormatManager进行字段映射
            mapped_data = self._apply_unified_format_mapping(data, table_name)

            # 🔧 [P0修复] 更新主工作区表格 - 使用安全适配器
            if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                success = self._safe_set_data(self.main_workspace.expandable_table, mapped_data)
                if success:
                    self.logger.debug(f"🔧 [分页UI] 表格数据设置成功: {table_name}, 页={page}")
                else:
                    self.logger.warning(f"🔧 [分页UI] 表格数据设置失败: {table_name}, 页={page}")

                # 恢复表格UI状态
                self._restore_table_ui_state(table_name)

                # 强制刷新表头
                if hasattr(self.main_workspace.expandable_table, 'header_update_manager'):
                    headers = list(mapped_data.columns)
                    start_record = (page - 1) * 50 + 1
                    count = len(mapped_data)

                    self.main_workspace.expandable_table.header_update_manager.force_update_all(
                        headers=headers,
                        start_record=start_record,
                        count=count
                    )

                self.logger.info(f"🔧 [事件优化] UI更新完成: 第{page}页，{len(mapped_data)}条记录")

        except Exception as e:
            self.logger.error(f"🔧 [事件优化] UI更新失败: {e}", exc_info=True)

    def _apply_unified_format_mapping(self, data, table_name: str):
        """🔧 [方案B] 使用UnifiedFormatManager进行字段映射"""
        try:
            # Step 1: 确保unified_format_manager可用
            if not hasattr(self, 'unified_format_manager') or not self.unified_format_manager:
                if hasattr(self, 'architecture_factory') and self.architecture_factory:
                    try:
                        self.unified_format_manager = self.architecture_factory.get_unified_format_manager()
                        if self.unified_format_manager:
                            self.logger.debug("🔧 [格式映射] 通过架构工厂获取UnifiedFormatManager实例")
                    except Exception as e:
                        self.logger.warning(f"🔧 [格式映射] 获取UnifiedFormatManager失败: {e}")
            
            # Step 2: 使用统一格式管理器进行字段映射
            if self.unified_format_manager and hasattr(data, 'columns'):
                # 获取表类型
                if hasattr(self.main_workspace, 'expandable_table') and hasattr(self.main_workspace.expandable_table, '_extract_table_type_from_name'):
                    table_type = self.main_workspace.expandable_table._extract_table_type_from_name(table_name)
                else:
                    # 降级处理：从表名推断类型
                    table_type = self._infer_table_type_from_name(table_name)
                
                # 获取原始表头
                headers = list(data.columns)
                
                # 使用统一格式管理器格式化表头（方案C：统一出口）
                formatted_headers = self.unified_format_manager.format_headers(headers, table_type)
                
                # 创建映射后的数据副本
                mapped_data = data.copy()
                mapped_data.columns = formatted_headers
                
                self.logger.debug(f"🔧 [格式映射] 字段映射成功: {table_name}, {len(headers)}个字段")
                return mapped_data
            else:
                # 降级处理：直接使用原始数据
                self.logger.warning(f"🔧 [格式映射] UnifiedFormatManager不可用或数据格式不支持，使用原始表头: {table_name}")
                return data
                
        except Exception as e:
            # 异常情况下的降级处理
            self.logger.error(f"🔧 [格式映射] 字段映射失败: {e}", exc_info=True)
            return data

    def _infer_table_type_from_name(self, table_name: str) -> str:
        """🔧 [辅助方法] 从表名推断表类型"""
        table_name_lower = table_name.lower()
        if 'a_grade' in table_name_lower or 'a岗' in table_name:
            return 'a_grade_employees'
        elif 'active' in table_name_lower or '在职' in table_name:
            return 'active_employees'
        elif 'retired' in table_name_lower or '离休' in table_name:
            return 'retired_employees'
        else:
            return 'general'

    def _safe_set_data(self, table, data):
        """🔧 [P0修复] 安全的数据设置适配器 - 临时接口统一方案"""
        try:
            if data is None:
                self.logger.warning("🔧 [数据适配器] 接收到空数据，跳过设置")
                return False
                
            # 检测数据格式并进行适配
            if hasattr(data, 'columns') and hasattr(data, 'to_dict'):  # Pandas DataFrame
                try:
                    headers = list(data.columns)
                    data_list = data.to_dict('records')
                    
                    # 调用实际的set_data方法（Python命名规范）
                    table.set_data(data_list, headers)
                    
                    self.logger.debug(f"🔧 [数据适配器] DataFrame转换成功: {len(data_list)}行 x {len(headers)}列")
                    return True
                    
                except Exception as convert_error:
                    self.logger.error(f"🔧 [数据适配器] DataFrame转换失败: {convert_error}")
                    # 尝试降级处理
                    try:
                        if hasattr(table, 'clear'):
                            table.clear()
                            self.logger.debug("🔧 [数据适配器] 已清空表格作为降级处理")
                    except:
                        pass
                    return False
                    
            elif isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict):
                # 已经是List[Dict]格式，需要提取headers
                try:
                    headers = list(data[0].keys()) if data else []
                    table.set_data(data, headers)
                    
                    self.logger.debug(f"🔧 [数据适配器] List[Dict]格式直接调用成功: {len(data)}行")
                    return True
                    
                except Exception as list_error:
                    self.logger.error(f"🔧 [数据适配器] List[Dict]调用失败: {list_error}")
                    return False
                    
            else:
                # 其他格式，尝试直接调用
                try:
                    table.set_data(data)
                    self.logger.debug("🔧 [数据适配器] 其他格式直接调用成功")
                    return True
                    
                except Exception as other_error:
                    self.logger.error(f"🔧 [数据适配器] 其他格式调用失败: {other_error}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"🔧 [数据适配器] 适配器整体失败: {e}", exc_info=True)
            # 最后的降级处理
            try:
                if hasattr(table, 'clear'):
                    table.clear()
                    self.logger.debug("🔧 [数据适配器] 已执行最终降级处理")
            except:
                pass
            return False

    def _clear_loading_state(self):
        """🚫 [用户要求] 加载状态已彻底禁用"""
        # 不执行任何操作，加载条已被彻底删除
        pass
    
    @pyqtSlot(int)
    def _on_page_size_changed_new_architecture(self, page_size: int):
        """处理分页大小变化事件 - 新架构版本"""
        try:
            # 确保current_table_name属性存在
            current_table_name = getattr(self, 'current_table_name', None)
            if not current_table_name:
                current_table_name = getattr(self.main_workspace, 'current_table_name', None)

            # 使用新架构处理分页大小变化
            if self.table_data_service and current_table_name:
                self.logger.info(f"🆕 新架构分页大小变化: {page_size}条/页")
                
                # 获取当前排序状态
                sort_columns = []
                if hasattr(self, 'global_sort_enabled') and self.global_sort_enabled:
                    if hasattr(self, 'current_sort_columns') and self.current_sort_columns:
                        sort_columns = self.current_sort_columns
                        self.logger.info(f"新架构页面大小变化时应用排序: {len(sort_columns)}列")
                
                # 重新加载第一页数据
                response = self.table_data_service.load_table_data(
                    table_name=current_table_name,  # 使用安全获取的表名
                    page=1,
                    page_size=page_size,
                    sort_columns=sort_columns,  # 传递排序状态
                    force_reload=False  # 🚀 [性能修复] 启用缓存
                )
                
                if response.success:
                    self.logger.info(f"🆕 新架构分页大小变化成功: {page_size}条/页, {len(response.data)}行")
                    # 数据更新事件会自动通过事件总线处理UI更新
                else:
                    self.logger.error(f"🆕 新架构分页大小变化失败: {response.error}")
                
                return
                
            # 🔧 [P1-完成] 新架构处理失败，抛出异常
            raise RuntimeError(f"新架构分页处理失败: {page_size}条/页")
                
        except Exception as e:
            self.logger.error(f"处理分页大小变化事件失败: {e}")

    def _setup_ui(self):
        """初始化主窗口UI"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局（垂直三段式）
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Header区域
        self.header_widget = MaterialHeaderWidget()
        main_layout.addWidget(self.header_widget)
        
        # Workspace区域（水平分割）
        self.main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧导航面板
        self.navigation_panel = EnhancedNavigationPanel(
            dynamic_table_manager=self.dynamic_table_manager,
            comm_manager=self.comm_manager
        )
        self.main_splitter.addWidget(self.navigation_panel)
        
        # 主工作区域
        self.main_workspace = MainWorkspaceArea(
            table_manager=self.dynamic_table_manager,
            config_sync_manager=self.config_sync_manager
        )
        self.main_splitter.addWidget(self.main_workspace)
        
        # 将分割器设置为主布局
        main_layout.addWidget(self.main_splitter)
        
        self.footer_widget = MaterialFooterWidget()
        main_layout.addWidget(self.footer_widget)
        
        self.setCentralWidget(central_widget)
        self.setWindowTitle("月度工资异动处理系统 - 高保真原型 v2.0")
        
        # 用户偏好设置（用于保存UI状态等）
        self.user_preferences = UserPreferences()
        
        # 设置快捷键
        self._setup_shortcuts()

        self.logger.info("主窗口UI设置完成。")
    
    def _setup_shortcuts(self):
        """设置快捷键"""
        try:
            # Ctrl+M 切换菜单栏显示/隐藏
            toggle_menu_action = QAction(self)
            toggle_menu_action.setShortcut('Ctrl+M')
            toggle_menu_action.triggered.connect(self._on_settings_clicked)
            self.addAction(toggle_menu_action)
            
            self.logger.info("快捷键设置完成")
            
        except Exception as e:
            self.logger.error(f"设置快捷键失败: {e}")

    def _setup_managers(self):
        """初始化各种管理器"""
        # 响应式布局管理器
        self.responsive_manager = ResponsiveLayoutManager(self)
        
        # 组件通信管理器
        self.comm_manager = ComponentCommunicationManager(self)
        
        # 菜单栏管理器
        self.menu_bar_manager = MenuBarManager(self, self.config_manager)
        
        # 🔧 [P2-重影修复] 强制重置全局表头管理器实例，确保新修复生效
        reset_global_header_manager()

        # 表头管理器（增强版）
        self.header_manager = get_global_header_manager()
        
        # 连接表头管理器信号
        self.header_manager.header_cleaned.connect(self._on_header_cleaned)
        self.header_manager.shadow_detected.connect(self._on_header_shadow_detected)
        
        # 为菜单栏管理器提供清理回调
        if hasattr(self, 'menu_bar_manager') and self.menu_bar_manager:
            self.menu_bar_manager._enhanced_header_cleanup_callback = self._conditional_header_cleanup
        
        self.logger.info("管理器设置完成，包含增强版表头管理器")

    def _apply_window_style(self):
        """应用窗口级Material Design样式"""
        try:
            self.logger.info("🔧 开始应用窗口级Material Design样式...")
            
            from src.gui.style_manager import StyleManager
            self.style_manager = StyleManager.get_instance()
            
            # 应用窗口样式
            if self.style_manager.apply_window_style(self):
                self.logger.info("✅ 窗口级样式应用成功")
            else:
                self.logger.warning("⚠️ 窗口级样式应用失败")
            
            # 设置响应式样式监听
            self._setup_responsive_style()
            
        except Exception as e:
            self.logger.error(f"❌ 窗口样式应用失败: {e}")
    
    def _setup_responsive_style(self):
        """设置响应式样式"""
        try:
            self.logger.debug("设置响应式样式监听...")
            
            # 保存原始的resizeEvent方法
            self._original_resize_event = self.resizeEvent
            
            # 创建样式更新方法
            def on_resize():
                try:
                    if hasattr(self, 'style_manager') and self.style_manager:
                        self.style_manager.update_responsive_style(self.width(), self.height())
                except Exception as e:
                    self.logger.error(f"响应式样式更新失败: {e}")
            
            # 重写resizeEvent方法
            def enhanced_resize_event(event):
                # 调用原始的resizeEvent
                if self._original_resize_event:
                    self._original_resize_event(event)
                
                # 执行响应式样式更新
                on_resize()
            
            # 应用增强的resizeEvent
            self.resizeEvent = enhanced_resize_event
            
            # 初始化响应式样式
            on_resize()
            
            self.logger.info("✅ 响应式样式监听设置完成")
            
        except Exception as e:
            self.logger.error(f"❌ 响应式样式设置失败: {e}")

    def _apply_table_style(self, table_widget):
        """应用Material Design表格样式"""
        try:
            self.logger.debug("🔧 开始应用表格样式...")
            
            if not hasattr(self, 'style_manager') or not self.style_manager:
                self.logger.warning("⚠️ StyleManager未初始化，跳过表格样式应用")
                return
            
            # 应用表格样式
            if self.style_manager.apply_component_style(table_widget, "table"):
                self.logger.debug("✅ 表格样式应用成功")
            
            # 如果表格有表头，也应用表头样式
            if hasattr(table_widget, 'horizontalHeader'):
                header = table_widget.horizontalHeader()
                if header:
                    self.style_manager.apply_component_style(header, "table_header")
                    self.logger.debug("✅ 表头样式应用成功")
            
            # 设置表格属性以配合Material Design样式
            if hasattr(table_widget, 'setAlternatingRowColors'):
                table_widget.setAlternatingRowColors(True)
            
            if hasattr(table_widget, 'setSelectionBehavior'):
                from PyQt5.QtWidgets import QAbstractItemView
                table_widget.setSelectionBehavior(QAbstractItemView.SelectRows)
            
            self.logger.debug("✅ 表格Material Design样式应用完成")
            
        except Exception as e:
            self.logger.error(f"❌ 表格样式应用失败: {e}")

    def _apply_button_style(self, button, button_type="primary"):
        """应用Material Design按钮样式"""
        try:
            self.logger.debug(f"🔧 开始应用按钮样式: {button_type}")
            
            if not hasattr(self, 'style_manager') or not self.style_manager:
                self.logger.warning("⚠️ StyleManager未初始化，跳过按钮样式应用")
                return
            
            # 应用按钮样式
            style_key = f"button_{button_type}"
            if self.style_manager.apply_component_style(button, style_key):
                # 应用Material Design阴影效果
                try:
                    from src.gui.modern_style import ModernShadowEffects
                    shadow = ModernShadowEffects.create_button_shadow()
                    button.setGraphicsEffect(shadow)
                    self.logger.debug(f"✅ 按钮样式和阴影应用成功: {button_type}")
                except Exception as shadow_error:
                    self.logger.debug(f"⚠️ 按钮阴影应用失败: {shadow_error}")
            else:
                self.logger.warning(f"⚠️ 按钮样式应用失败: {button_type}")
            
            # 设置按钮属性以配合Material Design样式
            if hasattr(button, 'setCursor'):
                from PyQt5.QtCore import Qt
                button.setCursor(Qt.PointingHandCursor)
            
        except Exception as e:
            self.logger.error(f"❌ 按钮样式应用失败 [{button_type}]: {e}")

    def _apply_input_style(self, input_widget):
        """应用Material Design输入框样式"""
        try:
            self.logger.debug("🔧 开始应用输入框样式...")
            
            if not hasattr(self, 'style_manager') or not self.style_manager:
                self.logger.warning("⚠️ StyleManager未初始化，跳过输入框样式应用")
                return
            
            # 应用输入框样式
            if self.style_manager.apply_component_style(input_widget, "input"):
                self.logger.debug("✅ 输入框样式应用成功")
            else:
                self.logger.warning("⚠️ 输入框样式应用失败")
            
            # 设置输入框属性以配合Material Design样式
            if hasattr(input_widget, 'setMinimumWidth'):
                input_widget.setMinimumWidth(200)
            
            if hasattr(input_widget, 'setMinimumHeight'):
                input_widget.setMinimumHeight(32)
            
        except Exception as e:
            self.logger.error(f"❌ 输入框样式应用失败: {e}")

    def _setup_connections(self):
        """设置信号连接"""
        try:
            # 1. Header组件信号连接
            self.header_widget.settings_clicked.connect(self._on_settings_clicked)
            # 🔧 [全局排序] 尝试连接全局排序开关（方法在类中定义，可以直接连接）
            try:
                if hasattr(self.header_widget, 'global_sort_toggled'):
                    self.header_widget.global_sort_toggled.connect(self.toggle_global_sort)
                    self.logger.info("🔧 [全局排序] 全局排序开关连接成功")
                else:
                    self.logger.warning("🔧 [全局排序] header_widget没有global_sort_toggled信号，跳过连接")
            except Exception as e:
                self.logger.error(f"🔧 [全局排序] 连接全局排序开关失败: {e}")
            
            # 2. 主工作区信号连接
            self.main_workspace.import_requested.connect(self._on_import_data_requested)
            # 🔧 [P0-修复] 连接亮度修复请求信号
            self.main_workspace.brightness_fix_requested.connect(self._fix_table_display_brightness)
            # 🔧 [P0-2] 连接刷新数据请求信号
            self.main_workspace.refresh_requested.connect(self._on_refresh_data)
            
            # 3. 响应式布局信号连接
            self.responsive_manager.breakpoint_changed.connect(self._on_responsive_layout_changed)
            
            # 4. 菜单栏信号连接
            self.menu_bar_manager.visibility_changed.connect(self._on_menu_visibility_changed)
            self.menu_bar_manager.menu_action_triggered.connect(self._on_menu_action_triggered)
            
            # 5. 导航面板信号连接
            self.navigation_panel.navigation_changed.connect(self._on_navigation_changed)
            
            # 6. 表格组件信号连接
            if hasattr(self.main_workspace, 'data_table') and self.main_workspace.data_table:
                # 连接字段映射更新信号
                self.main_workspace.data_table.field_mapping_updated.connect(self._on_field_mapping_updated)
                # 连接表头编辑信号
                self.main_workspace.data_table.header_edited.connect(self._on_header_edited)
            
            # 🗑️ [新架构清理] 移除旧的排序信号连接，使用表格组件内部的自定义排序循环
            # 新架构中，排序完全由表格组件内部处理，通过事件总线与主窗口通信
            self.logger.info("🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号")
            
            # 8. 🆕 分页组件事件连接
            if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                # 连接分页变化事件
                if hasattr(self.main_workspace.pagination_widget, 'page_changed'):
                    self.main_workspace.pagination_widget.page_changed.connect(self._on_page_changed_new_architecture)
                if hasattr(self.main_workspace.pagination_widget, 'page_size_changed'):
                    self.main_workspace.pagination_widget.page_size_changed.connect(self._on_page_size_changed_new_architecture)
                self.logger.info("✅ 已连接分页组件事件到新架构")
            
            self.logger.info("信号连接设置完成")
            
        except Exception as e:
            self.logger.error(f"设置信号连接失败: {e}", exc_info=True)
    
    @pyqtSlot()
    def _on_settings_clicked(self):
        """处理设置按钮点击事件"""
        try:
            if self.menu_bar_manager:
                # 切换菜单栏显示状态
                self.menu_bar_manager.toggle_visibility()
                self.logger.info("设置按钮点击，切换菜单栏显示状态")
            else:
                self.logger.warning("菜单栏管理器未初始化")
                
        except Exception as e:
            self.logger.error(f"处理设置按钮点击失败: {e}")
    
    @pyqtSlot(bool)
    def _on_menu_visibility_changed(self, visible: bool):
        """
        菜单栏可见性变化处理（增强版，仅在必要时清理表头）
        
        Args:
            visible: 菜单栏是否可见
        """
        try:
            self.logger.info(f"菜单栏可见性变化: {visible}")
            
            # 触发全面的布局刷新
            self.refresh_layout()
            
            # 仅在菜单栏切换时，延迟执行一次表头清理
            # 合并进统一调度
            self._schedule_ui_fix(240)
            
            # 更新状态信息
            status_msg = "菜单栏已显示" if visible else "菜单栏已隐藏"
            if hasattr(self, 'main_workspace'):
                self.main_workspace._update_status_label(status_msg, "info")
                
        except Exception as e:
            self.logger.error(f"处理菜单栏可见性变化失败: {e}")

    def refresh_layout(self):
        """
        全面刷新窗口布局（增强版，集成表头管理器）
        
        用于菜单栏状态变化等需要重新调整整体布局的场景
        """
        try:
            # 1. 预先注册所有表格到表头管理器
            self._register_all_tables_to_header_manager()
            
            # 2. 【修复闪动】只更新窗口，不强制立即重绘
            self.update()  # 移除repaint()，让Qt控制绘制时机
            
            # 3. 更新中央部件
            central_widget = self.centralWidget()
            if central_widget:
                central_widget.updateGeometry()
                central_widget.update()
            
            # 4. 更新主要组件
            components = [
                ('header_widget', self.header_widget if hasattr(self, 'header_widget') else None),
                ('navigation_panel', self.navigation_panel if hasattr(self, 'navigation_panel') else None),
                ('main_workspace', self.main_workspace if hasattr(self, 'main_workspace') else None),
                ('footer_widget', self.footer_widget if hasattr(self, 'footer_widget') else None)
            ]
            
            for name, component in components:
                if component:
                    component.updateGeometry()
                    component.update()
                    self.logger.debug(f"已刷新组件: {name}")
            
            # 4. 触发响应式管理器更新 (延迟执行，确保布局稳定)
            if hasattr(self, 'responsive_manager') and self.responsive_manager:
                # 合并进统一调度
                self._schedule_ui_fix(260)
            
            # 5. 特别处理表格组件的视口更新
            if hasattr(self, 'main_workspace') and self.main_workspace:
                if hasattr(self.main_workspace, 'data_table') and self.main_workspace.data_table:
                    self.main_workspace.data_table.viewport().update()
                    self.main_workspace.data_table.updateGeometry()
            
            self.logger.info("窗口布局全面刷新完成")
            
        except Exception as e:
            self.logger.error(f"刷新窗口布局失败: {e}")

    @pyqtSlot(str)
    def _on_menu_action_triggered(self, action_name: str):
        """处理菜单动作触发"""
        try:
            self.logger.info(f"菜单动作触发: {action_name}")
            
            # 根据动作名称执行相应操作
            if action_name == "import_data":
                self._on_import_data_requested()
            elif action_name == "export_report":
                self.main_workspace._on_export_report()
            elif action_name == "show_about":
                self._show_about_dialog()
            elif action_name == "customize_headers":
                self._show_header_customization_dialog()
            elif action_name == "reset_headers":
                self._reset_headers_to_default()
            # 可以根据需要添加更多菜单动作处理
            
        except Exception as e:
            self.logger.error(f"处理菜单动作失败: {action_name}, 错误: {e}")

    @pyqtSlot(str, dict)
    def _on_field_mapping_updated(self, table_name: str, mapping: dict):
        """处理字段映射更新 - 使用统一的ConfigSyncManager"""
        try:
            self.logger.info(f"字段映射已更新: {table_name}")
            
            # 更新内存中的映射
            self.field_mappings[table_name] = mapping
            
            # 使用ConfigSyncManager保存映射
            if hasattr(self, 'config_sync_manager'):
                success = self.config_sync_manager.save_mapping(table_name, mapping)
                if success:
                    self.logger.info(f"字段映射已成功保存到ConfigSyncManager: {table_name}")
                else:
                    self.logger.error(f"保存字段映射到ConfigSyncManager失败: {table_name}")
            
            # 重新加载当前表格数据以应用新的映射
            if hasattr(self, 'current_nav_path') and len(self.current_nav_path) == 4:
                current_table_name = self._generate_table_name_from_path(self.current_nav_path)
                if current_table_name == table_name:
                    self.logger.info(f"重新加载当前表格以应用新的字段映射: {table_name}")
                    self._reload_current_table_data()
            
        except Exception as e:
            self.logger.error(f"处理字段映射更新失败: {e}", exc_info=True)

    @pyqtSlot(int, str, str)
    def _on_header_edited(self, column: int, old_name: str, new_name: str):
        """处理表头编辑"""
        try:
            self.logger.info(f"表头已编辑: 列{column}, {old_name} -> {new_name}")
            
            # 立即刷新表格显示以应用新的表头名称
            self._refresh_table_headers()
            
        except Exception as e:
            self.logger.error(f"处理表头编辑失败: {e}", exc_info=True)

    def _reload_current_table_data(self):
        """重新加载当前表格数据"""
        try:
            if hasattr(self, 'current_nav_path') and len(self.current_nav_path) == 4:
                table_name = self._generate_table_name_from_path(self.current_nav_path)
                
                # 检查是否是分页模式
                if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget and self.main_workspace.pagination_widget.isVisible():
                    # 分页模式：重新加载当前页
                    current_page = self.main_workspace.pagination_widget.get_current_page()
                    page_size = self.main_workspace.pagination_widget.get_page_size()
                    self._load_data_with_pagination(table_name, current_page, page_size)
                else:
                    # 普通模式：重新加载全部数据
                    worker = Worker(self._load_database_data_with_mapping, table_name)
                    worker.signals.result.connect(lambda df: self.main_workspace.set_data(df, preserve_headers=True, table_name=table_name, current_table_name=table_name))
                    self.thread_pool.start(worker)
                    
        except Exception as e:
            self.logger.error(f"重新加载当前表格数据失败: {e}", exc_info=True)

    def _refresh_table_headers(self):
        """刷新表格表头显示"""
        try:
            if hasattr(self.main_workspace, 'data_table') and self.main_workspace.data_table:
                # 直接调用表格的表头刷新方法
                self.main_workspace.data_table._refresh_header_display()
                self.logger.debug("表格表头显示已刷新")
                
        except Exception as e:
            self.logger.error(f"刷新表格表头显示失败: {e}", exc_info=True)

    def _show_about_dialog(self):
        """显示关于对话框"""
        try:
            about_text = """
            月度工资异动处理系统 v2.0
            
            这是一个现代化的工资异动处理系统，采用PyQt5技术栈开发。
            
            主要功能：
            • 数据导入和验证
            • 异动检测和分析  
            • 报告生成和导出
            • 隐藏式菜单栏
            
            开发团队：系统开发团队
            技术栈：Python + PyQt5 + SQLite
            """
            
            QMessageBox.about(self, "关于系统", about_text)
            
        except Exception as e:
            self.logger.error(f"显示关于对话框失败: {e}")
    
    @pyqtSlot(str, dict)
    def _on_responsive_layout_changed(self, breakpoint: str, config: dict):
        """处理响应式布局变化"""
        try:
            # 传递给各个组件
            if hasattr(self, 'header_widget'):
                self.header_widget.handle_responsive_change(breakpoint, config)
            
            if hasattr(self, 'main_workspace'):
                self.main_workspace.handle_responsive_change(breakpoint, config)
                
            if hasattr(self, 'footer_widget'):
                self.footer_widget.handle_responsive_change(breakpoint, config)
                
            self.logger.debug(f"响应式布局变化: {breakpoint}")
            
        except Exception as e:
            self.logger.error(f"处理响应式布局变化失败: {e}")
    
    @pyqtSlot()
    def _on_import_data_requested(self):
        """响应数据导入请求，打开导入对话框。"""
        suggested_path = self._get_suggested_target_path()
        self.logger.info(f"接收到数据导入请求，推断的目标路径: {suggested_path}。打开导入对话框。")

        dialog = DataImportDialog(
            parent=self,
            dynamic_table_manager=self.dynamic_table_manager,
            target_path=suggested_path
        )

        # 设置ConfigSyncManager到MultiSheetImporter
        if hasattr(self, 'config_sync_manager') and self.config_sync_manager is not None:
            dialog.multi_sheet_importer.set_config_sync_manager(self.config_sync_manager)
            self.logger.info("🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框")
        else:
            # 🔧 [P0-修复] 如果ConfigSyncManager不存在，尝试从架构工厂获取
            if hasattr(self, 'architecture_factory') and self.architecture_factory and self.architecture_factory.is_initialized():
                try:
                    self.config_sync_manager = self.architecture_factory.get_config_sync_manager()
                    dialog.multi_sheet_importer.set_config_sync_manager(self.config_sync_manager)
                    self.logger.info("🔧 [P0-修复] 通过架构工厂获取ConfigSyncManager并设置到导入对话框")
                except Exception as e:
                    self.logger.error(f"🔧 [P0-修复] 从架构工厂获取ConfigSyncManager失败: {e}")
            else:
                self.logger.warning("ConfigSyncManager不可用，数据导入可能受影响")

        dialog.data_imported.connect(self._handle_data_imported)
        dialog.exec_()
    
    @pyqtSlot(dict)
    def _handle_data_imported(self, import_result: dict):
        """处理数据导入完成后的逻辑 - 增强版本，解决时序和数据一致性问题"""
        self.logger.info(f"开始处理导入结果: {import_result}")

        if not import_result.get("success"):
            error_message = import_result.get("error", "未知导入错误")
            self._show_error_message("导入失败", error_message)
            return

        import_mode = import_result.get("import_mode", "single_sheet")
        target_path_str = import_result.get("target_path", "")
        source_file = import_result.get("source_file", "未知来源")
        
        self.logger.info(f"导入模式: {import_mode}, 目标路径: '{target_path_str}'")

        # 数据验证增强
        if import_mode == 'single_sheet':
            df = import_result.get("dataframe")
            if df is None:
                self.logger.error("导入结果中缺少dataframe")
                self._show_warning_message("导入结果", "导入成功，但未返回有效数据。")
                return
            if df.empty:
                self.logger.warning("导入的dataframe为空")
                self._show_warning_message("导入结果", "导入成功，但数据为空。")
                return
            
            # 验证数据完整性
            self.logger.info(f"验证导入数据: {len(df)} 行, {len(df.columns)} 列")
            self.logger.info(f"数据列名: {list(df.columns)}")
        
        self.logger.info(f"接收到导入数据, 来源: {source_file}, 目标路径: {target_path_str}")
        
        # 从目标路径字符串解析路径列表
        if target_path_str:
            target_path_list = target_path_str.split(' > ')
        else:
            # 如果没有目标路径，使用当前导航路径作为后备
            target_path_list = self.current_nav_path if self.current_nav_path else []
            self.logger.info(f"目标路径为空，使用当前导航路径作为后备: {target_path_list}")
            
        if not target_path_list or len(target_path_list) < 4:
            self.logger.error(f"目标路径不完整或无效: {target_path_list}。无法保存数据。")
            self._show_error_message("保存失败", f"目标路径不完整: {target_path_str}")
            # 即便路径不完整，也尝试刷新导航，以便用户能看到新导入的数据
            self._update_navigation_if_needed(target_path_list)
            return

        # 仅单Sheet模式需要处理字段映射和数据保存
        if import_mode == 'single_sheet':
            df = import_result.get("dataframe")
            field_mapping = import_result.get("field_mapping", {})
            table_name = self._generate_table_name_from_path(target_path_list)

            # 增强的字段映射保存
            mapping_saved = False
            if field_mapping:
                self.logger.info(f"保存字段映射信息到表 {table_name}: {field_mapping}")
                self.field_mappings[table_name] = field_mapping
                
                # 使用ConfigSyncManager保存映射
                if hasattr(self, 'config_sync_manager'):
                    try:
                        success = self.config_sync_manager.save_mapping(table_name, field_mapping)
                        if success:
                            self.logger.info(f"字段映射已成功保存到ConfigSyncManager: {table_name}")
                            mapping_saved = True
                        else:
                            self.logger.error(f"保存字段映射到ConfigSyncManager失败: {table_name}")
                    except Exception as e:
                        self.logger.error(f"ConfigSyncManager保存异常: {e}")
                
                # 如果ConfigSyncManager保存失败，使用备用方法
                if not mapping_saved:
                    try:
                        self._save_field_mappings_to_file()
                        mapping_saved = True
                        self.logger.info("字段映射已保存到文件（备用方法）")
                    except Exception as e:
                        self.logger.error(f"备用字段映射保存失败: {e}")

            # 数据库保存增强 - 增加验证步骤
            db_save_success = False
            try:
                self.logger.info(f"开始保存数据到数据库: 表名 {table_name}")
                self._save_to_database(df, target_path_list)
                
                # 验证数据是否真的保存成功
                if self._verify_data_saved(table_name, len(df)):
                    db_save_success = True
                    self.logger.info(f"数据库保存验证成功: {table_name}")
                else:
                    self.logger.error(f"数据库保存验证失败: {table_name}")
                    
            except Exception as e:
                self.logger.error(f"数据库保存失败: {e}")
                import traceback
                self.logger.error(f"保存失败详情: {traceback.format_exc()}")
            
            # 只有在数据成功保存后才刷新界面
            if db_save_success:
                # 如果导入的路径与当前选择的路径一致，延迟刷新显示确保数据一致性
                current_path_str = " > ".join(self.current_nav_path) if self.current_nav_path else ""
                if target_path_str == current_path_str:
                    self.logger.info("导入路径与当前路径一致，执行延迟刷新")
                    # 延迟刷新确保数据库事务完成
                    safe_single_shot(500, lambda: self._refresh_current_data_display(table_name))
                else:
                    self._show_info_message("导入成功", 
                        f"数据已成功导入到: {target_path_str}\n如需查看，请在左侧导航栏中选择对应位置。")
            else:
                self._show_error_message("导入警告", "数据可能未正确保存，请检查系统日志或重试导入。")
        
        # 对于所有成功的导入，都通知导航面板更新（延迟执行确保数据库操作完成）
        safe_single_shot(800, lambda: self._update_navigation_if_needed(target_path_list))
    
    def _update_navigation_if_needed(self, target_path_list: List[str]):
        """如果需要，更新导航面板（添加新的年份、月份或类别）"""
        try:
            self.logger.info(f"检查是否需要更新导航面板: {target_path_list}")
            
            # 检查是否是工资数据导入
            if len(target_path_list) >= 4 and target_path_list[0] == "工资表":
                self.logger.info("检测到工资数据导入，开始刷新导航面板")
                
                # 使用强制刷新工资数据导航的方法
                if hasattr(self, 'navigation_panel') and hasattr(self.navigation_panel, 'force_refresh_salary_data'):
                    self.logger.info("使用强制刷新方法")
                    self.navigation_panel.force_refresh_salary_data()
                    
                    # 延迟导航到新导入的路径，给刷新过程足够时间
                    target_path_str = " > ".join(target_path_list)
                    self.logger.info(f"将在1500ms后导航到: {target_path_str}")

                    # 使用线程安全的延迟执行
                    self._schedule_safe_navigation(target_path_str, target_path_list)
                    
                elif hasattr(self, 'navigation_panel') and hasattr(self.navigation_panel, 'refresh_navigation_data'):
                    self.logger.info("使用完整刷新方法")
                    # 后备方案：完整刷新导航面板
                    self.navigation_panel.refresh_navigation_data()
                    
                    # 立即同步导航，不需要延迟
                    target_path_str = " > ".join(target_path_list)
                    self.logger.info(f"立即导航到: {target_path_str}")
                    self._navigate_to_imported_path(target_path_str)

                    # 立即刷新数据显示
                    self._refresh_current_data_display(self._generate_table_name_from_path(target_path_list))
                    
                else:
                    self.logger.warning("导航面板不支持动态刷新")
            else:
                self.logger.info("非工资数据导入，使用标准刷新")
                # 非工资数据导入，使用标准刷新
                if hasattr(self, 'navigation_panel') and hasattr(self.navigation_panel, 'refresh_navigation_data'):
                    self.navigation_panel.refresh_navigation_data()
                    self.logger.info("导航面板已刷新")
                else:
                    self.logger.warning("导航面板不支持动态刷新")
            
        except Exception as e:
            self.logger.error(f"更新导航面板失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 不影响主要功能，只记录错误

    def _schedule_safe_navigation(self, target_path_str: str, target_path_list: list):
        """线程安全的导航调度"""
        try:
            # 立即同步导航，不需要延迟
            self._safe_navigate_to_imported_path(target_path_str)
            
            # 立即刷新数据显示
            table_name = self._generate_table_name_from_path(target_path_list)
            self._safe_refresh_current_data_display(table_name)
            
            self.logger.info(f"同步导航完成: {target_path_str}")

        except Exception as e:
            self.logger.error(f"同步导航失败: {e}")

    def _safe_navigate_to_imported_path(self, target_path: str):
        """线程安全的导航到导入路径"""
        try:
            self._navigate_to_imported_path(target_path)
        except Exception as e:
            self.logger.error(f"安全导航失败: {e}")

    def _safe_refresh_current_data_display(self, table_name: str):
        """线程安全的刷新当前数据显示"""
        try:
            self._refresh_current_data_display(table_name)
        except Exception as e:
            self.logger.error(f"安全刷新失败: {e}")

    def _navigate_to_imported_path(self, target_path_str: str):
        """导航到新导入的数据路径"""
        try:
            self.logger.info(f"尝试导航到新导入的路径: {target_path_str}")
            
            if hasattr(self, 'navigation_panel') and hasattr(self.navigation_panel, 'navigate_to_path'):
                # 调用导航面板的导航方法
                self.navigation_panel.navigate_to_path(target_path_str)
                self.logger.info(f"已成功导航到新导入的路径: {target_path_str}")
                
                # 同时更新主窗口的状态栏提示
                self._update_status_label(f"已导航到新导入的数据: {target_path_str}", "success")
                
            else:
                self.logger.warning("导航面板不支持路径导航")
                # 至少更新状态栏告知用户
                self._update_status_label(f"数据导入成功，请在左侧导航栏查看: {target_path_str}", "info")
                
        except Exception as e:
            self.logger.error(f"导航到导入路径失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _refresh_current_data_display(self, table_name: str):
        """刷新当前数据显示"""
        try:
            self.logger.info(f"🔧 [数据刷新] 开始刷新数据显示: {table_name}")

            # 清除缓存，强制重新加载数据
            if hasattr(self, 'data_cache'):
                self.data_cache.clear()
                self.logger.info("🔧 [数据刷新] 已清除数据缓存")

            # 获取当前排序状态
            sort_columns = []
            if hasattr(self, 'global_sort_enabled') and self.global_sort_enabled:
                if hasattr(self, 'current_sort_columns') and self.current_sort_columns:
                    sort_columns = self.current_sort_columns
                    self.logger.info(f"数据刷新时应用排序: {len(sort_columns)}列")
                    print(f"数据刷新时应用排序: {len(sort_columns)}列")

            # 使用新架构加载数据
            if hasattr(self, 'table_data_service') and self.table_data_service:
                response = self.table_data_service.load_table_data(
                    table_name=table_name,
                    page=1,
                    page_size=50,
                    sort_columns=sort_columns,  # 传递排序状态
                    force_reload=False  # 🚀 [性能修复] 启用缓存
                )

                if response.success:
                    self.logger.info(f"🔧 [数据刷新] 数据刷新成功: {len(response.data)}行")
                else:
                    self.logger.error(f"🔧 [数据刷新] 数据刷新失败: {response.error}")
            else:
                self.logger.warning("🔧 [数据刷新] TableDataService不可用")

        except Exception as e:
            self.logger.error(f"🔧 [数据刷新] 刷新数据显示失败: {e}")

    def _verify_data_saved(self, table_name: str, expected_count: int) -> bool:
        """验证数据是否成功保存到数据库"""
        try:
            if not hasattr(self, 'dynamic_table_manager') or not self.dynamic_table_manager:
                self.logger.warning("动态表管理器不可用，无法验证数据保存")
                return False
            
            # 检查表是否存在
            if not self.dynamic_table_manager.table_exists(table_name):
                self.logger.error(f"表 {table_name} 不存在，数据保存失败")
                return False
            
            # 检查记录数量
            df_saved, _ = self.dynamic_table_manager.get_dataframe_paginated(table_name, 1, expected_count + 10)
            if df_saved is None:
                self.logger.error(f"无法从表 {table_name} 读取数据")
                return False
            
            actual_count = len(df_saved)
            self.logger.info(f"数据保存验证: 期望 {expected_count} 条，实际 {actual_count} 条")
            
            # 允许一定的误差（可能有重复数据处理）
            if actual_count >= expected_count:
                return True
            else:
                self.logger.warning(f"保存的数据量不符合期望: {actual_count} < {expected_count}")
                return False
                
        except Exception as e:
            self.logger.error(f"验证数据保存时发生异常: {e}")
            return False
    
    def _refresh_current_data_display(self, table_name: str):
        """智能数据显示刷新 - 根据数据量自动选择显示策略"""
        try:
            self.logger.info(f"[数据流追踪] 开始智能数据显示刷新: {table_name}")
            
            # Step 1: 获取数据基本信息
            total_records = self._get_table_total_records(table_name)
            if total_records < 0:
                self.logger.error(f"无法获取表 {table_name} 的记录数，使用降级处理")
                self._fallback_to_pagination(table_name)
                return
            
            # Step 2: 智能策略决策
            from src.core.smart_pagination_strategy import get_smart_pagination_strategy
            strategy_manager = get_smart_pagination_strategy()
            
            # 获取用户偏好（如果有配置的话）
            user_preference = getattr(self, '_pagination_preference', None)
            
            decision = strategy_manager.should_use_pagination(
                total_records=total_records,
                page_size=50,  # 使用默认页面大小
                user_preference=user_preference
            )
            
            self.logger.info(f"[数据流追踪] 智能分页策略决策: {decision.strategy}, "
                           f"原因={decision.reason}, 预期性能={decision.estimated_performance_ms}ms")
            
            # Step 3: 执行对应策略
            if decision.use_pagination:
                self._execute_pagination_mode(table_name, total_records, decision)
            else:
                self._execute_full_display_mode(table_name, total_records, decision)
                
        except Exception as e:
            self.logger.error(f"智能显示策略失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            # 降级处理
            self._fallback_to_pagination(table_name)
    
    def _get_table_total_records(self, table_name: str) -> int:
        """获取表的总记录数"""
        try:
            if hasattr(self, 'table_data_service') and self.table_data_service:
                count = self.table_data_service.get_table_record_count(table_name)
                self.logger.debug(f"[数据流追踪] 获取表 {table_name} 记录数: {count}")
                return count
            else:
                self.logger.warning(f"TableDataService不可用，无法获取表 {table_name} 记录数")
                return -1
        except Exception as e:
            self.logger.error(f"获取表记录数失败: {e}")
            return -1
    
    def _execute_full_display_mode(self, table_name: str, total_records: int, decision):
        """执行全量显示模式"""
        try:
            self.logger.info(f"[数据流追踪] 执行全量显示模式: {table_name}, {total_records}条记录")
            
            # 隐藏分页组件
            self._set_pagination_visibility(False)
            
            # 标记为小数据集模式
            self._current_display_mode = 'full_display'
            
            # 获取排序状态
            sort_columns = getattr(self, 'current_sort_columns', []) or []
            
            # 加载全部数据 - 使用足够大的页面大小确保获取全部数据
            effective_page_size = max(total_records + 10, 100)
            
            if hasattr(self, 'table_data_service') and self.table_data_service:
                response = self.table_data_service.load_table_data(
                    table_name=table_name,
                    page=1,
                    page_size=effective_page_size,
                    sort_columns=sort_columns,
                    force_reload=False
                )
                
                if response.success:
                    self.logger.info(f"[数据流追踪] 全量显示模式加载成功: {len(response.data)}行数据")
                    # 更新状态栏显示
                    self._update_status_label_for_full_display(total_records, decision.estimated_performance_ms)
                else:
                    self.logger.error(f"全量显示模式加载失败: {response.error}")
                    self._fallback_to_pagination(table_name)
            else:
                self.logger.error("TableDataService不可用，降级到分页模式")
                self._fallback_to_pagination(table_name)
                
        except Exception as e:
            self.logger.error(f"执行全量显示模式失败: {e}")
            self._fallback_to_pagination(table_name)
    
    def _execute_pagination_mode(self, table_name: str, total_records: int, decision):
        """执行分页显示模式"""
        try:
            self.logger.info(f"[数据流追踪] 执行分页显示模式: {table_name}, {total_records}条记录")
            
            # 显示分页组件
            self._set_pagination_visibility(True)
            
            # 标记为分页模式
            self._current_display_mode = 'pagination'
            
            # 计算页数
            page_size = decision.recommended_page_size or 50
            pages_count = (total_records + page_size - 1) // page_size
            
            # 正常分页加载
            self._load_data_with_pagination(table_name, page=1, page_size=page_size)
            
            # 更新状态栏显示
            self._update_status_label_for_pagination(total_records, pages_count)
            
            self.logger.info(f"[数据流追踪] 分页显示模式: {total_records}条记录分{pages_count}页显示")
            
        except Exception as e:
            self.logger.error(f"执行分页显示模式失败: {e}")
            self._fallback_to_pagination(table_name)
    
    def _set_pagination_visibility(self, visible: bool):
        """设置分页组件可见性"""
        try:
            if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                self.main_workspace.pagination_widget.setVisible(visible)
                self.logger.debug(f"[数据流追踪] 分页组件可见性设置为: {visible}")
            else:
                self.logger.debug("分页组件不存在，跳过可见性设置")
        except Exception as e:
            self.logger.error(f"设置分页组件可见性失败: {e}")
    
    def _update_status_label_for_full_display(self, total_records: int, estimated_ms: int):
        """更新状态栏 - 全量显示模式"""
        try:
            status_text = f"全量显示 {total_records}条记录 (预计{estimated_ms}ms)"
            self._update_status_label(status_text, "success")
        except Exception as e:
            self.logger.error(f"更新状态栏失败: {e}")
    
    def _update_status_label_for_pagination(self, total_records: int, pages_count: int):
        """更新状态栏 - 分页显示模式"""
        try:
            status_text = f"分页显示 共{total_records}条记录，{pages_count}页"
            self._update_status_label(status_text, "info")
        except Exception as e:
            self.logger.error(f"更新状态栏失败: {e}")
    
    def _fallback_to_pagination(self, table_name: str):
        """降级处理 - 使用传统分页模式"""
        try:
            self.logger.warning(f"[数据流追踪] 降级到传统分页模式: {table_name}")
            
            # 显示分页组件
            self._set_pagination_visibility(True)
            
            # 使用原有的分页逻辑
            if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                current_page = self.main_workspace.pagination_widget.get_current_page()
                page_size = self.main_workspace.pagination_widget.get_page_size()
                self._load_data_with_pagination(table_name, current_page, page_size)
            else:
                # 如果没有分页组件，使用非分页模式
                worker = Worker(self._load_database_data_with_mapping, table_name)
                worker.signals.result.connect(
                    lambda df: self._handle_refresh_result(df, table_name)
                )
                worker.signals.error.connect(
                    lambda error_info: self.logger.error(f"数据刷新失败: {error_info}")
                )
                self.thread_pool.start(worker)
                
        except Exception as e:
            self.logger.error(f"降级处理失败: {e}")
    
    def _handle_refresh_result(self, df, table_name: str):
        """处理数据刷新结果"""
        try:
            if df is not None and not df.empty:
                self.main_workspace.set_data(df, preserve_headers=True, table_name=table_name)
                self.logger.info(f"数据刷新成功: {len(df)}行数据已显示")
                self._update_status_label(f"数据已刷新: {len(df)}条记录", "success")
            else:
                self.logger.warning("刷新后的数据为空")
                self._update_status_label("数据刷新完成，但结果为空", "warning")
        except Exception as e:
            self.logger.error(f"处理刷新结果失败: {e}")
            self._update_status_label("数据刷新失败", "error")
    
    def _emergency_init_architecture_factory(self):
        """🔧 [P0-CRITICAL] 紧急初始化架构工厂"""
        try:
            self.logger.warning("🔧 [P0-CRITICAL] 执行架构工厂紧急初始化")
            
            # 导入新架构组件
            from src.core.architecture_factory import get_architecture_factory
            
            # 确保依赖项可用
            if not hasattr(self, 'dynamic_table_manager') or not self.dynamic_table_manager:
                from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
                database_manager = getattr(self, 'database_manager', None)
                if database_manager:
                    self.dynamic_table_manager = DynamicTableManager(database_manager)
                else:
                    self.logger.error("🔧 [P0-CRITICAL] 数据库管理器不可用，无法紧急初始化")
                    return False
            
            if not hasattr(self, 'config_manager') or not self.config_manager:
                from src.modules.system_config.config_manager import ConfigManager
                self.config_manager = ConfigManager()
            
            # 初始化架构工厂
            self.architecture_factory = get_architecture_factory(
                self.dynamic_table_manager, 
                self.config_manager
            )
            
            # 初始化新架构系统
            if not self.architecture_factory.initialize_architecture():
                self.logger.error("🔧 [P0-CRITICAL] 紧急初始化架构失败")
                return False

            # 获取关键组件
            self.config_sync_manager = self.architecture_factory.get_config_sync_manager()
            self.event_bus = self.architecture_factory.get_event_bus()
            
            self.logger.info("🔧 [P0-CRITICAL] 架构工厂紧急初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"🔧 [P0-CRITICAL] 架构工厂紧急初始化失败: {e}")
            return False

    def _handle_data_operation_error(self, operation: str, error: Exception, context: dict = None) -> dict:
        """统一的数据操作错误处理方法
        
        Args:
            operation: 操作名称（如"数据导入"、"分页加载"等）
            error: 异常对象
            context: 额外的上下文信息
            
        Returns:
            错误信息字典，包含错误类型、消息、建议等
        """
        import traceback
        
        error_info = {
            'operation': operation,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'timestamp': datetime.now().isoformat(),
            'context': context or {}
        }
        
        # 根据错误类型提供具体建议
        if isinstance(error, sqlite3.Error):
            error_info['category'] = 'database_error'
            error_info['suggestions'] = [
                '检查数据库文件是否存在且可访问',
                '验证数据库表结构是否正确',
                '检查是否有其他程序占用数据库'
            ]
        elif isinstance(error, FileNotFoundError):
            error_info['category'] = 'file_error'
            error_info['suggestions'] = [
                '确认文件路径是否正确',
                '检查文件是否被移动或删除',
                '验证文件访问权限'
            ]
        elif isinstance(error, pd.errors.EmptyDataError):
            error_info['category'] = 'data_error'
            error_info['suggestions'] = [
                '检查数据文件是否为空',
                '验证数据格式是否正确',
                '确认是否选择了正确的工作表'
            ]
        elif isinstance(error, KeyError):
            error_info['category'] = 'mapping_error'
            error_info['suggestions'] = [
                '检查字段映射配置是否正确',
                '验证数据表结构是否与期望一致',
                '更新字段映射规则'
            ]
        else:
            error_info['category'] = 'unknown_error'
            error_info['suggestions'] = [
                '查看详细日志了解错误原因',
                '尝试重新执行操作',
                '联系技术支持'
            ]
        
        # 🔧 [P2-优化] 简化错误日志记录
        self.logger.error(f"{operation}失败: {error_info['error_type']} - {error_info['error_message']}")
        if error_info.get('context'):
            self.logger.debug(f"错误上下文: {error_info['context']}")
        
        return error_info
    
    def _show_enhanced_error_dialog(self, error_info: dict):
        """显示增强的错误对话框"""
        try:
            from PyQt5.QtWidgets import QMessageBox, QTextEdit
            
            msg_box = QMessageBox(self)
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle(f"{error_info['operation']}失败")
            
            # 主要错误信息
            main_text = f"{error_info['operation']}时发生错误:\\n{error_info['error_message']}"
            msg_box.setText(main_text)
            
            # 详细信息和建议
            detailed_text = f"错误类型: {error_info['error_type']}\\n"
            detailed_text += f"错误分类: {error_info['category']}\\n"
            detailed_text += f"发生时间: {error_info['timestamp']}\\n\\n"
            
            if error_info.get('suggestions'):
                detailed_text += "建议解决方案:\\n"
                for i, suggestion in enumerate(error_info['suggestions'], 1):
                    detailed_text += f"{i}. {suggestion}\\n"
            
            if error_info.get('context'):
                detailed_text += f"\\n上下文信息: {error_info['context']}"
            
            msg_box.setDetailedText(detailed_text)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec_()
            
        except Exception as e:
            # 如果增强对话框失败，回退到简单消息
            self.logger.error(f"显示增强错误对话框失败: {e}")
            self._show_error_message(error_info['operation'] + "失败", error_info['error_message'])

    def _save_field_mappings_to_file(self):
        """将字段映射信息保存到文件"""
        try:
            import json
            import os
            
            # 确保目录存在
            mapping_dir = "state/data"
            os.makedirs(mapping_dir, exist_ok=True)
            
            mapping_file = os.path.join(mapping_dir, "field_mappings.json")
            
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(self.field_mappings, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"字段映射信息已保存到: {mapping_file}")
            
        except Exception as e:
            self.logger.error(f"保存字段映射信息失败: {e}")

    def _load_field_mappings_from_file(self):
        """从文件加载字段映射信息 - 统一使用ConfigSyncManager"""
        try:
            from src.modules.data_import.config_sync_manager import ConfigSyncManager
            
            # 🔧 [P2-修复] 确保架构工厂和配置同步管理器可用
            if not hasattr(self, 'architecture_factory') or not self.architecture_factory:
                self.logger.error("🔧 [P2-修复] 架构工厂未初始化，无法加载字段映射")
                return
                
            self.config_sync_manager = self.architecture_factory.get_config_sync_manager()
            if not self.config_sync_manager:
                self.logger.error("🔧 [P2-修复] 配置同步管理器获取失败，无法加载字段映射")
                return
            
            # 获取所有表名
            table_names = self.config_sync_manager.get_all_table_names()
            
            # 使用统一接口加载每个表的映射
            self.field_mappings = {}
            for table_name in table_names:
                mapping = self.config_sync_manager.load_mapping(table_name)
                if mapping:
                    self.field_mappings[table_name] = mapping
            
            self.logger.info(f"已加载字段映射信息，共{len(self.field_mappings)}个表的映射")
            
        except Exception as e:
            self.logger.error(f"加载字段映射信息失败: {e}")
            self.field_mappings = {}

    def _apply_field_mapping_to_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """🔧 [P0-修复] 字段映射应用 - 使用新架构的ConfigSyncManager"""
        try:
            self.logger.debug(f"🎯 [新架构] 开始应用字段映射: {table_name}")
            
            # 🎯 [新架构] 确保ConfigSyncManager可用
            if not hasattr(self, 'config_sync_manager') or not self.config_sync_manager:
                if hasattr(self, 'architecture_factory') and self.architecture_factory:
                    self.config_sync_manager = self.architecture_factory.get_config_sync_manager()
                    if self.config_sync_manager:
                        self.logger.debug("通过架构工厂获取ConfigSyncManager实例")
                    else:
                        self.logger.debug("ConfigSyncManager不可用，跳过字段映射")
                        return df
                else:
                    self.logger.debug("架构工厂不可用，跳过字段映射")
                    return df
            
            # 使用ConfigSyncManager获取字段映射并手动应用
            try:
                field_mapping = self.config_sync_manager.get_field_mapping_for_table(table_name)
                if field_mapping:
                    # 创建DataFrame副本
                    mapped_df = df.copy()
                    # 应用字段映射：将数据库字段名映射为显示名
                    rename_dict = {}
                    for db_field, display_name in field_mapping.items():
                        if db_field in mapped_df.columns:
                            rename_dict[db_field] = display_name
                    
                    if rename_dict:
                        mapped_df = mapped_df.rename(columns=rename_dict)
                        self.logger.debug(f"字段映射应用成功: {table_name}, {len(rename_dict)}个字段")
                        return mapped_df
                    else:
                        self.logger.debug(f"无匹配字段需要映射: {table_name}")
                        return df
                else:
                    self.logger.debug(f"无字段映射配置: {table_name}")
                    return df
            except Exception as mapping_error:
                self.logger.debug(f"字段映射失败: {mapping_error}")
                return df
            
                
        except Exception as e:
            self.logger.error(f"字段映射处理失败: {e}")
            return df

    def _apply_data_formatting(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """🎯 [用户需求] 应用数据格式化 - 使用统一格式管理器处理数据"""
        try:
            self.logger.debug(f"🎯 [数据格式化] 开始应用数据格式化: {table_name}")
            
            # 🎯 [新架构] 检查是否有统一格式管理器
            if not hasattr(self, 'unified_format_manager') or not self.unified_format_manager:
                # 尝试从架构工厂获取统一格式管理器
                if hasattr(self, 'architecture_factory') and self.architecture_factory:
                    try:
                        self.unified_format_manager = self.architecture_factory.get_unified_format_manager()
                        if self.unified_format_manager:
                            self.logger.debug("通过架构工厂获取UnifiedFormatManager实例")
                        else:
                            self.logger.debug("统一格式管理器不可用，跳过数据格式化")
                            return df
                    except Exception as e:
                        self.logger.warning(f"获取统一格式管理器失败: {e}")
                        return df
                else:
                    self.logger.debug("架构工厂不可用，跳过数据格式化")
                    return df
            
            # 使用UnifiedFormatManager格式化数据
            try:
                formatted_df = self.unified_format_manager.format_data(
                    df, table_name, data_source='prototype_main_window'
                )
                
                if formatted_df is not None and not formatted_df.empty:
                    self.logger.debug(f"数据格式化成功: {table_name}, {len(formatted_df)}行")
                    return formatted_df
                else:
                    self.logger.warning(f"数据格式化结果为空: {table_name}")
                    return df
                    
            except Exception as format_error:
                self.logger.warning(f"数据格式化失败: {format_error}")
                return df
            
        except Exception as e:
            self.logger.error(f"数据格式化处理失败: {e}")
            return df

    def _apply_unified_field_processing(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """【防重复版本】统一的字段处理流程 - 确保单次会话中每个表只处理一次
        
        Args:
            df: 原始数据（直接从数据库获取）
            table_name: 表名
            
        Returns:
            处理后的数据（映射+过滤）
        """
        try:
            # 🔧 [优化缓存] 改进的字段处理缓存机制
            if not hasattr(self, '_field_processing_cache'):
                self._field_processing_cache = {}

            # 生成更稳定的缓存键：表名+列名集合（不依赖哈希）
            sorted_columns = tuple(sorted(df.columns))
            cache_key = f"{table_name}:{len(sorted_columns)}"

            # 检查缓存是否有效
            if cache_key in self._field_processing_cache:
                cached_data = self._field_processing_cache[cache_key]

                # 验证缓存的列名是否与当前数据匹配
                cached_columns = set(cached_data['original_columns'])
                current_columns = set(df.columns)

                # 如果列名完全匹配，使用缓存
                if cached_columns == current_columns:
                    self.logger.debug(f"🔧 [缓存命中] 使用缓存的字段处理结果: {table_name}")
                    try:
                        # 应用缓存的处理流程
                        df_mapped = df.rename(columns=cached_data['field_mapping'])
                        df_filtered = df_mapped[cached_data['final_columns']]
                        return df_filtered
                    except Exception as cache_error:
                        self.logger.warning(f"🔧 [缓存失效] 缓存应用失败，重新处理: {cache_error}")
                        # 清除无效缓存
                        del self._field_processing_cache[cache_key]
                else:
                    # 列名不匹配，清除旧缓存
                    self.logger.info(f"🔧 [缓存更新] 表结构变化，清除旧缓存: {table_name}")
                    del self._field_processing_cache[cache_key]
            
            print(f"开始统一字段处理: {table_name}, 原始列数: {len(df.columns)}")
            self.logger.info(f"开始统一字段处理: {table_name}, 原始列数: {len(df.columns)}")
            
            # 🎯 [用户需求] 步骤1: 数据格式化 - 确保所有数据按照字段类型进行格式化
            df_formatted = self._apply_data_formatting(df, table_name)
            print(f"数据格式化后列数: {len(df_formatted.columns)}")
            # 🔧 [调试] 记录格式化过程中的列变化
            if len(df_formatted.columns) != len(df.columns):
                dropped_in_formatting = set(df.columns) - set(df_formatted.columns)
                self.logger.info(f"🔧 [调试] 格式化时删除的列: {dropped_in_formatting}")
            
            # 步骤2: 应用字段映射（数据库列名 -> 显示列名）- 在过滤前进行，确保所有字段都能被映射
            df_with_mapping = self._apply_field_mapping_to_dataframe(df_formatted, table_name)
            print(f"字段映射后列数: {len(df_with_mapping.columns)}")
            
            # 步骤3: 过滤系统字段（在字段映射后进行，使用映射后的显示名称）
            df_filtered = self._apply_system_field_filtering(df_with_mapping, table_name)
            print(f"系统字段过滤后列数: {len(df_filtered.columns)}")
            # 🔧 [调试] 记录系统字段过滤过程中的列变化
            if len(df_filtered.columns) != len(df_with_mapping.columns):
                dropped_in_filtering = set(df_with_mapping.columns) - set(df_filtered.columns)
                self.logger.info(f"🔧 [调试] 系统字段过滤时删除的列: {dropped_in_filtering}")
            
            # 步骤4: 应用表级字段偏好过滤（可选的用户自定义过滤）
            try:
                df_final = self._apply_table_field_preference(df_filtered, table_name)
            except Exception as e:
                self.logger.warning(f"应用表格字段偏好失败，使用过滤后数据: {e}")
                df_final = df_filtered
            print(f"最终列数: {len(df_final.columns)}")
            
            # 🔧 [优化缓存] 改进的缓存存储机制
            # 1. 获取字段映射（原始列名 -> 显示列名）
            field_mapping = {}
            for orig_col in df.columns:
                if orig_col in df_final.columns:
                    # 列名没有变化
                    field_mapping[orig_col] = orig_col
                else:
                    # 查找映射后的列名（更精确的匹配）
                    for final_col in df_final.columns:
                        # 使用更严格的匹配规则
                        if (orig_col == final_col or
                            (isinstance(orig_col, str) and isinstance(final_col, str) and
                             (orig_col.lower() == final_col.lower() or
                              orig_col.replace('_', ' ') == final_col))):
                            field_mapping[orig_col] = final_col
                            break

            # 2. 存储更完整的缓存信息
            self._field_processing_cache[cache_key] = {
                'original_columns': list(df.columns),  # 原始列名
                'final_columns': list(df_final.columns),  # 最终列名
                'field_mapping': field_mapping,  # 字段映射关系
                'processed_at': time.time(),  # 处理时间
                'table_name': table_name,  # 表名
                'columns_count': len(df.columns)  # 列数
            }

            # 3. 清理过期缓存
            self._cleanup_field_processing_cache()
            
            self.logger.info(f"🔧 [字段处理] 统一字段处理完成并缓存: {len(df_final.columns)}个字段")
            return df_final

        except Exception as e:
            self.logger.error(f"🔧 [字段处理] 统一字段处理失败: {e}")
            print(f"🔧 [字段处理] 统一字段处理失败: {e}")
            return df

    def _cleanup_field_processing_cache(self):
        """清理过期的字段处理缓存"""
        try:
            if not hasattr(self, '_field_processing_cache'):
                return

            current_time = time.time()
            cache_ttl = 3600  # 缓存有效期1小时
            max_cache_size = 50  # 最大缓存条目数

            # 清理过期缓存
            expired_keys = []
            for key, cache_data in self._field_processing_cache.items():
                if current_time - cache_data.get('processed_at', 0) > cache_ttl:
                    expired_keys.append(key)

            for key in expired_keys:
                del self._field_processing_cache[key]

            if expired_keys:
                self.logger.info(f"🔧 [缓存清理] 清理了 {len(expired_keys)} 个过期缓存条目")

            # 如果缓存条目过多，清理最旧的
            if len(self._field_processing_cache) > max_cache_size:
                # 按处理时间排序，删除最旧的
                sorted_items = sorted(
                    self._field_processing_cache.items(),
                    key=lambda x: x[1].get('processed_at', 0)
                )

                # 保留最新的条目
                items_to_keep = sorted_items[-max_cache_size:]
                self._field_processing_cache = dict(items_to_keep)

                removed_count = len(sorted_items) - len(items_to_keep)
                if removed_count > 0:
                    self.logger.info(f"🔧 [缓存清理] 清理了 {removed_count} 个最旧的缓存条目")

        except Exception as e:
            self.logger.error(f"🔧 [缓存清理] 清理缓存失败: {e}")

    def clear_field_processing_cache(self, table_name: str = None):
        """🔧 [缓存管理] 清理字段处理缓存"""
        try:
            if not hasattr(self, '_field_processing_cache'):
                return

            if table_name:
                # 清理特定表的缓存
                keys_to_remove = [key for key in self._field_processing_cache.keys() if key.startswith(f"{table_name}:")]
                for key in keys_to_remove:
                    del self._field_processing_cache[key]
                self.logger.info(f"🔧 [缓存清理] 清理了表 {table_name} 的 {len(keys_to_remove)} 个缓存条目")
            else:
                # 清理所有缓存
                cache_count = len(self._field_processing_cache)
                self._field_processing_cache.clear()
                self.logger.info(f"🔧 [缓存清理] 清理了所有 {cache_count} 个字段处理缓存条目")

        except Exception as e:
            self.logger.error(f"🔧 [缓存清理] 清理缓存失败: {e}")

    def _apply_system_field_filtering(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """🔧 [P2-优化] 过滤系统字段，隐藏不需要显示的字段"""
        try:
            # 定义需要隐藏的系统字段（包括英文和中文名称）
            # 🎯 [用户需求] 根据用户要求，不隐藏年份和月份字段，需要格式化后显示
            SYSTEM_HIDDEN_FIELDS = [
                'created_at', 'updated_at', 'id', 'sequence_number',
                '创建时间', '更新时间', '自增主键', '序号'
                # 移除 'year', 'month' - 用户要求显示并格式化这些字段
            ]
            
            # 获取当前数据框的列名
            current_columns = list(df.columns)
            
            # 过滤出需要显示的列（排除系统字段）
            visible_columns = [col for col in current_columns if col not in SYSTEM_HIDDEN_FIELDS]
            
            if len(visible_columns) != len(current_columns):
                # 有字段被过滤
                hidden_fields = [col for col in current_columns if col in SYSTEM_HIDDEN_FIELDS]
                df_filtered = df[visible_columns].copy()
                
                self.logger.debug(f"系统字段过滤: 隐藏 {len(hidden_fields)} 个字段")
                
                return df_filtered
            else:
                return df
                
        except Exception as e:
            self.logger.error(f"系统字段过滤失败: {e}")
            return df

    def _apply_table_field_preference(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """在显示层应用表级字段偏好过滤

        Args:
            df: 原始数据（包含所有字段）
            table_name: 表名

        Returns:
            过滤后的数据（仅包含用户选择的字段）
        """
        try:
            # 防重复处理机制
            if hasattr(self, '_applying_field_preference') and self._applying_field_preference:
                return df

            self._applying_field_preference = True

            # 获取配置管理器
            config_sync = None
            if hasattr(self, "config_sync_manager") and self.config_sync_manager:
                config_sync = self.config_sync_manager
            elif hasattr(self, 'architecture_factory') and self.architecture_factory:
                config_sync = getattr(self.architecture_factory, 'config_sync_manager', None)

            if not config_sync:
                return df

            # 获取表级偏好字段
            preferred_fields = config_sync.get_table_field_preference(table_name)
            
            # 如果用户设置了偏好字段，则应用过滤
            if preferred_fields and len(preferred_fields) > 0:
                # 过滤出存在的偏好字段
                available_fields = [field for field in preferred_fields if field in df.columns]
                
                if available_fields:
                    self.logger.info(f"应用表 {table_name} 的字段偏好: {len(available_fields)}个字段 {available_fields}")
                    filtered_df = df[available_fields].copy()
                    return filtered_df
                else:
                    self.logger.warning(f"表 {table_name} 的偏好字段在数据中不存在，显示所有字段")
            else:
                self.logger.info(f"表 {table_name} 没有用户偏好设置，显示所有可见字段")
            
            return df

        except Exception as e:
            self.logger.error(f"应用表级字段偏好失败: {e}")
            return df
        finally:
            # 重置标志位
            if hasattr(self, '_applying_field_preference'):
                self._applying_field_preference = False

    def _on_navigation_changed(self, path: str, context: dict):
        """处理导航变化 - 支持分页模式自动检测"""
        self.logger.info(f"导航变化: {path}")

        # 取消导航面板中的自动选择重试，避免过期回调影响
        try:
            if hasattr(self, 'navigation_panel') and hasattr(self.navigation_panel, '_cancel_latest_path_retry'):
                self.navigation_panel._cancel_latest_path_retry()
        except Exception as cancel_err:
            self.logger.debug(f"取消自动选择重试失败(忽略): {cancel_err}")
        self.current_nav_path = path.split(' > ')
        self.current_selection = context

        # 【关键修复】导航切换时的完全重置机制
        self._complete_table_reset_on_navigation()
        
        # 清除字段处理缓存，确保新表使用最新的字段处理
        self.clear_field_processing_cache()
        self.logger.debug("已清除字段处理缓存，确保新表重新处理字段映射")

        # 更新状态栏显示当前导航路径
        if hasattr(self, 'footer_widget') and hasattr(self.footer_widget, 'update_navigation_path'):
            self.footer_widget.update_navigation_path(path)
            self.logger.debug(f"已更新状态栏导航路径: {path}")

        # 检查是否是自动选择的最新数据
        if context.get('auto_selected', False):
            self._auto_loading_latest_data = True
            self.logger.info("检测到自动选择的最新数据，将显示特殊提示")

        # 使用稳定映射：只要能从路径映射出表名，即视为选中叶子表
        table_name = self._generate_table_name_from_path(self.current_nav_path)
        if table_name:

            # 🔧 [P0-修复] 设置当前表名，统一由主窗口管理
            self.current_table_name = table_name
            self.logger.debug(f"🔧 [P0-修复] 主窗口设置current_table_name: {table_name}")

            # 🔧 [表名修复] 检查表名是否有效
            if not table_name:
                self.logger.warning(f"🔧 [表名修复] 无法从路径生成表名: {self.current_nav_path}")
                self.main_workspace.set_data(None)
                self._show_empty_table_with_prompt("请在左侧选择一个具体的数据表进行查看。")
                return

            # 🔧 [P0-修复] 统一的导航切换状态重置
            self._reset_navigation_components()
            
            # 同步到表格组件表名，供后续刷新使用
            try:
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    if hasattr(self.main_workspace.expandable_table, 'set_table_name'):
                        self.main_workspace.expandable_table.set_table_name(table_name)
            except Exception as e:
                self.logger.debug(f"同步表格组件表名失败(忽略): {e}")

            # 启用分页刷新按钮（已有表上下文）
            try:
                if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                    if hasattr(self.main_workspace.pagination_widget, 'refresh_btn'):
                        self.main_workspace.pagination_widget.refresh_btn.setEnabled(True)
            except Exception:
                pass

            # 先检查数据量，决定是否使用分页模式
            self._check_and_load_data_with_pagination(table_name)
        else:
            # 如果选择的不是最末级，则清空表格并提示
            self.main_workspace.set_data(None)
            self._show_empty_table_with_prompt("请在左侧选择一个具体的数据表进行查看。")
            # 禁用分页刷新按钮（无表上下文）
            try:
                if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                    if hasattr(self.main_workspace.pagination_widget, 'refresh_btn'):
                        self.main_workspace.pagination_widget.refresh_btn.setEnabled(False)
            except Exception:
                pass

    def _get_active_table_name(self) -> str:
        """获取当前激活表名（多源回退）"""
        try:
            # 1) 主窗体状态
            if getattr(self, 'current_table_name', None):
                return self.current_table_name
            # 2) 表格组件
            try:
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    if hasattr(self.main_workspace.expandable_table, 'table_name'):
                        tname = getattr(self.main_workspace.expandable_table, 'table_name', None)
                        if tname:
                            return tname
            except Exception:
                pass
            # 3) 导航路径推导
            nav_path = getattr(self, 'current_nav_path', None)
            if nav_path:
                tname2 = self._generate_table_name_from_path(nav_path)
                if tname2:
                    return tname2
        except Exception as e:
            self.logger.debug(f"_get_active_table_name 异常(忽略): {e}")
        return ""

    def _reset_navigation_components(self):
        """🔧 [P0-修复] 统一的导航切换状态重置，防止组件状态污染"""
        try:
            # 重置分页组件状态
            if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                self.main_workspace.pagination_widget.reset()
                self.logger.debug("🔧 [P0-修复] 分页组件状态已重置")
            
            # 重置表格组件的分页状态
            if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                self.main_workspace.expandable_table.pagination_state = None
                # 🔧 [P0-修复] 清理表格的排序状态，防止表头重影
                if hasattr(self.main_workspace.expandable_table, 'clear_sort_indicators'):
                    self.main_workspace.expandable_table.clear_sort_indicators()
                self.logger.debug("🔧 [P0-修复] 表格组件分页和排序状态已重置")
                
            # 清理分页缓存
            if hasattr(self, 'pagination_cache') and self.pagination_cache and self.current_table_name:
                self.pagination_cache.clear_cache(self.current_table_name)
                self.logger.debug(f"🔧 [P0-修复] 已清理表 {self.current_table_name} 的分页缓存")
                
            # 🔧 [P0-修复] 强制清理表头缓存，防止重影
            self._conditional_header_cleanup()
            
        except Exception as e:
            self.logger.error(f"🔧 [P0-修复] 导航组件重置失败: {e}")

    def _check_and_load_data_with_pagination(self, table_name: str):
        """检查数据量并决定是否使用分页模式加载数据"""
        try:
            # 设置当前表名
            self.current_table_name = table_name

            # 使用新架构加载数据
            if self.table_data_service:
                self.logger.info(f"🆕 使用新架构加载数据: {table_name}（通过事件系统）")
                # 新架构依赖事件系统，通过table_data_service.load_table_data触发事件
                # 避免直接调用_load_data_with_new_architecture导致重复加载
                response = self.table_data_service.load_table_data(table_name, 1)
                if response is not None and response.success:
                    self.logger.info(f"🆕 新架构数据加载请求已发送，等待事件系统处理")
                else:
                    # 🔧 [P0-修复] 数据库表不存在时的友好处理
                    if response and "不存在" in str(response.error_code):
                        self.logger.info(f"🔧 [数据流追踪] 数据表 {table_name} 尚未创建，显示空表格等待数据导入")
                        self._show_empty_table_with_prompt(f"数据表 {table_name} 尚未有数据，请导入Excel文件")
                    else:
                        self.logger.error(f"🆕 新架构数据加载请求失败: {response}")
                        self._show_empty_table_with_prompt(f"数据加载失败，请检查数据库连接")
                return

            # 🔧 [P1-移除旧架构] 移除降级逻辑
            self.logger.error(f"🔧 [P1-移除旧架构] 新架构数据加载失败，不再支持降级")
            raise RuntimeError(f"新架构数据加载失败: {table_name}")
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {e}")
            self._show_empty_table_with_prompt(f"数据加载失败: {str(e)}")
    
    def _load_data_with_new_architecture(self, table_name: str, page: int = 1):
        """🆕 新架构数据加载方法，包含排序和分页支持"""
        try:
            self.logger.info(f"🆕 新架构数据加载开始: {table_name}, 页码: {page}")
            
            # 获取总记录数 - 新架构方法
            total_records = self.table_data_service.get_table_record_count(table_name)
            
            # 🔧 [分页修复] 确保分页组件设置正确的总记录数
            if total_records > 0:
                if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                    pagination_widget = self.main_workspace.pagination_widget
                    current_total = pagination_widget.state.total_records
                    if current_total != total_records:
                        pagination_widget.set_total_records(total_records)
                        self.logger.info(f"🔧 [分页修复] 更新分页组件总记录数: {current_total} -> {total_records}")
                    
                    # 确保当前页面在有效范围内
                    max_page = pagination_widget.state.total_pages
                    if page > max_page and max_page > 0:
                        page = max_page
                        self.logger.info(f"🔧 [分页修复] 调整页码到有效范围: {page}")
            
            if total_records == 0:
                self.logger.info(f"🆕 表 {table_name} 为空，显示空状态")
                self.main_workspace.set_data([])
                return
            
            # 根据数据量选择加载模式
            if total_records <= 50:
                self.logger.info(f"🆕 新架构非分页模式: {total_records}条记录")
                
                # 直接加载所有数据
                from src.core.unified_data_request_manager import DataRequest, RequestType
                request = DataRequest(
                    table_name=table_name,
                    page=1,
                    page_size=total_records,
                    request_type=RequestType.INITIAL_LOAD
                )
                response = self.unified_data_request_manager.request_table_data(request)
                
                if response is not None and response.success:
                    data = response.data if response.data is not None else []
                    self.logger.info(f"🆕 新架构数据加载成功: {len(data)}行")
                    
                    # 设置表格数据
                    self.main_workspace.set_data(data)
                    
                    # 🔧 [CRITICAL修复] 非分页模式也要正确设置分页组件状态，但不在排序时重置页码
                    if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                        pagination_widget = self.main_workspace.pagination_widget
                        pagination_widget.set_total_records(total_records)
                        # 🔧 [CRITICAL修复] 只在非排序操作时重置页码
                        current_context = getattr(self, '_current_operation_context', {})
                        if current_context.get('operation_type') != 'sort_change':
                            pagination_widget.set_current_page(1)
                            self.logger.info(f"🔧 [CRITICAL修复] 非分页模式，非排序操作，重置页码: 总记录数={total_records}")
                        else:
                            self.logger.info(f"🔧 [CRITICAL修复] 非分页模式，排序操作，保持页码: 总记录数={total_records}")
                    
                else:
                    self.logger.error(f"🆕 新架构数据加载失败: {response}")
                    self.main_workspace.set_data([])
            else:
                self.logger.info(f"🆕 新架构分页模式: {total_records}条记录")
                
                # 分页加载
                response = self.table_data_service.load_table_data(table_name, page)
                
                if response is not None and response.success:
                    data = response.data if response.data is not None else []
                    
                    self.logger.info(f"🆕 新架构数据加载成功: {response.loaded_fields}字段, {len(data)}行, 耗时{response.processing_time_ms}ms")
                    
                    # 设置表格数据
                    self.main_workspace.set_data(data)
                    
                    # 🔧 [分页修复] 分页模式确保分页组件状态正确
                    if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                        pagination_widget = self.main_workspace.pagination_widget
                        
                        # 设置总记录数（如果还没设置）
                        if pagination_widget.state.total_records != total_records:
                            pagination_widget.set_total_records(total_records)
                        
                        # 设置当前页码
                        if pagination_widget.state.current_page != page:
                            pagination_widget.set_current_page(page)
                        
                        # 强制更新UI状态，确保按钮可点击性正确
                        pagination_widget._update_ui_state()
                        
                        self.logger.info(f"🔧 [分页修复] 分页模式设置完成: 当前页={page}, 总记录数={total_records}, 总页数={pagination_widget.state.total_pages}")
                        
                        # 验证按钮状态
                        can_next = pagination_widget.state.current_page < pagination_widget.state.total_pages
                        can_prev = pagination_widget.state.current_page > 1
                        self.logger.info(f"🔧 [分页修复] 按钮状态验证: 可下一页={can_next}, 可上一页={can_prev}")
                        
                else:
                    self.logger.error(f"🆕 新架构分页数据加载失败: {response}")
                    self.main_workspace.set_data([])
            
        except Exception as e:
            self.logger.error(f"🆕 新架构数据加载异常: {e}", exc_info=True)
            self.main_workspace.set_data([])
    
    # 🔧 [P1-移除旧架构] 删除 _legacy_check_and_load_data_with_pagination 方法

    def _load_data_with_pagination(self, table_name: str, page: int = 1, page_size: int = 50):
        """使用分页模式加载数据（支持缓存）"""
        self.logger.info(f"使用分页模式加载 {table_name}，第{page}页，每页{page_size}条")
        
        # 显示分页组件
        if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
            self.main_workspace.pagination_widget.setVisible(True)
        
        # 获取当前排序状态用于缓存键
        sort_state_key = ""
        if hasattr(self, 'sort_state_manager') and self.sort_state_manager:
            try:
                sort_state = self.sort_state_manager.restore_sort_state(table_name)
                if sort_state and hasattr(sort_state, 'sort_columns') and sort_state.sort_columns:
                    # 生成排序状态的简短标识
                    sort_parts = []
                    for col in sort_state.sort_columns:
                        if isinstance(col, dict):
                            col_name = col.get('column_name', '')
                            order = col.get('order', 'asc')
                            sort_parts.append(f"{col_name}:{order}")
                    sort_state_key = "|".join(sort_parts)
            except Exception as e:
                self.logger.warning(f"获取排序状态用于缓存失败: {e}")

        # 🚀 [性能优化] 使用优化的缓存系统获取数据
        if hasattr(self, 'performance_manager'):
            # 构建排序列信息
            sort_columns = []
            if sort_state_key:
                # 解析排序状态键
                for sort_part in sort_state_key.split("|"):
                    if ":" in sort_part:
                        col_name, order = sort_part.split(":", 1)
                        sort_columns.append({'column_name': col_name, 'order': order})

            # 🔧 [P4-修复] 启用优化的缓存系统，修复键冲突问题
            try:
                # 🔧 [P4-修复] 使用健壮的路径处理
                import sys
                from pathlib import Path
                
                # 获取项目根目录下的temp目录
                current_file_path = Path(__file__)
                project_root = current_file_path.parent.parent.parent
                temp_dir = project_root / 'temp'
                
                # 确保temp目录存在且在Python路径中
                if temp_dir.exists() and str(temp_dir) not in sys.path:
                    sys.path.insert(0, str(temp_dir))
                    self.logger.info(f"[数据流追踪] 已添加缓存模块路径: {temp_dir}")
                
                # 可选优化：若存在本地优化加载器则使用，否则忽略（动态导入，避免静态导入告警）
                CacheOptimizedDataLoader = None
                try:
                    import importlib
                    mod = importlib.import_module('cache_optimization_fix')
                    CacheOptimizedDataLoader = getattr(mod, 'CacheOptimizedDataLoader', None)
                except Exception:
                    CacheOptimizedDataLoader = None
                
                # 创建或获取缓存加载器实例
                if CacheOptimizedDataLoader and not hasattr(self, '_optimized_cache_loader'):
                    # 传入数据库管理器
                    db_manager = getattr(self, 'db_manager', None)
                    self._optimized_cache_loader = CacheOptimizedDataLoader(db_manager=db_manager)
                    self.logger.info("🚀 [缓存修复] 优化缓存加载器已初始化，数据库管理器已连接")
                
                # 使用优化缓存加载数据
                cached_data, cache_metadata, from_cache = self._optimized_cache_loader.load_table_data_with_cache(
                    table_name, page, page_size, sort_columns
                )
                
                if from_cache and cached_data is not None:
                    self.logger.info(f"🚀 [优化缓存命中] {table_name} 第{page}页，数据行数: {len(cached_data)}")
                    
                    # 获取总记录数
                    total_records = cache_metadata.get('total_records', len(cached_data) * page)
                    
                    # 使用统一字段处理
                    df_with_mapping = self._apply_unified_field_processing(cached_data, table_name)
                    
                    # 🔧 [CRITICAL修复] 确保操作上下文在缓存命中时也正确传递
                    # 在set_data前强制设置分页操作上下文
                    self._current_operation_context = {
                        'operation_type': 'page_change',
                        'target_page': page,
                        'from_cache': True
                    }
                    
                    # 设置表格数据
                    self.set_data(df_with_mapping, total_records=total_records, current_page=page)
                    self.logger.info(f"🚀 [缓存成功] 已从优化缓存加载第{page}页数据，操作上下文已设置")
                    return
                        
            except ImportError as ie:
                self.logger.info(f"[数据流追踪] 优化缓存模块未找到: {ie}，使用标准缓存机制")
            except Exception as e:
                self.logger.warning(f"[数据流追踪] 优化缓存系统初始化异常: {e}，回退到标准缓存")
                # 发生异常时继续使用原有逻辑

        # 尝试从旧缓存获取数据（包含排序状态）
        cached_data = self.pagination_cache.get_page_data(table_name, page, page_size, sort_state_key)
        if cached_data is not None:
            self.logger.info(f"缓存命中: {table_name} 第{page}页")

            # 从缓存获取表信息
            table_info = self.pagination_cache.get_table_info(table_name)
            total_records = table_info.total_records if table_info else len(cached_data) * page

            # 使用统一字段处理
            df_with_mapping = self._apply_unified_field_processing(cached_data, table_name)
            
            # 构造结果
            result = {
                'success': True,
                'data': df_with_mapping,
                'total_records': total_records,
                'current_page': page,
                'page_size': page_size,
                'table_name': table_name,
                'from_cache': True
            }
            
            # 直接处理结果
            self._on_pagination_data_loaded(result)
            
            # 异步预加载相邻页面
            if table_info:
                total_pages = (total_records + page_size - 1) // page_size
                self.pagination_cache.preload_adjacent_pages(
                    table_name, page, page_size, total_pages, 
                    self.dynamic_table_manager.get_dataframe_paginated
                )
            
            return
        
        # 缓存未命中，从数据库加载
        self.logger.info(f"缓存未命中，从数据库加载: {table_name} 第{page}页")
        
        # 创建分页工作线程，传递排序状态管理器
        worker = PaginationWorker(
            table_manager=self.dynamic_table_manager,
            table_name=table_name,
            page=page,
            page_size=page_size,
            apply_mapping_func=self._apply_unified_field_processing,
            sort_state_manager=getattr(self, 'sort_state_manager', None)
        )
        
        # 连接信号
        worker.signals.result.connect(self._on_pagination_data_loaded)
        
        # 启动工作线程
        self.thread_pool.start(worker)

    def _on_pagination_data_loaded(self, result: dict):
        """处理分页数据加载完成（支持缓存）"""
        try:
            if result.get('success', False):
                # 加载成功
                df = result['data']
                total_records = result['total_records']
                current_page = result['current_page']
                page_size = result['page_size']
                table_name = result['table_name']
                from_cache = result.get('from_cache', False)
                
                cache_msg = "（缓存）" if from_cache else "（数据库）"
                self.logger.info(f"分页数据加载成功{cache_msg}: {len(df)}条数据，第{current_page}页，总计{total_records}条")
                
                # 如果数据来自数据库，需要缓存它（包含排序状态）
                if not from_cache:
                    # 获取排序状态用于缓存
                    sort_state_key = ""
                    if hasattr(self, 'sort_state_manager') and self.sort_state_manager:
                        try:
                            sort_state = self.sort_state_manager.restore_sort_state(table_name)
                            if sort_state and hasattr(sort_state, 'sort_columns') and sort_state.sort_columns:
                                sort_parts = []
                                for col in sort_state.sort_columns:
                                    if isinstance(col, dict):
                                        col_name = col.get('column_name', '')
                                        order = col.get('order', 'asc')
                                        sort_parts.append(f"{col_name}:{order}")
                                sort_state_key = "|".join(sort_parts)
                        except Exception as e:
                            self.logger.warning(f"获取排序状态用于缓存失败: {e}")

                    # 🚀 [性能优化] 缓存到性能管理器
                    if hasattr(self, 'performance_manager'):
                        # 构建排序列信息
                        sort_columns = []
                        if sort_state_key:
                            for sort_part in sort_state_key.split("|"):
                                if ":" in sort_part:
                                    col_name, order = sort_part.split(":", 1)
                                    sort_columns.append((col_name, order))

                        self.performance_manager.cache_data(
                            table_name, current_page, page_size, df,
                            sort_columns if sort_columns else None
                        )
                        self.logger.info(f"🚀 [性能缓存] 数据已缓存: {table_name} 第{current_page}页")

                    # 缓存页面数据（包含排序状态）
                    self.pagination_cache.put_page_data(table_name, current_page, page_size, df, sort_state_key)

                    # 缓存或更新表信息
                    columns = df.columns.tolist()
                    self.pagination_cache.put_table_info(table_name, total_records, columns)
                    
                    # 异步预加载相邻页面
                    total_pages = (total_records + page_size - 1) // page_size
                    self.pagination_cache.preload_adjacent_pages(
                        table_name, current_page, page_size, total_pages,
                        self.dynamic_table_manager.get_dataframe_paginated
                    )
                
                # 数据完整性验证
                if df.empty:
                    self.logger.warning(f"第{current_page}页数据为空")
                else:
                    # 检查是否所有行都为空
                    empty_rows = df.isnull().all(axis=1).sum()
                    if empty_rows == len(df):
                        self.logger.error(f"第{current_page}页所有数据行都为空，可能存在数据导入问题")
                    elif empty_rows > 0:
                        self.logger.warning(f"第{current_page}页有{empty_rows}行为空数据")
                
                # 🧹 [代码清理] 数据已通过统一格式化管理器处理，移除冗余格式化调用
                # 🔧 [重构完成] 异步分页数据已由统一格式化管理器处理，无需额外调用
                self.logger.info(f"🧹 [异步分页] 使用重构后的统一格式化结果: {len(df)}行, {len(df.columns)}列")
                
                # 🚀 [性能优化] 记录数据加载时间
                load_time_ms = result.get('load_time_ms', 0)
                if hasattr(self, 'performance_manager') and load_time_ms > 0:
                    self.performance_manager.record_data_load_time(load_time_ms)

                # 设置数据到表格（使用分页模式）
                self.main_workspace.set_data(df, preserve_headers=True, table_name=table_name, current_table_name=table_name)

                # 设置分页状态到表格组件，确保行号正确显示
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    pagination_state = {
                        'current_page': current_page,
                        'page_size': page_size,
                        'total_records': total_records,
                        'start_record': (current_page - 1) * page_size + 1,
                        'end_record': min(current_page * page_size, total_records)
                    }
                    self.logger.info(f"🔍 [调试-分页] 即将调用set_pagination_state: 第{pagination_state.get('current_page')}页, 记录{pagination_state.get('start_record')}-{pagination_state.get('end_record')}")
                    self.main_workspace.expandable_table.set_pagination_state(pagination_state)
                    self.logger.info("🔍 [调试-分页] set_pagination_state调用完成")
                
                # 🔧 [P0-新1修复] 更新分页组件状态，添加验证和强制刷新
                if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                    pagination_widget = self.main_workspace.pagination_widget
                    
                    # 设置分页状态
                    pagination_widget.set_total_records(total_records)
                    pagination_widget.set_current_page(current_page)
                    pagination_widget.set_page_size(page_size)
                    
                    # 🔧 [P0-新1修复] 验证状态正确性并强制刷新UI
                    state = pagination_widget.get_current_state()
                    expected_total_pages = max(1, (total_records + page_size - 1) // page_size)
                    
                    if state.total_pages != expected_total_pages:
                        self.logger.warning(f"🔧 [P0-新1修复] 分页状态不一致: 期望{expected_total_pages}页，实际{state.total_pages}页")
                        # 强制重新计算
                        state.total_records = total_records
                        state.calculate_derived_fields()
                        pagination_widget._update_ui_state()
                    
                    self.logger.info(f"🔧 [P0-新1修复] 分页状态验证: 当前第{state.current_page}页，共{state.total_pages}页，总记录{state.total_records}条")
                
                # 更新状态信息（包含缓存状态）
                cache_stats = self.pagination_cache.get_cache_stats()
                status_msg = f"已加载 {table_name}，第{current_page}页，共{total_records}条记录{cache_msg}"
                if cache_stats:
                    status_msg += f" [缓存命中率: {cache_stats.get('hit_rate', 0)}%]"

                # 🚀 [性能优化] 添加性能统计信息
                if hasattr(self, 'performance_manager'):
                    perf_stats = self.performance_manager.get_comprehensive_stats()
                    data_cache_stats = perf_stats.get('data_cache', {})
                    if data_cache_stats.get('hit_rate'):
                        status_msg += f" [性能缓存: {data_cache_stats['hit_rate']}]"

                self._update_status_label(status_msg, "success")

                # 如果这是自动加载的最新数据，添加特殊提示
                if hasattr(self, '_auto_loading_latest_data') and self._auto_loading_latest_data:
                    self._update_status_label(f"✨ 已自动加载最新数据: {status_msg}", "success")
                    self._auto_loading_latest_data = False  # 重置标志

            else:
                # 加载失败 - 增强错误处理
                error = result.get('error', '未知错误')
                table_name = result.get('table_name', '未知表')
                error_type = result.get('error_type', 'unknown')
                page = result.get('page', '未知')
                
                self.logger.error(f"分页数据加载失败: {table_name} 第{page}页 - {error} (类型: {error_type})")

                # 清空显示并提供详细错误信息
                import pandas as pd
                empty_df = pd.DataFrame()
                self.main_workspace.set_data(empty_df)
                
                # 根据错误类型提供不同的提示
                if error_type == 'pagination_load_error':
                    error_prompt = f"数据加载失败: {error}\\n\\n建议：\\n1. 检查数据库连接\\n2. 验证表结构\\n3. 重新导入数据"
                else:
                    error_prompt = f"加载 {table_name} 失败: {error}"
                    
                self._show_empty_table_with_prompt(error_prompt)
                
        except Exception as e:
            self.logger.error(f"处理分页数据结果失败: {e}", exc_info=True)
            self.main_workspace.set_data(None)
            self._show_empty_table_with_prompt(f"处理分页数据失败: {e}")
        finally:
            # 🆕 [新架构] 完成渐进式状态迁移
            self.complete_navigation_transition()
            self.logger.info("🆕 [新架构] 分页数据加载完成，已完成渐进式状态迁移")

    def _load_database_data_with_mapping(self, table_name: str) -> Optional[pd.DataFrame]:
        """从数据库加载数据并应用字段映射（修复：统一字段加载策略）"""
        try:
            # 检查表是否存在
            if not self.dynamic_table_manager.table_exists(table_name):
                self.logger.info(f"数据表 {table_name} 尚未创建，请导入数据后重试")
                return None

            # 【修复】统一字段加载策略：始终加载所有字段
            self.logger.info("统一加载所有字段（修复字段不一致问题）")
            df = self.dynamic_table_manager.get_dataframe_from_table(table_name)

            if df is None or df.empty:
                self.logger.info(f"数据表 {table_name} 为空，请导入数据后重试")
                return None

            self.logger.info(f"成功从数据库加载 {len(df)} 行数据，{len(df.columns)} 个字段")

            # 应用字段映射，确保显示中文表头
            df_with_mapping = self._apply_field_mapping_to_dataframe(df, table_name)

            self.logger.info(f"应用字段映射后的表头: {list(df_with_mapping.columns)}")

            return df_with_mapping

        except Exception as e:
            self.logger.error(f"加载数据时出错: {e}", exc_info=True)
            return None

    def _load_database_data(self, table_name: str) -> Optional[pd.DataFrame]:
        """从数据库加载指定表的数据。"""
        try:
            # 检查表是否存在
            if not self.dynamic_table_manager.table_exists(table_name):
                self.logger.info(f"数据表 {table_name} 尚未创建，请导入数据后重试")
                return None

            df = self.dynamic_table_manager.get_dataframe_from_table(table_name)
            
            if df is None or df.empty:
                self.logger.info(f"数据表 {table_name} 为空，请导入数据后重试")
                return None
                
            self.logger.info(f"成功从数据库加载 {len(df)} 行数据。")
            return df
        except Exception as e:
            self.logger.error(f"加载数据时出错: {e}", exc_info=True)
            # 在主线程中显示错误消息
            # self.main_workspace.set_data(None) # 避免循环调用
            return None

    def _save_to_database(self, df: pd.DataFrame, category_node_path: list[str]):
        """将DataFrame保存到由导航路径决定的数据库表中。"""
        try:
            table_name = self._generate_table_name_from_path(category_node_path)
            self.logger.info(f"准备将 {len(df)} 条数据保存到表: {table_name}")
            worker = Worker(self._execute_save, df, table_name)
            worker.signals.result.connect(self._on_save_finished)
            self.thread_pool.start(worker)
        except Exception as e:
            self.logger.error(f"准备保存数据时出错: {e}", exc_info=True)
            self._show_error_message("数据库错误", f"保存数据失败: {e}")

    def _execute_save(self, df: pd.DataFrame, table_name: str) -> dict:
        """在工作线程中执行实际的数据库保存操作，并返回结果。"""
        try:
            success, message = self.dynamic_table_manager.save_dataframe_to_table(df, table_name)
            if success:
                return {"success": True, "table_name": table_name, "message": message}
            else:
                return {"success": False, "table_name": table_name, "error": message}
        except Exception as e:
            # This will catch cases where save_dataframe_to_table might not return a tuple
            # For example if it returns a single boolean False
            self.logger.error(f"异步保存数据到表 {table_name} 失败: {e}", exc_info=True)
            error_message = str(e) if str(e) else "发生未知异常"
            # Attempt to unpack if possible, otherwise treat as a single value
            try:
                success, msg = e
                error_message = msg
            except (TypeError, ValueError):
                pass
            return {"success": False, "table_name": table_name, "error": error_message}

    @pyqtSlot(dict)
    def _on_save_finished(self, result: dict):
        """处理数据库保存完成后的结果。"""
        success = result.get("success", False)
        table_name = result.get("table_name", "未知表")
        if success:
            self.logger.info(f"数据成功保存到表: {table_name}")
            self._show_info_message("保存成功", f"数据已成功保存到数据库表 '{table_name}'。")
        else:
            error = result.get("error", "未知错误")
            self.logger.error(f"保存数据到表 {table_name} 失败: {error}")
            self._show_error_message("保存失败", f"无法将数据保存到数据库表 '{table_name}'。\n错误: {error}")

    def _generate_table_name_from_path(self, path_list: list[str]) -> str:
        """根据导航路径列表生成数据库表名"""
        # 检查路径是否为空
        if not path_list:
            self.logger.warning("🔧 [表名生成] 路径列表为空")
            return ""

        # 检查第一级是否是"工资表"
        if len(path_list) > 0 and path_list[0] != "工资表":
            self.logger.warning(f"🔧 [表名生成] 路径第一级不是'工资表': {path_list[0]}")
            return ""

        # 处理不完整路径的情况
        if len(path_list) < 4:
            self.logger.info(f"🔧 [表名生成] 导航路径不完整({len(path_list)}层): {path_list}")

            # 如果有年份和月份，可以尝试查找匹配的表
            if len(path_list) >= 3:
                # 提取年份和月份
                year = re.sub(r'\D', '', path_list[1])
                month = re.sub(r'\D', '', path_list[2]).zfill(2)

                # 尝试查找匹配的表
                if hasattr(self, 'dynamic_table_manager') and self.dynamic_table_manager:
                    try:
                        # 获取所有工资数据表
                        all_tables = self.dynamic_table_manager.get_table_list(table_type='salary_data')
                        if all_tables:
                            # 筛选匹配年月的表
                            matching_tables = []
                            for table in all_tables:
                                table_name = table.get('name', '')
                                if f"salary_data_{year}_{month}" in table_name:
                                    matching_tables.append(table_name)

                            # 如果有匹配的表，返回第一个
                            if matching_tables:
                                # 优先选择"全部在职人员"表
                                for table in matching_tables:
                                    if "active_employees" in table:
                                        self.logger.info(f"🔧 [表名生成] 找到匹配年月的全部在职人员表: {table}")
                                        return table

                                # 如果没有全部在职人员表，返回第一个匹配的表
                                self.logger.info(f"🔧 [表名生成] 找到匹配年月的表: {matching_tables[0]}")
                                return matching_tables[0]
                    except Exception as e:
                        self.logger.warning(f"🔧 [表名生成] 查找匹配表失败: {e}")

            # 如果没有找到匹配的表，尝试查找最新的表
            if hasattr(self, 'dynamic_table_manager') and self.dynamic_table_manager:
                try:
                    # 获取所有工资数据表
                    all_tables = self.dynamic_table_manager.get_table_list(table_type='salary_data')
                    if all_tables:
                        # 按表名排序，选择最新的表
                        table_names = [table.get('name', '') for table in all_tables if table.get('name')]
                        if table_names:
                            latest_table = sorted(table_names)[-1]  # 选择最新的表
                            self.logger.info(f"🔧 [表名生成] 路径不完整时自动选择最新表: {latest_table}")
                            return latest_table
                except Exception as e:
                    self.logger.warning(f"🔧 [表名生成] 查找最新表失败: {e}")

            return ""

        # 处理完整路径的情况
        base_name = "salary_data"
        year = re.sub(r'\D', '', path_list[1])
        month = re.sub(r'\D', '', path_list[2]).zfill(2)

        category = path_list[3]
        category_map = {
            "全部在职人员": "active_employees",
            "A岗职工": "a_grade_employees",
            "B岗职工": "b_grade_employees",
            "退休人员": "pension_employees",
            "离休人员": "retired_employees",
            "其他人员": "other_employees"
        }
        category_en = category_map.get(category, "unknown_category")

        table_name = f"{base_name}_{year}_{month}_{category_en}"
        self.logger.info(f"🔧 [表名生成] 从完整路径生成表名: {path_list} -> {table_name}")
        return table_name
        
    def _get_suggested_target_path(self) -> str:
        """根据当前状态智能推断建议的目标路径"""
        try:
            if hasattr(self, 'current_nav_path') and self.current_nav_path:
                return " > ".join(self.current_nav_path)
            else:
                # 如果没有当前路径，生成一个默认路径
                from datetime import datetime
                now = datetime.now()
                return f"工资表 > {now.year}年 > {now.month:02d}月 > 全部在职人员"
        except Exception as e:
            self.logger.warning(f"无法生成建议路径: {e}")
            return ""

    def _initialize_data(self):
        """
        初始化数据显示 - 专业版本
        优先尝试加载在职人员工资表数据
        """
        try:
            self.logger.info("开始初始化数据...")

            # 优先尝试加载真实的在职人员数据
            if self._try_load_active_employee_data():
                self.logger.info("成功加载在职人员数据作为默认显示")
                return
                
            # 没有数据时，显示23个标准表头的空表格
            self._show_empty_table_with_prompt("系统已准备好标准在职人员工资表格式，请导入Excel数据文件。")

        except Exception as e:
            self.logger.error(f"初始化数据失败: {e}")
            # 即使初始化失败，也要显示标准表头
            self._show_empty_table_with_prompt("数据初始化异常，已显示标准工资表格式，请检查数据库连接。")

    def _get_standard_active_employee_headers(self) -> List[str]:
        """
        获取标准的全部在职人员工资表表头（22个字段，排除序号）
        
        重要：此方法必须保证100%可靠，不能依赖任何外部组件
        无论系统处于任何状态，都必须返回正确的22个表头
        
        🔧 [数据流追踪] 2025-07-27 修复表头数量验证错误，实际为22个字段
        """
        # 硬编码的22个标准表头 - 保证专业性和可靠性
        STANDARD_HEADERS = [
            "工号",
            "姓名", 
            "部门名称",
            "人员类别代码",  
            "人员类别",
            "2025年岗位工资",
            "2025年薪级工资", 
            "津贴",
            "结余津贴",
            "2025年基础性绩效",
            "卫生费",
            "交通补贴", 
            "物业补贴",
            "住房补贴",
            "车补",
            "通讯补贴",
            "2025年奖励性绩效预发",
            "补发",
            "借支", 
            "应发工资",
            "2025公积金",
            "代扣代存养老保险"
        ]
        
        # 🔧 [数据流追踪] 验证表头数量（必须是22个）
        expected_count = 22
        if len(STANDARD_HEADERS) != expected_count:
            # 这是开发时的安全检查，生产环境不应该出现
            self.logger.error(f"🔧 [P0-修复] 标准表头数量错误: {len(STANDARD_HEADERS)}，应为{expected_count}个")
            self.logger.info(f"🔧 [数据流追踪] 当前标准表头: {STANDARD_HEADERS}")
        else:
            self.logger.debug(f"🔧 [数据流追踪] 标准表头验证通过: {expected_count}个字段")
        
        return STANDARD_HEADERS.copy()  # 返回副本，防止意外修改

    def _get_table_headers_by_type(self, table_name: str) -> List[str]:
        """
        🔧 [P1-修复] 根据表名获取对应的表头
        
        Args:
            table_name: 表名，如 'salary_data_2025_05_active_employees'
            
        Returns:
            对应表类型的标准表头列表
        """
        try:
            self.logger.debug(f"🔧 [数据流追踪] 获取表头，表名: {table_name}")
            
            # 表名解析，提取表类型
            if "active_employees" in table_name or "在职人员" in table_name:
                # 在职人员表头（22个字段）
                headers = self._get_standard_active_employee_headers()
                self.logger.info(f"🔧 [数据流追踪] 使用在职人员表头: {len(headers)}个字段")
                return headers
                
            elif "retired_employees" in table_name or "离休人员" in table_name:
                # 离休人员表头（从专用模板获取）
                headers = [
                    "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴",
                    "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴",
                    "补发", "合计", "借支", "备注"
                ]
                self.logger.info(f"🔧 [数据流追踪] 使用离休人员表头: {len(headers)}个字段")
                return headers
                
            elif "pension_employees" in table_name or "退休人员" in table_name:
                # 退休人员表头
                headers = [
                    "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴",
                    "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴",
                    "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整",
                    "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整",
                    "补发", "借支", "应发工资", "公积", "保险扣款", "备注"
                ]
                self.logger.info(f"🔧 [数据流追踪] 使用退休人员表头: {len(headers)}个字段")
                return headers
                
            elif "a_grade_employees" in table_name or "A岗职工" in table_name:
                # A岗职工表头 - 🔧 [修复] 调整"人员类别代码"位置到"部门名称"之后
                headers = [
                    "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资",
                    "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费",
                    "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支",
                    "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"
                ]
                self.logger.info(f"🔧 [数据流追踪] 使用A岗职工表头: {len(headers)}个字段")
                return headers
                
            else:
                # 默认使用在职人员表头
                self.logger.warning(f"🔧 [数据流追踪] 未识别的表类型 {table_name}，使用默认在职人员表头")
                return self._get_standard_active_employee_headers()
                
        except Exception as e:
            self.logger.error(f"🔧 [P1-修复] 获取表头失败: {e}")
            # 异常时返回在职人员表头作为后备
            return self._get_standard_active_employee_headers()

    def _show_empty_table_with_prompt(self, prompt: str = None):
        """
        显示带有标准表头的空表格
        
        🔧 [P1-修复] 根据当前表类型显示对应的表头，而非固定显示在职人员表头
        """
        # 🔧 [幂等保护] 短窗去重，避免导航切换时重复空表渲染/分页重置
        try:
            from time import time
            if not hasattr(self, '_empty_show_last_ts'):
                self._empty_show_last_ts = 0.0
            if not hasattr(self, '_empty_show_token'):
                self._empty_show_token = 0
            cooldown_sec = 0.3
            now_ts = time()
            if (now_ts - self._empty_show_last_ts) < cooldown_sec:
                # 在冷却窗口内忽略重复空表显示
                self.logger.debug("🔧 [幂等保护] 短时间内重复的空表显示请求已忽略")
                return
            self._empty_show_last_ts = now_ts
            self._empty_show_token += 1
        except Exception:
            pass
        # 🔧 [P0-CRITICAL] 样式保护机制 - 如果是由于线程安全错误导致的调用，不显示空表格
        if prompt and ("设置数据失败" in prompt or "线程" in prompt or "invokeMethod" in prompt):
            self.logger.warning(f"🔧 [样式保护] 检测到线程安全相关错误，跳过空表格显示以保护样式: {prompt}")
            return
        
        # 🔧 [P2优化] 递归调用防护 - 避免_show_empty_table_with_prompt和set_data之间的循环调用
        if hasattr(self, '_showing_empty_table') and self._showing_empty_table:
            self.logger.debug("🔧 [P2优化] 检测到_show_empty_table_with_prompt递归调用，跳过以防止无限循环")
            return
            
        self._showing_empty_table = True
        try:
            # 🔧 [P1-修复] 根据当前表名获取对应表头
            if hasattr(self, 'current_table_name') and self.current_table_name:
                headers = self._get_table_headers_by_type(self.current_table_name)
                self.logger.info(f"🔧 [数据流追踪] 使用表 {self.current_table_name} 的专用表头: {len(headers)}个字段")
            else:
                # 没有当前表名时，默认使用在职人员表头
                headers = self._get_standard_active_employee_headers()
                self.logger.info(f"🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: {len(headers)}个字段")
            empty_data = []
            
            # 设置空表格但使用标准表头
            if hasattr(self, 'main_workspace') and hasattr(self.main_workspace, 'set_data'):
                self.main_workspace.set_data(empty_data, headers)
            elif hasattr(self, 'expandable_table'):
                self.expandable_table.set_data(empty_data, headers)
            
            # 更新状态提示
            self.has_real_data = False
            default_prompt = "欢迎使用！系统已准备好工资表格式，请从左侧导航选择数据或导入Excel文件。"
            display_prompt = prompt if prompt else default_prompt
            self._update_status_label(display_prompt, "info")
            
            self.logger.info(f"已显示标准空表格，表头数量: {len(headers)}")
            
        except Exception as e:
            self.logger.error(f"显示空表格异常: {e}")
            
            # 异常情况下的专业处理 - 绝不能显示错误内容
            try:
                # 🔧 [P1-修复] 即使出现异常，也要坚持显示对应表类型的表头
                if hasattr(self, 'current_table_name') and self.current_table_name:
                    emergency_headers = self._get_table_headers_by_type(self.current_table_name)
                else:
                    emergency_headers = self._get_standard_active_employee_headers()
                
                if hasattr(self, 'main_workspace') and hasattr(self.main_workspace, 'set_data'):
                    self.main_workspace.set_data([], emergency_headers)
                elif hasattr(self, 'expandable_table'):
                    self.expandable_table.set_data([], emergency_headers)
                
                self._update_status_label("系统已准备好标准工资表格式", "info")
                self.logger.warning(f"异常恢复成功，显示了{len(emergency_headers)}个标准表头")
                
            except Exception as critical_error:
                self.logger.critical(f"严重异常 - 无法显示标准表头: {critical_error}")
        finally:
            # 🔧 [P2优化] 确保在任何情况下都释放递归防护锁
            self._showing_empty_table = False

    def _try_load_active_employee_data(self) -> bool:
        """尝试加载在职人员数据"""
        try:
            # 获取最新的在职人员数据路径
            latest_path = self.dynamic_table_manager.get_latest_salary_data_path()
            if not latest_path or "全部在职人员" not in latest_path:
                self.logger.info("未找到在职人员工资表数据")
                return False
                
            # 解析路径获取表名
            table_name = self._path_to_table_name(latest_path.split(' > '))
            if not table_name or not self.dynamic_table_manager.table_exists(table_name):
                self.logger.info("在职人员工资表不存在于数据库中")
                return False
                
            # 加载数据
            self.logger.info(f"尝试加载在职人员数据: {table_name}")
            self._load_data_with_new_architecture(table_name)
            return True
            
        except Exception as e:
            self.logger.error(f"尝试加载在职人员数据失败: {e}")
            return False

    # 🔧 [P0-修复] 删除重复的方法定义，统一使用第1674行的完整实现

    @pyqtSlot(str, str)
    def _update_status_label(self, message: str, status_type: str = "info"):
        """更新状态标签"""
        try:
            if hasattr(self.main_workspace, 'status_label'):
                self.main_workspace.status_label.setText(f"✓ {message}")
                self.main_workspace.status_label.setStyleSheet("color: #4CAF50;")
            elif status_type == "error":
                self.main_workspace.status_label.setText(f"✗ {message}")
                self.main_workspace.status_label.setStyleSheet("color: #F44336;")
            elif status_type == "warning":
                self.main_workspace.status_label.setText(f"⚠ {message}")
                self.main_workspace.status_label.setStyleSheet("color: #FFC107;")
            else: # info
                self.main_workspace.status_label.setText(f"ⓘ {message}")
                self.main_workspace.status_label.setStyleSheet("color: #2196F3;")
            
            # 使用 QTimer 延迟清除消息
            safe_single_shot(5000, lambda: self.main_workspace.status_label.setText(""))
        except Exception as e:
            self.logger.error(f"更新状态标签失败: {e}")
    
    def _show_info_message(self, title: str, message: str):
        """显示信息消息框。"""
        QMessageBox.information(self, title, message)

    def _show_warning_message(self, title: str, message: str):
        """显示警告消息框。"""
        QMessageBox.warning(self, title, message)

    def _show_error_message(self, title: str, message: str):
        """显示错误消息框。"""
        QMessageBox.critical(self, title, message)

    @pyqtSlot()
    def _on_refresh_data(self):
        """
        🔧 [P1-3] 全局状态刷新机制 - 综合性的系统状态刷新
        """
        self.logger.info("🔧 [P1-3] 全局状态刷新被触发，开始综合性系统刷新。")

        try:
            # 🔧 [P1-3] 执行全局状态刷新
            self._execute_global_state_refresh()

        except Exception as e:
            self.logger.error(f"🔧 [P1-3] 全局状态刷新失败: {e}")
            self._update_status_label(f"刷新失败: {str(e)}", "error")

    def _execute_global_state_refresh(self):
        """
        🔧 [P1-3] 执行全局状态刷新的核心逻辑
        """
        # 刷新进行中保护（节流/合并）
        if getattr(self, '_global_refresh_in_progress', False):
            self.logger.info("🔧 [P1-3] 全局刷新正在进行，忽略本次触发（合并）")
            return
        self._global_refresh_in_progress = True
        self.logger.info("🔧 [P1-3] 开始执行全局状态刷新流程")

        # 🔧 [P1-3] 阶段1: 系统状态预检查
        refresh_context = self._prepare_refresh_context()
        # 就绪检测网关：未就绪则提示并短延迟重试/或降级
        try:
            from src.core.readiness_gateway import is_system_ready
            ready, reason = is_system_ready(getattr(self, 'dynamic_table_manager', None))
            if not ready:
                self.logger.info(f"🔧 [网关] 系统未就绪: {reason}")
                self._update_status_label("系统未就绪，稍后自动重试或请先导入数据", "info")
                # 此处不执行完整数据层刷新与导航强制刷新，避免闪屏与不一致
                # 由调用侧或定时器稍后触发一次全局刷新即可
                return
        except Exception as e:
            self.logger.debug(f"🔧 [网关] 就绪检测异常（忽略继续）: {e}")

        # 🔧 [P1-3] 阶段2: 核心组件状态刷新
        self._refresh_core_components(refresh_context)

        # 🔧 [P1-3] 阶段3: 数据层刷新
        self._refresh_data_layer(refresh_context)

        # 🔧 [P1-3] 阶段4: UI组件状态同步
        self._refresh_ui_components(refresh_context)

        # 🔧 [P1-3] 阶段5: 显示状态修复
        self._refresh_display_state(refresh_context)

        # 🔧 [P1-3] 阶段6: 状态验证和反馈
        self._verify_refresh_results(refresh_context)
        # 结束进行中保护
        self._global_refresh_in_progress = False

    def _prepare_refresh_context(self) -> dict:
        """
        🔧 [P1-3] 准备刷新上下文信息

        Returns:
            dict: 刷新上下文信息
        """
        try:
            context = {
                'start_time': time.time(),
                'current_table': getattr(self, 'current_table_name', None),
                'has_data': False,
                'components_status': {},
                'refresh_stages': [],
                'errors': []
            }

            # 检查当前数据状态
            if hasattr(self, 'main_workspace') and hasattr(self.main_workspace, 'table_widget'):
                table_widget = self.main_workspace.table_widget
                if table_widget and table_widget.rowCount() > 0:
                    context['has_data'] = True

            # 检查关键组件状态
            context['components_status'] = {
                'navigation_panel': hasattr(self, 'navigation_panel') and self.navigation_panel is not None,
                'main_workspace': hasattr(self, 'main_workspace') and self.main_workspace is not None,
                'header_manager': hasattr(self, 'header_manager') and self.header_manager is not None,
                'table_data_service': hasattr(self, 'table_data_service') and self.table_data_service is not None
            }

            self.logger.debug(f"🔧 [P1-3] 刷新上下文准备完成: {context}")
            return context

        except Exception as e:
            self.logger.error(f"🔧 [P1-3] 准备刷新上下文失败: {e}")
            return {'start_time': time.time(), 'errors': [str(e)]}

    def _refresh_core_components(self, context: dict):
        """
        🔧 [P1-3] 刷新核心组件状态

        Args:
            context: 刷新上下文
        """
        try:
            self.logger.info("🔧 [P1-3] 开始刷新核心组件状态")
            context['refresh_stages'].append('core_components_start')

            # 🔧 [P1-3] 刷新表头管理器
            if context['components_status']['header_manager']:
                try:
                    # 强制清理所有表头重影
                    shadow_report = self.header_manager.force_comprehensive_cleanup()
                    self.logger.info(f"🔧 [P1-3] 表头重影清理完成: {shadow_report}")

                    # 重置表头管理器状态
                    if hasattr(self.header_manager, 'reset_state'):
                        self.header_manager.reset_state()

                except Exception as e:
                    context['errors'].append(f"表头管理器刷新失败: {e}")
                    self.logger.error(f"🔧 [P1-3] 表头管理器刷新失败: {e}")

            # 🔧 [P1-3] 刷新状态管理器
            if hasattr(self, 'state_manager'):
                try:
                    # 同步当前表状态
                    if context['current_table']:
                        self.state_manager.sync_states(context['current_table'])
                        self.logger.debug(f"🔧 [P1-3] 状态管理器已同步: {context['current_table']}")

                except Exception as e:
                    context['errors'].append(f"状态管理器刷新失败: {e}")
                    self.logger.error(f"🔧 [P1-3] 状态管理器刷新失败: {e}")

            context['refresh_stages'].append('core_components_complete')
            self.logger.info("🔧 [P1-3] 核心组件状态刷新完成")

        except Exception as e:
            context['errors'].append(f"核心组件刷新失败: {e}")
            self.logger.error(f"🔧 [P1-3] 核心组件刷新失败: {e}")

    def _refresh_data_layer(self, context: dict):
        """
        🔧 [P1-3] 刷新数据层

        Args:
            context: 刷新上下文
        """
        try:
            self.logger.info("🔧 [P1-3] 开始刷新数据层")
            context['refresh_stages'].append('data_layer_start')

            # 🔧 [P1-3] 刷新表格数据服务
            if context['components_status']['table_data_service'] and context['current_table']:
                try:
                    # 强制刷新当前表数据
                    response = self.table_data_service.refresh_table_data(context['current_table'])
                    if response.success:
                        self.logger.info(f"🔧 [P1-3] 表格数据刷新成功: {context['current_table']}")
                    else:
                        context['errors'].append(f"表格数据刷新失败: {response.error}")

                except Exception as e:
                    context['errors'].append(f"数据服务刷新失败: {e}")
                    self.logger.error(f"🔧 [P1-3] 数据服务刷新失败: {e}")

            # 🔧 [P1-3] 刷新导航面板数据
            if context['components_status']['navigation_panel']:
                try:
                    # 强制刷新导航面板
                    if hasattr(self.navigation_panel, 'force_refresh_salary_data'):
                        self.navigation_panel.force_refresh_salary_data()
                        self.logger.info("🔧 [P1-3] 导航面板数据已刷新")
                    elif hasattr(self.navigation_panel, 'refresh_navigation_data'):
                        self.navigation_panel.refresh_navigation_data()
                        self.logger.info("🔧 [P1-3] 导航面板数据已刷新（通用方法）")

                except Exception as e:
                    context['errors'].append(f"导航面板刷新失败: {e}")
                    self.logger.error(f"🔧 [P1-3] 导航面板刷新失败: {e}")

            context['refresh_stages'].append('data_layer_complete')
            self.logger.info("🔧 [P1-3] 数据层刷新完成")

        except Exception as e:
            context['errors'].append(f"数据层刷新失败: {e}")
            self.logger.error(f"🔧 [P1-3] 数据层刷新失败: {e}")

    def _refresh_ui_components(self, context: dict):
        """
        🔧 [P1-3] 刷新UI组件状态

        Args:
            context: 刷新上下文
        """
        try:
            self.logger.info("🔧 [P1-3] 开始刷新UI组件状态")
            context['refresh_stages'].append('ui_components_start')

            # 🔧 [P2-2] 刷新导航面板状态
            if context['components_status']['navigation_panel']:
                try:
                    if hasattr(self.navigation_panel, 'refresh_navigation_state'):
                        self.navigation_panel.refresh_navigation_state()
                        self.logger.debug("🔧 [P2-2] 导航面板状态已刷新")

                    # 🔧 [P2-2] 与全局状态同步
                    if hasattr(self.navigation_panel, 'sync_with_global_state'):
                        global_state = {
                            'current_table': getattr(self, 'current_table_name', None),
                            'navigation_path': context.get('navigation_path', []),
                            'active_tables': context.get('active_tables', [])
                        }
                        self.navigation_panel.sync_with_global_state(global_state)
                        self.logger.debug("🔧 [P2-2] 导航面板已与全局状态同步")

                except Exception as e:
                    context['errors'].append(f"导航面板状态刷新失败: {e}")
                    self.logger.error(f"🔧 [P2-2] 导航面板状态刷新失败: {e}")

            # 🔧 [P1-3] 刷新主工作区
            if context['components_status']['main_workspace']:
                try:
                    # 刷新表格组件
                    if hasattr(self.main_workspace, 'table_widget'):
                        table_widget = self.main_workspace.table_widget
                        if table_widget:
                            # 刷新表头
                            self._refresh_table_headers(table_widget)
                            # 刷新行号
                            self._refresh_row_numbers(table_widget)
                            self.logger.debug("🔧 [P1-3] 表格组件已刷新")

                    # 刷新分页组件
                    if hasattr(self.main_workspace, 'pagination_widget'):
                        pagination = self.main_workspace.pagination_widget
                        if pagination and hasattr(pagination, 'refresh_pagination_state'):
                            pagination.refresh_pagination_state()
                            self.logger.debug("🔧 [P1-3] 分页组件已刷新")

                    # 刷新工具栏状态
                    if hasattr(self.main_workspace, 'toolbar_widget'):
                        toolbar = self.main_workspace.toolbar_widget
                        if toolbar and hasattr(toolbar, 'refresh_toolbar_state'):
                            toolbar.refresh_toolbar_state()
                            self.logger.debug("🔧 [P1-3] 工具栏已刷新")

                except Exception as e:
                    context['errors'].append(f"主工作区刷新失败: {e}")
                    self.logger.error(f"🔧 [P1-3] 主工作区刷新失败: {e}")

            # 🔧 [P1-3] 刷新状态栏
            try:
                if hasattr(self, 'status_bar'):
                    self.status_bar.clearMessage()
                    self.status_bar.showMessage("刷新中...", 2000)

            except Exception as e:
                context['errors'].append(f"状态栏刷新失败: {e}")
                self.logger.error(f"🔧 [P1-3] 状态栏刷新失败: {e}")

            context['refresh_stages'].append('ui_components_complete')
            self.logger.info("🔧 [P1-3] UI组件状态刷新完成")

        except Exception as e:
            context['errors'].append(f"UI组件刷新失败: {e}")
            self.logger.error(f"🔧 [P1-3] UI组件刷新失败: {e}")

    def _refresh_display_state(self, context: dict):
        """
        🔧 [P1-3] 刷新显示状态

        Args:
            context: 刷新上下文
        """
        try:
            self.logger.info("🔧 [P1-3] 开始刷新显示状态")
            context['refresh_stages'].append('display_state_start')

            # 🔧 [P1-3] 修复表格显示亮度
            if context['components_status']['main_workspace']:
                try:
                    # 延迟执行亮度修复，确保UI更新完成
                    # 合并进统一调度
                    self._schedule_ui_fix(300)
                    self.logger.debug("🔧 [P1-3] 已安排表格亮度修复")

                except Exception as e:
                    context['errors'].append(f"亮度修复安排失败: {e}")
                    self.logger.error(f"🔧 [P1-3] 亮度修复安排失败: {e}")

            # 🔧 [P1-3] 强制重绘所有组件
            try:
                self._force_global_redraw()
                self.logger.debug("🔧 [P1-3] 全局重绘已执行")

            except Exception as e:
                context['errors'].append(f"全局重绘失败: {e}")
                self.logger.error(f"🔧 [P1-3] 全局重绘失败: {e}")

            context['refresh_stages'].append('display_state_complete')
            self.logger.info("🔧 [P1-3] 显示状态刷新完成")

        except Exception as e:
            context['errors'].append(f"显示状态刷新失败: {e}")
            self.logger.error(f"🔧 [P1-3] 显示状态刷新失败: {e}")

    def _verify_refresh_results(self, context: dict):
        """
        🔧 [P1-3] 验证刷新结果

        Args:
            context: 刷新上下文
        """
        try:
            self.logger.info("🔧 [P1-3] 开始验证刷新结果")

            # 计算刷新耗时
            elapsed_time = time.time() - context['start_time']

            # 统计刷新结果（统一口径）
            stages = context.get('refresh_stages', [])
            total_stages = len(stages)
            completed_stages = len([s for s in stages if s.endswith('_complete')])
            error_count = len(context.get('errors', []))

            # 生成刷新报告
            refresh_report = {
                'success': error_count == 0 and completed_stages == total_stages,
                'elapsed_time': elapsed_time,
                'total_stages': total_stages,
                'completed_stages': completed_stages,
                'error_count': error_count,
                'errors': context['errors']
            }

            # 更新状态显示
            if refresh_report['success']:
                status_msg = f"刷新完成 ({elapsed_time:.2f}s)"
                self._update_status_label(status_msg, "success")
                self.logger.info(f"🔧 [P1-3] 全局状态刷新成功: {refresh_report}")
            else:
                status_msg = f"刷新完成但有{error_count}个错误"
                self._update_status_label(status_msg, "warning")
                self.logger.warning(f"🔧 [P1-3] 全局状态刷新完成但有错误: {refresh_report}")

            # 记录详细报告
            self.logger.debug(f"🔧 [P1-3] 刷新详细报告: {refresh_report}")

        except Exception as e:
            self.logger.error(f"🔧 [P1-3] 验证刷新结果失败: {e}")
            self._update_status_label("刷新验证失败", "error")

    def _refresh_table_headers(self, table_widget):
        """
        🔧 [P1-3] 刷新表格表头

        Args:
            table_widget: 表格组件
        """
        try:
            if not table_widget:
                return

            # 刷新水平表头
            h_header = table_widget.horizontalHeader()
            if h_header:
                h_header.update()
                h_header.repaint()

            # 刷新垂直表头
            v_header = table_widget.verticalHeader()
            if v_header:
                v_header.update()
                v_header.repaint()

            self.logger.debug("🔧 [P1-3] 表格表头已刷新")

        except Exception as e:
            self.logger.error(f"🔧 [P1-3] 刷新表格表头失败: {e}")

    def _refresh_row_numbers(self, table_widget):
        """
        🔧 [P1-3] 刷新表格行号

        Args:
            table_widget: 表格组件
        """
        try:
            if not table_widget:
                return

            # 重新设置行号
            row_count = table_widget.rowCount()
            for row in range(row_count):
                table_widget.setVerticalHeaderItem(row, None)  # 清除现有行号

            # 强制更新垂直表头
            v_header = table_widget.verticalHeader()
            if v_header:
                v_header.update()
                v_header.repaint()

            self.logger.debug(f"🔧 [P1-3] 表格行号已刷新 ({row_count}行)")

        except Exception as e:
            self.logger.error(f"🔧 [P1-3] 刷新表格行号失败: {e}")

    def _fix_table_display_brightness_comprehensive(self):
        """
        🔧 [P1-3] 综合性表格显示亮度修复
        """
        try:
            if not hasattr(self, 'main_workspace') or not self.main_workspace:
                return

            table_widget = getattr(self.main_workspace, 'table_widget', None)
            if not table_widget:
                return

            # 使用增强的亮度检测和修复
            issues = self._detect_brightness_issues(table_widget)
            if issues:
                self.logger.info(f"🔧 [P1-3] 检测到亮度问题: {issues}")
                self._apply_brightness_fixes(table_widget, issues)

                # 验证修复效果
                if hasattr(self, '_verify_brightness_fix'):
                    self._verify_brightness_fix(table_widget)

            self.logger.debug("🔧 [P1-3] 综合性亮度修复完成")

        except Exception as e:
            self.logger.error(f"🔧 [P1-3] 综合性亮度修复失败: {e}")

    def _force_global_redraw(self):
        """
        🔧 [P1-3] 强制全局重绘
        """
        try:
            # 重绘主窗口
            self.update()
            self.repaint()

            # 重绘主工作区
            if hasattr(self, 'main_workspace') and self.main_workspace:
                self.main_workspace.update()
                self.main_workspace.repaint()

                # 重绘表格组件
                if hasattr(self.main_workspace, 'table_widget'):
                    table_widget = self.main_workspace.table_widget
                    if table_widget:
                        self._force_comprehensive_redraw(table_widget)

            # 重绘导航面板
            if hasattr(self, 'navigation_panel') and self.navigation_panel:
                self.navigation_panel.update()
                self.navigation_panel.repaint()

            # 由事件循环自然调度，无需主动处理事件

            self.logger.debug("🔧 [P1-3] 全局重绘已完成")

        except Exception as e:
            self.logger.error(f"🔧 [P1-3] 全局重绘失败: {e}")

        try:
            # 🔧 [P2-重影修复] 第一步：清理表头重影
            if hasattr(self, 'header_manager') and self.header_manager:
                self.logger.info("🔧 [P2-重影修复] 执行表头重影清理...")
                shadow_report = self.header_manager.enhanced_auto_detect_and_fix_shadows()
                if shadow_report.get('shadow_tables'):
                    fixed_count = len(shadow_report.get('fixed_tables', []))
                    self.logger.info(f"🔧 [P2-重影修复] 修复了 {fixed_count} 个表格的表头重影")
                else:
                    self.logger.debug("🔧 [P2-重影修复] 未检测到表头重影问题")

            # 🔧 [组件检查] 验证必要组件是否存在
            if not hasattr(self, 'navigation_panel') or not self.navigation_panel:
                self.logger.warning("🔧 [组件检查] 导航面板组件未初始化，仅刷新表格数据")
            else:
                # 刷新导航面板数据
                if hasattr(self.navigation_panel, 'force_refresh_salary_data'):
                    self.navigation_panel.force_refresh_salary_data()
                    self.logger.info("🔧 [组件检查] 导航面板数据已刷新")

            # 🔧 [P2-重影修复] 第二步：使用新架构的TableDataService刷新数据
            if hasattr(self, 'table_data_service') and self.table_data_service:
                if hasattr(self, 'current_table_name') and self.current_table_name:
                    self.logger.info(f"🔧 [P2-重影修复] 使用新架构刷新表格数据: {self.current_table_name}")

                    # 强制清理缓存并重新加载
                    response = self.table_data_service.refresh_table_data(self.current_table_name)
                    if response.success:
                        self.logger.info("🔧 [P2-重影修复] 数据刷新成功")
                        self._update_status_label("数据刷新成功", "success")

                        # 🔧 [P3-颜色修复] 第三步：修复显示亮度
                        self._fix_display_brightness_after_data_refresh()

                        # 🔧 [P2-重影修复] 第四步：刷新后再次检查表头重影
                        if hasattr(self, 'header_manager') and self.header_manager:
                            # 合并进统一调度，由前置检测决定是否清理
                            self._schedule_ui_fix(320)
                    else:
                        # 🔧 [P0-修复] 刷新失败时显示对应表类型的正确表头（不是默认在职人员表头）
                        self.logger.error(f"🔧 [P2-重影修复] 数据刷新失败: {response.error}")
                        # 获取当前表的正确表头类型
                        correct_headers = self._get_table_headers_by_type(self.current_table_name)
                        self.main_workspace.set_data([], correct_headers)
                        self.logger.info(f"🔧 [P2-重影修复] 已显示表 {self.current_table_name} 的正确表头: {len(correct_headers)}个字段")
                        self._update_status_label(f"数据刷新失败，已显示 {self.current_table_name} 对应的表头", "error")
                else:
                    self.logger.warning("🔧 [P2-重影修复] 没有当前表名，保持当前表格状态，不强制显示在职人员表头")
                    # 🔧 [P0-修复] 不强制刷新为默认表头，而是提示用户先选择表
                    self._update_status_label("请先选择具体的数据表后再刷新", "warning")
            else:
                self.logger.error("🔧 [P2-重影修复] TableDataService未初始化，无法刷新数据")
                self._update_status_label("服务未初始化，无法刷新", "error")

        except Exception as e:
            self.logger.error(f"🔧 [P2-重影修复] 刷新数据时发生异常: {e}")
            self._update_status_label(f"刷新失败: {str(e)}", "error")

    def _fix_display_brightness_after_data_refresh(self):
        """
        🔧 [P0-1] 智能显示亮度修复机制

        添加防抖和频率限制，避免无限循环

        修复数据导入后的表头和行号显示亮度问题，支持多种触发场景
        """
        try:
            # 🔧 [P1-2] 使用增强的递归调用防护机制
            if not self._check_method_call_protection('_fix_display_brightness_after_data_refresh'):
                return

            self.logger.info("🔧 [P1-2] 开始智能显示亮度修复（增强防护）")

            # 🔧 [P0-1] 多重检测机制
            table_widget = self._get_primary_table_widget()
            if not table_widget:
                self.logger.warning("🔧 [P0-1] 未找到有效的表格组件")
                return

            # 🔧 [P0-1] 检测当前亮度状态
            brightness_issues = self._detect_brightness_issues(table_widget)
            if not brightness_issues:
                self.logger.debug("🔧 [P0-1] 未检测到亮度问题，跳过修复")
                return

            self.logger.info(f"🔧 [P0-1] 检测到亮度问题: {brightness_issues}")

            # 🔧 [P0-1] 执行分层修复
            self._apply_brightness_fixes(table_widget, brightness_issues)

            # 🔧 [P0-1] 不再自动验证，避免递归调用
            self.logger.info("🔧 [P0-1] 智能显示亮度修复完成")

        except Exception as e:
            self.logger.error(f"🔧 [P0-1] 智能显示亮度修复失败: {e}")
            # 🔧 [P0-1] 发生异常时不再重试，避免无限循环

    def _schedule_ui_fix(self, delay_ms: int = 120):
        """合并调度亮度与表头修复，避免重复触发"""
        try:
            # 若窗口处于最小化或不可见，直接跳过调度（待还原时再执行）
            try:
                if getattr(self, "_is_minimized", False) or not self.isVisible():
                    from src.utils.logging_utils import log_throttle
                    if log_throttle('ui-fix-minimized', 1.0):
                        self.logger.debug("🔧 [UI修复] 窗口最小化/不可见，跳过合并调度")
                    return
            except Exception:
                # 状态判断失败不影响后续
                pass

            # 去重与硬节流：任何冷却期内的请求一律丢弃；仅允许单一挂起
            if not hasattr(self, '_ui_fix_token'):
                self._ui_fix_token = 0
            if not hasattr(self, '_ui_fix_cooldown_until'):
                self._ui_fix_cooldown_until = 0.0
            if not hasattr(self, '_ui_fix_pending'):
                self._ui_fix_pending = False

            from time import time
            now_ts = time()
            cooldown_sec = 0.30  # 300ms 冷却窗口

            if now_ts < getattr(self, '_ui_fix_cooldown_until', 0.0):
                return
            if self._ui_fix_pending:
                return

            self._ui_fix_token += 1
            current_token = self._ui_fix_token
            self._ui_fix_pending = True
            # 立即设置下一冷却期，防止执行刚结束又重复调度
            self._ui_fix_cooldown_until = now_ts + cooldown_sec

            from PyQt5.QtCore import QTimer
            def execute_if_latest():
                if getattr(self, '_ui_fix_token', 0) != current_token:
                    return
                # 标记执行中
                self._ui_fix_pending = False
                # 阶段1：优先执行响应式布局更新（轻量）
                try:
                    if hasattr(self, 'responsive_manager') and self.responsive_manager and self.isVisible():
                        try:
                            from src.utils.logging_utils import log_throttle
                            if log_throttle('responsive-force-update', 1.0):
                                self.logger.debug("触发响应式布局更新(force_update)")
                        except Exception:
                            pass
                        self.responsive_manager.force_update()
                except Exception:
                    pass

                # 阶段2：延迟执行亮度/表头修复（重操作）
                from src.utils.thread_safe_timer import safe_single_shot
                def delayed_heavy_fixes():
                    try:
                        # 亮度检测与修复
                        table_widget = self._get_primary_table_widget()
                        if table_widget and self.isVisible():
                            issues = self._detect_brightness_issues(table_widget)
                            if issues:
                                self.logger.info(f"UI亮度问题检测到并修复: {issues}")
                                self._fix_display_brightness_after_data_refresh()
                            else:
                                try:
                                    from src.utils.logging_utils import log_throttle
                                    if log_throttle('ui-no-brightness-issue', 1.0):
                                        self.logger.debug("UI亮度检测未发现问题，跳过修复")
                                except Exception:
                                    self.logger.debug("UI亮度检测未发现问题，跳过修复")

                        # 表头重影清理仅在有 header_manager 时执行
                        if hasattr(self, 'header_manager') and self.header_manager and self.isVisible():
                            try:
                                # 前置检测：仅在检测到重影时才清理
                                current_labels = []
                                try:
                                    if table_widget:
                                        for col in range(table_widget.columnCount()):
                                            item = table_widget.horizontalHeaderItem(col)
                                            current_labels.append(item.text() if item else "")
                                except Exception:
                                    current_labels = []
                                if current_labels:
                                    duplicates = self.header_manager._detect_header_shadows(current_labels)
                                    if duplicates:
                                        self.logger.info(f"检测到表头重影并执行清理: {duplicates}")
                                        self.header_manager.clear_table_header_immediately()
                                    else:
                                        try:
                                            from src.utils.logging_utils import log_throttle
                                            if log_throttle('no-header-shadow', 1.0):
                                                self.logger.debug("未检测到表头重影，跳过清理")
                                        except Exception:
                                            self.logger.debug("未检测到表头重影，跳过清理")
                            except Exception:
                                pass
                    except Exception:
                        pass

                safe_single_shot(500, delayed_heavy_fixes)

            safe_single_shot(max(0, int(delay_ms)), execute_if_latest)
        except Exception as e:
            self.logger.warning(f"合并调度UI修复失败: {e}")

    def _get_primary_table_widget(self):
        """
        🔧 [P1-3] 获取主要的表格组件

        Returns:
            QTableWidget: 主要表格组件，如果未找到则返回None
        """
        try:
            if hasattr(self, 'main_workspace') and self.main_workspace:
                # 优先获取expandable_table
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    return self.main_workspace.expandable_table
                # 备选table_widget
                elif hasattr(self.main_workspace, 'table_widget') and self.main_workspace.table_widget:
                    return self.main_workspace.table_widget
            return None
        except Exception as e:
            self.logger.error(f"🔧 [P1-3] 获取主要表格组件失败: {e}")
            return None

    def _flush_column_widths(self):
        """统一触发表格列宽的最终保存（窗口失焦、关闭前调用）"""
        try:
            table = self._get_primary_table_widget()
            if table and hasattr(table, 'column_width_manager') and table.column_width_manager:
                table.column_width_manager.save_on_resize_end()
                self.logger.debug("🔧 [列宽保存修复] 统一触发列宽最终保存")
        except Exception as e:
            self.logger.debug(f"列宽最终保存失败: {e}")

    def _detect_brightness_issues(self, table_widget) -> list:
        """
        🔧 [P1-2] 增强版亮度状态检测机制

        Args:
            table_widget: 表格组件

        Returns:
            list: 检测到的亮度问题列表
        """
        issues = []

        try:
            # 🔧 [P1-2] 基础组件状态检测
            if not table_widget.isEnabled():
                issues.append("table_disabled")

            # 🔧 [P1-2] 表头状态检测
            h_header = table_widget.horizontalHeader()
            if h_header:
                if not h_header.isEnabled():
                    issues.append("horizontal_header_disabled")

                # 检测表头透明度
                if h_header.graphicsEffect():
                    issues.append("horizontal_header_graphics_effect")

                # 检测表头样式问题
                h_style = h_header.styleSheet()
                if self._has_opacity_issues(h_style):
                    issues.append("horizontal_header_opacity_style")

            v_header = table_widget.verticalHeader()
            if v_header:
                if not v_header.isEnabled():
                    issues.append("vertical_header_disabled")

                # 检测行号透明度
                if v_header.graphicsEffect():
                    issues.append("vertical_header_graphics_effect")

                # 检测行号样式问题
                v_style = v_header.styleSheet()
                if self._has_opacity_issues(v_style):
                    issues.append("vertical_header_opacity_style")

            # 🔧 [P1-2] 表格主体透明度检测
            if table_widget.graphicsEffect():
                effect = table_widget.graphicsEffect()
                if hasattr(effect, 'opacity') and effect.opacity() < 1.0:
                    issues.append("table_opacity_effect")
                else:
                    issues.append("graphics_effect_present")

            # 🔧 [P1-2] 样式表深度检测
            style_sheet = table_widget.styleSheet()
            if self._has_opacity_issues(style_sheet):
                issues.append("table_opacity_in_stylesheet")

            # 🔧 [P1-1] 智能窗口状态检测
            if self._is_window_state_affecting_brightness_smart():
                issues.append("window_state_brightness_issue")

            # 🔧 [P1-2] 父组件状态检测
            parent_issues = self._detect_parent_brightness_issues(table_widget)
            issues.extend(parent_issues)

            # 🔧 [P1-2] 视觉亮度检测（基于颜色值）
            visual_issues = self._detect_visual_brightness_issues(table_widget)
            issues.extend(visual_issues)

        except Exception as e:
            self.logger.error(f"🔧 [P1-2] 检测亮度问题失败: {e}")

        return issues

    def _has_opacity_issues(self, style_sheet: str) -> bool:
        """
        🔧 [P1-2] 检测样式表中的透明度问题

        Args:
            style_sheet: 样式表字符串

        Returns:
            bool: 是否存在透明度问题
        """
        if not style_sheet:
            return False

        style_lower = style_sheet.lower()

        # 检测各种透明度设置
        opacity_indicators = [
            "opacity:",
            "rgba(",
            "hsla(",
            "background-color: transparent",
            "color: transparent",
            "border-color: transparent"
        ]

        for indicator in opacity_indicators:
            if indicator in style_lower:
                # 进一步检查是否是问题性的透明度设置
                if self._is_problematic_opacity(style_sheet, indicator):
                    return True

        return False

    def _is_problematic_opacity(self, style_sheet: str, indicator: str) -> bool:
        """
        🔧 [P1-2] 判断透明度设置是否有问题

        Args:
            style_sheet: 样式表字符串
            indicator: 透明度指示符

        Returns:
            bool: 是否是有问题的透明度设置
        """
        try:
            if "opacity:" in indicator:
                # 查找opacity值
                import re
                opacity_match = re.search(r'opacity:\s*([0-9.]+)', style_sheet, re.IGNORECASE)
                if opacity_match:
                    opacity_value = float(opacity_match.group(1))
                    return opacity_value < 0.9  # 透明度小于0.9认为有问题

            elif "rgba(" in indicator:
                # 检查rgba的alpha值
                import re
                rgba_matches = re.findall(r'rgba\([^)]+,\s*([0-9.]+)\)', style_sheet, re.IGNORECASE)
                for alpha in rgba_matches:
                    if float(alpha) < 0.9:
                        return True

            elif "transparent" in indicator:
                return True  # 完全透明肯定有问题

        except Exception:
            pass

        return False

    def _is_window_state_affecting_brightness(self) -> bool:
        """
        🔧 [P0-1] 智能检测窗口状态是否影响亮度

        添加上下文感知和防抖机制，避免无限循环

        Returns:
            bool: 窗口状态是否影响亮度
        """
        try:
            # 🔧 [P0-1] 检查是否有模态对话框打开
            app = QApplication.instance()
            if app:
                active_modal_widget = app.activeModalWidget()
                if active_modal_widget:
                    # 有模态对话框时，主窗口失去焦点是正常的，不应视为亮度问题
                    self.logger.debug("🔧 [P0-1] 检测到模态对话框，跳过窗口焦点检测")
                    # 只检查其他状态
                    if self.windowState() & Qt.WindowMinimized:
                        return True
                    if self.windowOpacity() < 1.0:
                        return True
                    return False

            # 🔧 [P0-1] 添加调用频率限制
            current_time = time.time()
            if not hasattr(self, '_last_brightness_check_time'):
                self._last_brightness_check_time = 0
            if not hasattr(self, '_brightness_check_count'):
                self._brightness_check_count = 0

            # 如果在1秒内检查次数超过5次，暂停检查
            if current_time - self._last_brightness_check_time < 1.0:
                self._brightness_check_count += 1
                if self._brightness_check_count > 5:
                    self.logger.warning("🔧 [P0-1] 亮度检测频率过高，暂停检查")
                    return False
            else:
                # 重置计数器
                self._brightness_check_count = 1
                self._last_brightness_check_time = current_time

            # 检测窗口是否最小化
            if self.windowState() & Qt.WindowMinimized:
                return True

            # 🔧 [P0-1] 智能焦点检测：只有在没有子窗口的情况下才检查焦点
            if not self.isActiveWindow():
                # 检查是否有子窗口或对话框
                child_windows = self.findChildren(QDialog)
                if not child_windows:
                    # 没有子窗口时才认为失去焦点是问题
                    return True

            # 检测窗口透明度
            if self.windowOpacity() < 1.0:
                return True

        except Exception as e:
            self.logger.debug(f"🔧 [P0-1] 检测窗口状态失败: {e}")

        return False

    def _is_window_state_affecting_brightness_smart(self) -> bool:
        """
        🔧 [P1-1] 智能窗口状态亮度检测

        更加智能的检测逻辑，减少误报

        Returns:
            bool: 窗口状态是否影响亮度
        """
        try:
            # 🔧 [P1-1] 检查是否有模态对话框打开
            app = QApplication.instance()
            if app:
                active_modal_widget = app.activeModalWidget()
                if active_modal_widget:
                    # 有模态对话框时，主窗口失去焦点是正常的
                    self.logger.debug("🔧 [P1-1] 检测到模态对话框，跳过焦点检测")
                    return False

            # 🔧 [P1-1] 添加智能防抖机制
            current_time = time.time()
            if not hasattr(self, '_last_brightness_state_check'):
                self._last_brightness_state_check = 0
            if not hasattr(self, '_brightness_state_stable_count'):
                self._brightness_state_stable_count = 0

            # 如果检测过于频繁，要求状态稳定一段时间
            if current_time - self._last_brightness_state_check < 2.0:
                self._brightness_state_stable_count += 1
                if self._brightness_state_stable_count < 3:
                    # 状态不够稳定，不报告问题
                    return False
            else:
                self._brightness_state_stable_count = 0

            self._last_brightness_state_check = current_time

            # 检测窗口是否最小化
            if self.windowState() & Qt.WindowMinimized:
                return True

            # 🔧 [P1-1] 智能焦点检测：考虑更多上下文
            if not self.isActiveWindow():
                # 检查是否有子窗口或对话框
                child_dialogs = self.findChildren(QDialog)
                if child_dialogs:
                    # 有子对话框时，失去焦点是正常的
                    return False

                # 检查是否有其他应用程序窗口在前台
                active_window = app.activeWindow()
                if active_window and active_window != self:
                    # 其他窗口在前台，这是正常的用户行为
                    return False

            # 检测窗口透明度
            if self.windowOpacity() < 1.0:
                return True

        except Exception as e:
            self.logger.debug(f"🔧 [P1-1] 智能窗口状态检测失败: {e}")

        return False

    def _detect_parent_brightness_issues(self, table_widget) -> list:
        """
        🔧 [P1-2] 检测父组件的亮度问题

        Args:
            table_widget: 表格组件

        Returns:
            list: 父组件亮度问题列表
        """
        issues = []

        try:
            parent = table_widget.parent()
            while parent:
                # 检测父组件是否禁用
                if hasattr(parent, 'isEnabled') and not parent.isEnabled():
                    issues.append("parent_disabled")
                    break

                # 检测父组件透明度
                if hasattr(parent, 'graphicsEffect') and parent.graphicsEffect():
                    effect = parent.graphicsEffect()
                    if hasattr(effect, 'opacity') and effect.opacity() < 1.0:
                        issues.append("parent_opacity_effect")
                        break

                # 检测父组件样式
                if hasattr(parent, 'styleSheet'):
                    parent_style = parent.styleSheet()
                    if self._has_opacity_issues(parent_style):
                        issues.append("parent_opacity_style")
                        break

                parent = parent.parent()

        except Exception as e:
            self.logger.debug(f"🔧 [P1-2] 检测父组件亮度问题失败: {e}")

        return issues

    def _detect_visual_brightness_issues(self, table_widget) -> list:
        """
        🔧 [P1-2] 检测视觉亮度问题（基于颜色值）

        Args:
            table_widget: 表格组件

        Returns:
            list: 视觉亮度问题列表
        """
        issues = []

        try:
            # 检测表头背景色亮度
            h_header = table_widget.horizontalHeader()
            if h_header:
                bg_color = h_header.palette().color(h_header.palette().Background)
                if self._is_color_too_dark(bg_color):
                    issues.append("horizontal_header_dark_background")

            v_header = table_widget.verticalHeader()
            if v_header:
                bg_color = v_header.palette().color(v_header.palette().Background)
                if self._is_color_too_dark(bg_color):
                    issues.append("vertical_header_dark_background")

            # 检测表格背景色亮度
            table_bg_color = table_widget.palette().color(table_widget.palette().Base)
            if self._is_color_too_dark(table_bg_color):
                issues.append("table_dark_background")

        except Exception as e:
            self.logger.debug(f"🔧 [P1-2] 检测视觉亮度问题失败: {e}")

        return issues

    def _is_color_too_dark(self, color) -> bool:
        """
        🔧 [P1-2] 判断颜色是否过暗

        Args:
            color: QColor对象

        Returns:
            bool: 颜色是否过暗
        """
        try:
            # 计算颜色亮度（使用相对亮度公式）
            r, g, b = color.red(), color.green(), color.blue()
            brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255.0

            # 亮度小于0.3认为过暗
            return brightness < 0.3

        except Exception:
            return False

    def _apply_brightness_fixes(self, table_widget, issues: list):
        """
        🔧 [P1-2] 增强版亮度修复应用机制

        Args:
            table_widget: 表格组件
            issues: 检测到的亮度问题列表
        """
        try:
            # 🔧 [P1-2] 应用递归调用防护
            if not self._check_method_call_protection('_apply_brightness_fixes'):
                return

            self.logger.info(f"🔧 [P1-2] 开始应用增强版亮度修复: {issues}")

            # 🔧 [P1-2] 基础组件状态修复
            if "table_disabled" in issues:
                table_widget.setEnabled(True)
                self.logger.debug("🔧 [P1-2] 重新启用表格组件")

            # 🔧 [P1-2] 表头状态修复
            h_header = table_widget.horizontalHeader()
            if h_header:
                if "horizontal_header_disabled" in issues:
                    h_header.setEnabled(True)
                    self.logger.debug("🔧 [P1-2] 重新启用水平表头")

                if "horizontal_header_graphics_effect" in issues:
                    h_header.setGraphicsEffect(None)
                    self.logger.debug("🔧 [P1-2] 移除水平表头透明度效果")

                if "horizontal_header_opacity_style" in issues:
                    self._clean_opacity_from_stylesheet(h_header)
                    self.logger.debug("🔧 [P1-2] 清理水平表头样式透明度")

                if "horizontal_header_dark_background" in issues:
                    self._fix_header_background_color(h_header)
                    self.logger.debug("🔧 [P1-2] 修复水平表头背景色")

            v_header = table_widget.verticalHeader()
            if v_header:
                if "vertical_header_disabled" in issues:
                    v_header.setEnabled(True)
                    self.logger.debug("🔧 [P1-2] 重新启用垂直表头")

                if "vertical_header_graphics_effect" in issues:
                    v_header.setGraphicsEffect(None)
                    self.logger.debug("🔧 [P1-2] 移除垂直表头透明度效果")

                if "vertical_header_opacity_style" in issues:
                    self._clean_opacity_from_stylesheet(v_header)
                    self.logger.debug("🔧 [P1-2] 清理垂直表头样式透明度")

                if "vertical_header_dark_background" in issues:
                    self._fix_header_background_color(v_header)
                    self.logger.debug("🔧 [P1-2] 修复垂直表头背景色")

            # 🔧 [P1-2] 表格主体透明度修复
            if "table_opacity_effect" in issues or "graphics_effect_present" in issues:
                table_widget.setGraphicsEffect(None)
                self.logger.debug("🔧 [P1-2] 移除表格透明度效果")

            # 🔧 [P1-2] 表格样式修复
            if "table_opacity_in_stylesheet" in issues or "opacity_in_stylesheet" in issues:
                self._clean_opacity_from_stylesheet(table_widget)
                self.logger.debug("🔧 [P1-2] 清理表格样式透明度")

            if "table_dark_background" in issues:
                self._fix_table_background_color(table_widget)
                self.logger.debug("🔧 [P1-2] 修复表格背景色")

            # 🔧 [P1-1] 智能窗口状态修复
            if "window_state_brightness_issue" in issues:
                self._fix_window_state_brightness_smart()
                self.logger.debug("🔧 [P1-1] 智能修复窗口状态亮度问题")

            # 🔧 [P1-2] 父组件问题修复
            if any(issue.startswith("parent_") for issue in issues):
                self._fix_parent_brightness_issues(table_widget, issues)
                self.logger.debug("🔧 [P1-2] 修复父组件亮度问题")

            # 🔧 [P1-2] 强制重绘和更新
            self._force_comprehensive_redraw(table_widget)

        except Exception as e:
            self.logger.error(f"🔧 [P1-2] 应用亮度修复失败: {e}")

    def _clean_opacity_from_stylesheet(self, widget):
        """
        🔧 [P1-2] 清理组件样式表中的透明度设置

        Args:
            widget: 要清理的组件
        """
        try:
            style_sheet = widget.styleSheet()
            if not style_sheet:
                return

            # 分行处理样式表
            lines = style_sheet.split('\n')
            cleaned_lines = []

            for line in lines:
                line_lower = line.lower()
                # 跳过包含透明度设置的行
                if any(keyword in line_lower for keyword in ['opacity:', 'rgba(', 'hsla(', 'transparent']):
                    continue
                cleaned_lines.append(line)

            # 设置清理后的样式表
            widget.setStyleSheet('\n'.join(cleaned_lines))

        except Exception as e:
            self.logger.debug(f"🔧 [P1-2] 清理样式表透明度失败: {e}")

    def _fix_header_background_color(self, header):
        """
        🔧 [P1-2] 修复表头背景色

        Args:
            header: 表头组件
        """
        try:
            # 设置标准的表头背景色
            palette = header.palette()
            palette.setColor(palette.Background, QColor(240, 240, 240))  # 浅灰色
            palette.setColor(palette.Button, QColor(240, 240, 240))
            header.setPalette(palette)

        except Exception as e:
            self.logger.debug(f"🔧 [P1-2] 修复表头背景色失败: {e}")

    def _fix_table_background_color(self, table_widget):
        """
        🔧 [P1-2] 修复表格背景色

        Args:
            table_widget: 表格组件
        """
        try:
            # 设置标准的表格背景色
            palette = table_widget.palette()
            palette.setColor(palette.Base, QColor(255, 255, 255))  # 白色
            palette.setColor(palette.AlternateBase, QColor(248, 248, 248))  # 浅灰色
            table_widget.setPalette(palette)

        except Exception as e:
            self.logger.debug(f"🔧 [P1-2] 修复表格背景色失败: {e}")

    def _fix_window_state_brightness(self):
        """
        🔧 [P1-2] 修复窗口状态导致的亮度问题
        """
        try:
            # 确保窗口透明度正常
            if self.windowOpacity() < 1.0:
                self.setWindowOpacity(1.0)

            # 激活窗口
            if not self.isActiveWindow():
                self.activateWindow()
                self.raise_()

        except Exception as e:
            self.logger.debug(f"🔧 [P1-2] 修复窗口状态亮度失败: {e}")

    def _fix_window_state_brightness_smart(self):
        """
        🔧 [P1-1] 智能修复窗口状态导致的亮度问题

        更加谨慎的修复策略，避免不必要的操作
        """
        try:
            # 🔧 [P1-1] 检查是否真的需要修复
            app = QApplication.instance()
            if app and app.activeModalWidget():
                # 有模态对话框时不进行窗口激活操作
                self.logger.debug("🔧 [P1-1] 检测到模态对话框，跳过窗口激活")
                return

            # 确保窗口透明度正常
            if self.windowOpacity() < 1.0:
                self.setWindowOpacity(1.0)
                self.logger.debug("🔧 [P1-1] 已恢复窗口透明度")

            # 🔧 [P1-1] 谨慎的窗口激活：只在确实需要时激活
            if not self.isActiveWindow():
                # 检查是否有其他重要窗口在前台
                active_window = app.activeWindow() if app else None
                if not active_window or active_window.parent() == self:
                    # 只有在没有其他重要窗口或者活动窗口是我们的子窗口时才激活
                    self.activateWindow()
                    self.raise_()
                    self.logger.debug("🔧 [P1-1] 已激活主窗口")
                else:
                    self.logger.debug("🔧 [P1-1] 检测到其他活动窗口，跳过激活")

        except Exception as e:
            self.logger.debug(f"🔧 [P1-1] 智能修复窗口状态亮度失败: {e}")

    def _fix_parent_brightness_issues(self, table_widget, issues: list):
        """
        🔧 [P1-2] 修复父组件亮度问题

        Args:
            table_widget: 表格组件
            issues: 问题列表
        """
        try:
            parent = table_widget.parent()
            while parent:
                if "parent_disabled" in issues and hasattr(parent, 'setEnabled'):
                    parent.setEnabled(True)
                    break

                if "parent_opacity_effect" in issues and hasattr(parent, 'setGraphicsEffect'):
                    parent.setGraphicsEffect(None)
                    break

                if "parent_opacity_style" in issues and hasattr(parent, 'styleSheet'):
                    self._clean_opacity_from_stylesheet(parent)
                    break

                parent = parent.parent()

        except Exception as e:
            self.logger.debug(f"🔧 [P1-2] 修复父组件亮度问题失败: {e}")

    def _force_comprehensive_redraw(self, table_widget):
        """
        🔧 [P1-2] 强制全面重绘

        Args:
            table_widget: 表格组件
        """
        try:
            # 表格主体重绘
            table_widget.update()
            table_widget.repaint()

            # 表头重绘
            h_header = table_widget.horizontalHeader()
            if h_header:
                h_header.update()
                h_header.repaint()

            v_header = table_widget.verticalHeader()
            if v_header:
                v_header.update()
                v_header.repaint()

            # 视口重绘
            viewport = table_widget.viewport()
            if viewport:
                viewport.update()
                viewport.repaint()

            # 由事件循环自然调度，无需主动处理事件

        except Exception as e:
            self.logger.debug(f"🔧 [P1-2] 强制重绘失败: {e}")

    def _verify_brightness_fix(self, table_widget):
        """
        🔧 [P0-1] 验证亮度修复效果（无递归版本）

        Args:
            table_widget: 表格组件
        """
        try:
            remaining_issues = self._detect_brightness_issues(table_widget)
            if remaining_issues:
                self.logger.warning(f"🔧 [P0-1] 亮度修复后仍有问题: {remaining_issues}")
                # 🔧 [P0-1] 不再执行二次修复，避免无限循环
                self.logger.info("🔧 [P0-1] 跳过二次修复，避免递归调用")
            else:
                self.logger.info("🔧 [P0-1] 亮度修复验证通过")

        except Exception as e:
            self.logger.error(f"🔧 [P0-1] 验证亮度修复效果失败: {e}")

    def _delayed_brightness_fix(self):
        """🔧 [P3-颜色修复] 延迟亮度修复，处理窗口状态变化"""
        try:
            if hasattr(self, 'main_workspace') and self.main_workspace:
                # 🔧 [P0-修复] 获取实际的表格组件，而不是MainWorkspaceArea
                table_widget = None
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    table_widget = self.main_workspace.expandable_table
                elif hasattr(self.main_workspace, 'table_widget') and self.main_workspace.table_widget:
                    table_widget = self.main_workspace.table_widget

                if table_widget:
                    # 再次确保组件状态正常
                    table_widget.setEnabled(True)

                    # 强制更新表头
                    horizontal_header = table_widget.horizontalHeader()
                    vertical_header = table_widget.verticalHeader()

                    if horizontal_header:
                        horizontal_header.setEnabled(True)
                        horizontal_header.update()

                    if vertical_header:
                        vertical_header.setEnabled(True)
                        vertical_header.update()

                    # 最终重绘
                    table_widget.update()

                    self.logger.debug("🔧 [P3-颜色修复] 延迟亮度修复完成")
                else:
                    self.logger.warning("🔧 [P3-颜色修复] 延迟修复时未找到有效的表格组件")

        except Exception as e:
            self.logger.error(f"🔧 [P3-颜色修复] 延迟亮度修复失败: {e}")

    def _fix_table_display_brightness(self):
        """🔧 [P0-修复] PrototypeMainWindow级别的表格显示亮度修复方法"""
        try:
            self.logger.debug("🔧 [P0-修复] PrototypeMainWindow开始修复表格显示亮度")

            # 获取实际的表格组件
            if hasattr(self, 'main_workspace') and self.main_workspace:
                table_widget = None
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    table_widget = self.main_workspace.expandable_table
                elif hasattr(self.main_workspace, 'table_widget') and self.main_workspace.table_widget:
                    table_widget = self.main_workspace.table_widget

                if table_widget:
                    # 确保表格组件处于启用状态
                    if not table_widget.isEnabled():
                        table_widget.setEnabled(True)
                        self.logger.debug("🔧 [P0-修复] 重新启用表格组件")

                    # 修复水平表头亮度
                    horizontal_header = table_widget.horizontalHeader()
                    if horizontal_header:
                        horizontal_header.setEnabled(True)
                        horizontal_header.update()
                        self.logger.debug("🔧 [P0-修复] 修复水平表头亮度")

                    # 修复垂直表头（行号）亮度
                    vertical_header = table_widget.verticalHeader()
                    if vertical_header:
                        vertical_header.setEnabled(True)
                        vertical_header.update()
                        self.logger.debug("🔧 [P0-修复] 修复垂直表头亮度")

                    # 移除可能的透明度效果
                    if table_widget.graphicsEffect():
                        table_widget.setGraphicsEffect(None)
                        self.logger.debug("🔧 [P0-修复] 移除表格透明度效果")

                    # 强制重绘表格
                    table_widget.update()
                    table_widget.repaint()

                    self.logger.debug("🔧 [P0-修复] PrototypeMainWindow表格显示亮度修复完成")
                else:
                    self.logger.warning("🔧 [P0-修复] 未找到有效的表格组件")

        except Exception as e:
            self.logger.error(f"🔧 [P0-修复] PrototypeMainWindow修复表格显示亮度失败: {e}")

    def _fix_table_display_brightness_mainworkspace_level(self):
        """🔧 [P3-颜色修复] 修复表格显示亮度（MainWorkspaceArea级别）"""
        try:
            self.logger.debug("🔧 [P3-颜色修复] 开始修复表格显示亮度")

            # 修复expandable_table的显示亮度
            if hasattr(self, 'expandable_table') and self.expandable_table:
                table = self.expandable_table

                # 确保表格组件处于启用状态
                if not table.isEnabled():
                    table.setEnabled(True)
                    self.logger.debug("🔧 [P3-颜色修复] 重新启用expandable_table")

                # 修复水平表头亮度
                horizontal_header = table.horizontalHeader()
                if horizontal_header:
                    horizontal_header.setEnabled(True)
                    horizontal_header.update()
                    self.logger.debug("🔧 [P3-颜色修复] 修复expandable_table水平表头亮度")

                # 修复垂直表头（行号）亮度
                vertical_header = table.verticalHeader()
                if vertical_header:
                    vertical_header.setEnabled(True)
                    vertical_header.update()
                    self.logger.debug("🔧 [P3-颜色修复] 修复expandable_table垂直表头亮度")

                # 移除可能的透明度效果
                if table.graphicsEffect():
                    table.setGraphicsEffect(None)
                    self.logger.debug("🔧 [P3-颜色修复] 移除expandable_table透明度效果")

                # 强制重绘表格
                table.update()
                table.repaint()

                self.logger.debug("🔧 [P3-颜色修复] expandable_table显示亮度修复完成")

        except Exception as e:
            self.logger.error(f"🔧 [P3-颜色修复] 修复表格显示亮度失败: {e}")

    def showEvent(self, event):
        """窗口显示时触发的事件，用于初始化加载。"""
        super().showEvent(event)
        # 🔧 [统一调度] 窗口显示后统一通过合并调度执行必要修复与响应式刷新
        self._schedule_ui_fix(200)

    def changeEvent(self, event):
        """
        🔧 [P1-3] 窗口状态变化事件处理

        处理窗口最大化、最小化、激活等状态变化时的亮度修复
        """
        super().changeEvent(event)

        try:
            # 处理窗口状态变化
            if event.type() == QEvent.WindowStateChange:
                self.logger.debug(f"🔧 [P1-3] 窗口状态变化: {self.windowState()}")

                # 最小化：标记并不触发任何UI修复；暂停表头清理与监控
                if self.windowState() & Qt.WindowMinimized:
                    setattr(self, "_is_minimized", True)
                    self.logger.debug("🔧 [窗口状态] 进入最小化状态，跳过UI修复调度")
                    try:
                        from src.gui.table_header_manager import get_global_header_manager
                        hdr = get_global_header_manager()
                        if hasattr(hdr, 'stop_shadow_monitoring'):
                            hdr.stop_shadow_monitoring()
                        if hasattr(hdr, 'pause_cleanup'):
                            hdr.pause_cleanup()
                    except Exception:
                        pass
                    return

                # 从最小化还原到正常/最大化：统一单通道恢复
                if (self.windowState() & Qt.WindowMaximized) or (self.windowState() == Qt.WindowNoState):
                    # single-flight：避免并发恢复
                    if getattr(self, "_restore_in_progress", False):
                        return
                    setattr(self, "_restore_in_progress", True)
                    try:
                        if getattr(self, "_is_minimized", False):
                            setattr(self, "_is_minimized", False)
                        # 统一调度一次 UI 修复（略大延迟避开尺寸剧变）
                        self._schedule_ui_fix(280)
                        self.logger.debug("🔧 [窗口状态] 统一还原恢复：合并调度一次UI修复")
                        # 延迟恢复表头清理定时器与监控（确保只恢复一份）
                        try:
                            from src.utils.thread_safe_timer import safe_single_shot
                            from src.gui.table_header_manager import get_global_header_manager
                            def _resume_hdr():
                                try:
                                    hdr = get_global_header_manager()
                                    if hasattr(hdr, 'resume_cleanup'):
                                        hdr.resume_cleanup(0)
                                    if hasattr(hdr, 'start_shadow_monitoring'):
                                        hdr.start_shadow_monitoring(2000)
                                except Exception:
                                    pass
                            safe_single_shot(700, _resume_hdr)
                        except Exception:
                            pass
                    finally:
                        setattr(self, "_restore_in_progress", False)

            # 处理窗口激活状态变化
            elif event.type() == QEvent.ActivationChange:
                if self.isActiveWindow():
                    # 取消激活即调度的行为，避免与还原场景重复触发
                    self.logger.debug("🔧 [窗口状态] 窗口激活，跳过UI修复调度（避免重复）")
                else:
                    # 🔧 [列宽最终保存] 窗口失焦时刷新一次列宽保存
                    try:
                        self._flush_column_widths()
                    except Exception as e:
                        self.logger.debug(f"列宽最终保存(窗口失焦)失败: {e}")

        except Exception as e:
            self.logger.error(f"🔧 [P1-3] 处理窗口状态变化失败: {e}")

    def _get_modern_styles(self) -> str:
        """获取现代化的QSS样式表。"""
        return """
            /* 主窗口样式 */
            QMainWindow {
                background-color: #FAFAFA;
                color: #212121;
                font-family: 'Microsoft YaHei', sans-serif;
                font-size: 14px;
            }
            
            /* Material Design按钮样式 */
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 500;
            }
            
            QPushButton:hover {
                background-color: #1976D2;
            }
            
            QPushButton:pressed {
                background-color: #1565C0;
            }
            
            QPushButton:disabled {
                background-color: #BDBDBD;
                color: #757575;
            }
            
            /* 输入框样式 */
            QLineEdit, QTextEdit, QComboBox {
                border: 2px solid #E0E0E0;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #2196F3;
            }
            
            /* 分割器样式 */
            QSplitter::handle {
                background-color: #E0E0E0;
                width: 3px;
                height: 3px;
            }
            
            QSplitter::handle:hover {
                background-color: #BDBDBD;
            }
            
            /* 状态栏样式 */
            QStatusBar {
                background-color: #F5F5F5;
                border-top: 1px solid #E0E0E0;
                padding: 4px;
            }
            
            /* 菜单栏样式 */
            QMenuBar {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 4px;
            }
            
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 4px;
            }
            
            QMenuBar::item:selected {
                background-color: #1976D2;
            }
            
            /* 工具栏样式 */
            QToolBar {
                background-color: #F5F5F5;
                border: 1px solid #E0E0E0;
                spacing: 2px;
                padding: 4px;
            }
            
            QToolButton {
                background-color: transparent;
                border: none;
                border-radius: 4px;
                padding: 6px;
            }
            
            QToolButton:hover {
                background-color: #E3F2FD;
            }
            
            QToolButton:pressed {
                background-color: #BBDEFB;
            }
        """ 

    def resizeEvent(self, event):
        """处理窗口大小变化事件 - 统一调度、合并执行"""
        super().resizeEvent(event)
        try:
            self.window_resized.emit(event.size())
        except Exception:
            pass

        # 🔧 [统一调度-快速止血] 使用稳定定时器：仅在停止拖动后触发一次合并修复
        try:
            from PyQt5.QtCore import QTimer
            if not hasattr(self, '_resize_stable_timer') or self._resize_stable_timer is None:
                self._resize_stable_timer = QTimer(self)
                self._resize_stable_timer.setSingleShot(True)
                self._resize_stable_timer.timeout.connect(lambda: self._schedule_ui_fix(0))
            if self._resize_stable_timer.isActive():
                self._resize_stable_timer.stop()
            self._resize_stable_timer.start(300)
        except Exception:
            # 回退到原有机制
            self._schedule_ui_fix(180)
    
    # ===================== 增强版表头管理器集成方法 =====================
    
    def _complete_table_reset_on_navigation(self):
        """
        🆕 渐进式表格状态迁移机制 - 新架构
        
        核心原则：
        1. 保持UI连续性，避免闪烁和空白
        2. 异步状态清理，不阻塞用户交互  
        3. 原子性状态更新，确保一致性
        4. 遵循现行格式统一管理机制
        """
        try:
            self.logger.debug("🆕 [新架构] 开始渐进式表格状态迁移")
            
            # ============= 第一阶段：准备迁移状态 =============
            
            # 1. 设置迁移锁，防止并发状态修改
            self._navigation_transition_lock = True
            
            # 2. 保存当前有效状态供迁移使用
            current_state = self._capture_current_table_state()
            
            # 🚫 [用户要求] 导航切换也不显示任何加载条！
            old_table = getattr(self, 'current_table_name', None)
            new_table = getattr(self, '_pending_table_name', None)
            self.logger.info(f"🚫 [用户要求] 表格切换不显示加载条: {old_table} -> {new_table}")
            
            # ============= 第二阶段：最小化必要清理 =============
            
            # 4. 清理表名相关状态（新表需要重新设置）
            self._clear_table_specific_state()
            
            # 5. 清理排序状态（新表应该重新开始排序）
            self._clear_sort_state_gracefully()
            
            # 6. 标记缓存为待更新状态，而不是立即清空
            self._mark_cache_for_refresh()

            # 🔧 [P1-2] 表间切换时的表头重影预防清理
            self._clear_header_shadows_on_navigation()

            self.logger.info("🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载")
            
        except Exception as e:
            self.logger.error(f"🆕 [新架构] 渐进式状态迁移失败: {e}")
            # 降级处理：至少确保基本功能
            self._emergency_state_cleanup()
    
    # 🗑️ [新架构] 旧的_reset_sort_state_for_table方法已移除，使用_clear_sort_state_gracefully替代
    # ============= 渐进式状态管理辅助方法 - 新架构 =============
    
    def _save_current_table_ui_state(self, table_name: str):
        """🔧 [P0-1修复] 保存当前表格的UI状态（列宽、排序等）"""
        try:
            if not table_name:
                return
                
            # 确保有工作区和表格组件
            if not hasattr(self.main_workspace, 'expandable_table') or not self.main_workspace.expandable_table:
                self.logger.debug("没有可用的表格组件，跳过状态保存")
                return
            
            table_widget = self.main_workspace.expandable_table
            
            # 🔧 [P0-2修复] 首先收集当前表格状态数据
            if not hasattr(self, '_table_ui_state_cache'):
                self._table_ui_state_cache = {}
            
            # 收集当前表格状态
            current_state = {
                'column_widths': {},
                'sort_columns': [],
                'ui_state': {
                    'column_order': [],
                    'selection': [],
                    'scroll_position': 0
                }
            }
            
            # 保存列宽（转换为字典格式）
            header = table_widget.horizontalHeader()
            for i in range(table_widget.columnCount()):
                column_name = table_widget.horizontalHeaderItem(i).text() if table_widget.horizontalHeaderItem(i) else f"列{i}"
                current_state['column_widths'][column_name] = header.sectionSize(i)
            
            # 保存排序状态（转换为列表格式）
            sort_section = header.sortIndicatorSection()
            sort_order = header.sortIndicatorOrder()
            if sort_section >= 0:
                column_name = table_widget.horizontalHeaderItem(sort_section).text() if table_widget.horizontalHeaderItem(sort_section) else f"列{sort_section}"
                current_state['sort_columns'] = [{
                    'column': column_name,
                    'order': 'asc' if sort_order == 0 else 'desc'
                }]
            
            # 保存UI状态
            for i in range(table_widget.columnCount()):
                current_state['ui_state']['column_order'].append(header.logicalIndex(i))
            
            # 🔧 [P0-2修复] 优先使用StateManager，传递正确的状态字典
            if hasattr(self, 'state_manager') and self.state_manager:
                try:
                    success = self.state_manager.save_table_state_by_name(table_name, current_state)
                    if success:
                        self.logger.info(f"🔧 [P0-2修复] 通过StateManager保存状态成功: {table_name}")
                        # 同时保存到内存缓存作为备份
                        self._table_ui_state_cache[table_name] = current_state
                        return
                except Exception as e:
                    self.logger.warning(f"🔧 [P0-2修复] StateManager保存失败，使用内存缓存: {e}")
            
            # 降级到内存缓存方案
            # 保存选择状态
            for item in table_widget.selectedItems():
                current_state['ui_state']['selection'].append((item.row(), item.column()))
            
            # 缓存状态
            self._table_ui_state_cache[table_name] = current_state
            
            self.logger.info(f"🔧 [P0-2修复] 已保存表格UI状态（内存缓存）: {table_name}, "
                           f"列宽: {len(current_state['column_widths'])}个, "
                           f"排序列: {len(current_state['sort_columns'])}")
            
        except Exception as e:
            self.logger.error(f"🔧 [P0-1修复] 保存表格UI状态失败 {table_name}: {e}")

    def _verify_pagination_ui_sync(self, expected_page: int, table_name: str, data):
        """🔧 [P1-2修复] 验证分页UI同步状态"""
        try:
            # 验证分页组件状态
            if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                actual_page = self.main_workspace.pagination_widget.state.current_page
                if actual_page != expected_page:
                    self.logger.warning(f"🔧 [P1-2修复] 分页状态不同步: 期望第{expected_page}页, 实际第{actual_page}页")
                    # 尝试修复
                    self.main_workspace.pagination_widget.silent_set_current_page(expected_page)
                
            # 验证表头显示
            if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                table = self.main_workspace.expandable_table
                if table.columnCount() > 0:
                    first_header = table.horizontalHeaderItem(0).text() if table.horizontalHeaderItem(0) else ""
                    # 检查是否显示中文表头（避免显示数字）
                    if first_header.isdigit():
                        self.logger.error(f"🔧 [P1-2修复] 表头显示错误: 显示数字'{first_header}'而非中文")
                        # 尝试重新应用表头
                        if hasattr(data, 'columns'):
                            headers = list(data.columns)
                            for i, header in enumerate(headers):
                                if i < table.columnCount():
                                    table.setHorizontalHeaderItem(i, table.horizontalHeaderItem(i).__class__(header))
                    else:
                        self.logger.debug(f"🔧 [P1-2修复] 表头验证通过: '{first_header}'")
            
            self.logger.debug(f"🔧 [P1-2修复] 分页UI同步验证完成: 第{expected_page}页, 表{table_name}")
            
        except Exception as e:
            self.logger.error(f"🔧 [P1-2修复] 分页UI同步验证失败: {e}")

    def _restore_table_ui_state(self, table_name: str):
        """🔧 [P0-2修复] 恢复表格的UI状态（列宽、排序等）"""
        try:
            if not table_name:
                return
                
            # 确保有工作区和表格组件
            if not hasattr(self.main_workspace, 'expandable_table') or not self.main_workspace.expandable_table:
                self.logger.debug("没有可用的表格组件，跳过状态恢复")
                return
            
            table_widget = self.main_workspace.expandable_table
            
            # 🔧 [P0-3修复] 优先使用StateManager恢复状态
            if hasattr(self, 'state_manager') and self.state_manager:
                try:
                    # 🔧 [P0-新4修复] 修正方法调用参数，只传递table_name
                    success = self.state_manager.restore_table_state_by_name(table_name)
                    if success:
                        self.logger.info(f"🔧 [P0-新4修复] 通过StateManager恢复状态成功: {table_name}")
                        return
                except Exception as e:
                    self.logger.warning(f"🔧 [P0-新4修复] StateManager恢复失败，尝试内存缓存: {e}")
            
            # 降级到内存缓存方案
            if not hasattr(self, '_table_ui_state_cache') or table_name not in self._table_ui_state_cache:
                self.logger.debug(f"没有找到缓存的表格状态: {table_name}")
                return
            
            saved_state = self._table_ui_state_cache[table_name]
            
            # 恢复列宽
            if 'column_widths' in saved_state:
                header = table_widget.horizontalHeader()
                for i, width in enumerate(saved_state['column_widths']):
                    try:
                        # 🔧 [P0-新2修复] 确保width为数值类型，避免str/int比较错误
                        width_int = int(width) if isinstance(width, (str, float)) else width
                        if i < table_widget.columnCount() and width_int > 0:
                            header.resizeSection(i, width_int)
                    except (ValueError, TypeError):
                        self.logger.warning(f"🔧 [P0-新2修复] 无效的列宽值: {width}, 列索引: {i}")
                        continue
            
            # 恢复排序状态
            if 'sort_column' in saved_state and 'sort_order' in saved_state:
                sort_column = saved_state['sort_column']
                sort_order = saved_state['sort_order']
                try:
                    # 🔧 [P0-新2修复] 确保sort_column为数值类型，避免str/int比较错误
                    sort_column_int = int(sort_column) if isinstance(sort_column, (str, float)) else sort_column
                    if sort_column_int >= 0 and sort_column_int < table_widget.columnCount():
                        header = table_widget.horizontalHeader()
                        header.setSortIndicator(sort_column_int, sort_order)
                except (ValueError, TypeError):
                    self.logger.warning(f"🔧 [P0-新2修复] 无效的排序列值: {sort_column}")
                    pass
            
            # 恢复选择状态
            if 'selection' in saved_state:
                table_widget.clearSelection()
                for row, col in saved_state['selection']:
                    if row < table_widget.rowCount() and col < table_widget.columnCount():
                        table_widget.setCurrentCell(row, col)
            
            self.logger.info(f"🔧 [P0-2修复] 已恢复表格UI状态（内存缓存）: {table_name}, "
                           f"列宽: {len(saved_state.get('column_widths', []))}个")
            
        except Exception as e:
            self.logger.error(f"🔧 [P0-2修复] 恢复表格UI状态失败 {table_name}: {e}")
    
    def _capture_current_table_state(self) -> dict:
        """捕获当前表格状态供迁移使用"""
        try:
            state = {
                'has_data': False,
                'row_count': 0,
                'column_count': 0,
                'pagination_enabled': False
            }
            
            if hasattr(self, 'main_workspace') and self.main_workspace:
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    table = self.main_workspace.expandable_table
                    state['has_data'] = table.rowCount() > 0
                    state['row_count'] = table.rowCount()
                    state['column_count'] = table.columnCount()
                    
                if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                    state['pagination_enabled'] = self.main_workspace.pagination_widget.isVisible()
            
            self.logger.debug(f"🆕 [新架构] 捕获状态: {state}")
            return state
            
        except Exception as e:
            self.logger.error(f"捕获表格状态失败: {e}")
            return {}
    
    def _clear_table_specific_state(self):
        """清理表格特定状态，保留UI结构"""
        try:
            # 清理表名状态
            if hasattr(self, 'main_workspace') and self.main_workspace:
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    table = self.main_workspace.expandable_table
                    table.current_table_name = None
                    self.logger.debug("🆕 [新架构] 已清理表名状态")
            
            # 重置主窗口表名（将由新数据设置）
            self.current_table_name = None
            
        except Exception as e:
            self.logger.error(f"清理表格特定状态失败: {e}")
    
    def _clear_sort_state_gracefully(self):
        """优雅地清理排序状态"""
        try:
            # 清理主窗口排序状态
            if hasattr(self, 'current_sort_columns'):
                self.current_sort_columns = []
                
            # 重置表头排序指示器
            if hasattr(self, 'main_workspace') and self.main_workspace:
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    table = self.main_workspace.expandable_table
                    if hasattr(table, 'horizontalHeader'):
                        header = table.horizontalHeader()
                        header.setSortIndicator(-1, Qt.AscendingOrder)
                        
            self.logger.debug("🆕 [新架构] 已优雅清理排序状态")
            
        except Exception as e:
            self.logger.error(f"清理排序状态失败: {e}")
    
    def _mark_cache_for_refresh(self):
        """标记缓存为待刷新状态，而不是立即清空"""
        try:
            # 标记分页缓存为待刷新（不立即清空）
            if hasattr(self, 'pagination_cache') and self.pagination_cache:
                # 设置刷新标志，在新数据到达时再清理
                setattr(self.pagination_cache, '_refresh_pending', True)
                self.logger.debug("🆕 [新架构] 已标记分页缓存为待刷新")
                
        except Exception as e:
            self.logger.error(f"标记缓存刷新失败: {e}")

    def _clear_header_shadows_on_navigation(self):
        """
        🔧 [P1-2] 表间切换时的表头重影预防清理

        在表间切换时主动清理可能的表头重影，防止重影问题传播到新表
        """
        try:
            self.logger.debug("🔧 [P1-2] 开始表间切换表头重影预防清理")

            # 1. 获取当前表格组件
            current_table = None
            if hasattr(self, 'main_workspace') and self.main_workspace:
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    current_table = self.main_workspace.expandable_table

            if not current_table:
                self.logger.debug("🔧 [P1-2] 未找到当前表格，跳过表头清理")
                return

            # 2. 检测当前表头是否有重影
            current_labels = []
            try:
                for col in range(current_table.columnCount()):
                    item = current_table.horizontalHeaderItem(col)
                    if item:
                        current_labels.append(item.text())
                    else:
                        current_labels.append("")
            except Exception as e:
                self.logger.debug(f"🔧 [P1-2] 获取表头标签失败: {e}")
                return

            # 3. 使用表头管理器检测重影
            if hasattr(self, 'header_manager') and self.header_manager:
                duplicates = self.header_manager._detect_header_shadows(current_labels)
                if duplicates:
                    self.logger.warning(f"🔧 [P1-2] 表间切换时检测到表头重影: {duplicates}")

                    # 4. 执行预防性清理
                    table_id = f"navigation_switch_{id(current_table)}"
                    self.header_manager.register_table(table_id, current_table)
                    self.header_manager.clear_table_header_immediately(table_id)

                    self.logger.info(f"🔧 [P1-2] 已执行表间切换表头重影清理")
                else:
                    self.logger.debug("🔧 [P1-2] 表间切换时未检测到表头重影")

            # 5. 清理行号重影
            self._clear_row_number_shadows_on_navigation(current_table)

        except Exception as e:
            self.logger.error(f"🔧 [P1-2] 表间切换表头重影清理失败: {e}")

    def _clear_row_number_shadows_on_navigation(self, table):
        """
        🔧 [P1-2] 表间切换时的行号重影预防清理

        Args:
            table: 当前表格组件
        """
        try:
            self.logger.debug("🔧 [P1-2] 开始表间切换行号重影预防清理")

            # 清理垂直表头（行号）
            v_header = table.verticalHeader()
            if v_header:
                # 强制更新行号显示
                v_header.update()
                v_header.clearSelection()

                # 检查是否有重复的行号
                row_labels = []
                for row in range(min(table.rowCount(), 10)):  # 只检查前10行
                    item = table.verticalHeaderItem(row)
                    if item:
                        row_labels.append(item.text())

                # 检测行号重复
                seen = set()
                duplicates = []
                for label in row_labels:
                    if label in seen:
                        duplicates.append(label)
                    else:
                        seen.add(label)

                if duplicates:
                    self.logger.warning(f"🔧 [P1-2] 表间切换时检测到行号重影: {duplicates}")
                    # 强制重绘行号
                    v_header.reset()
                    v_header.update()
                    self.logger.info("🔧 [P1-2] 已执行行号重影清理")
                else:
                    self.logger.debug("🔧 [P1-2] 表间切换时未检测到行号重影")

        except Exception as e:
            self.logger.error(f"🔧 [P1-2] 表间切换行号重影清理失败: {e}")

    def _emergency_state_cleanup(self):
        """🔧 [P2-1修复] 紧急状态清理，确保基本功能"""
        try:
            self.logger.warning("🔧 [P2-1修复] 执行紧急状态清理")
            
            # 🔧 [P2-1修复] 确保释放所有锁定状态
            if hasattr(self, '_navigation_transition_lock'):
                self._navigation_transition_lock = False
            if hasattr(self, '_setting_data_lock'):
                self._setting_data_lock = False
            if hasattr(self, '_processing_pagination'):
                self._processing_pagination = False
                
            # 🔧 [P2-1修复] 基本状态重置
            self.current_table_name = None
            if hasattr(self, 'current_sort_columns'):
                self.current_sort_columns = []
            
            # 🔧 [P2-1修复] 清理操作上下文
            if hasattr(self, '_current_operation_context'):
                self._current_operation_context = None
            
            # 🔧 [P2-1修复] 确保UI状态缓存初始化
            if not hasattr(self, '_table_ui_state_cache'):
                self._table_ui_state_cache = {}
            
            # 🔧 [P2-1修复] 重置分页状态（如果可用）
            try:
                if hasattr(self.main_workspace, 'pagination_widget') and self.main_workspace.pagination_widget:
                    self.main_workspace.pagination_widget.reset()
                    self.logger.info("🔧 [P2-1修复] 分页组件已重置")
            except Exception:
                pass  # 降级：忽略分页重置失败
                
            # 🔧 [P2-1修复] 确保错误处理管理器可用
            if not hasattr(self, 'error_handler') or not self.error_handler:
                self.logger.warning("🔧 [P2-1修复] ErrorHandler不可用，基本功能仍可正常使用")
                
            self.logger.info("🔧 [P2-1修复] 紧急状态清理完成，系统进入安全模式")
                
        except Exception as e:
            self.logger.error(f"🔧 [P2-1修复] 紧急状态清理失败: {e}")
            # 🔧 [P2-1修复] 最后的安全网：确保最基本的运行状态
            try:
                self.current_table_name = None
                self._setting_data_lock = False
            except:
                pass

    def complete_navigation_transition(self):
        """完成导航迁移，清理过渡状态"""
        try:
            if hasattr(self, '_navigation_transition_lock') and self._navigation_transition_lock:
                # 清理加载状态
                if hasattr(self, 'main_workspace') and self.main_workspace:
                    if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                        # 🚫 [用户要求] 不使用加载条，删除set_loading_state调用
                        pass
                
                # 清理待刷新的缓存
                if hasattr(self, 'pagination_cache') and self.pagination_cache:
                    if hasattr(self.pagination_cache, '_refresh_pending') and self.pagination_cache._refresh_pending:
                        # 现在可以安全清理缓存
                        self.pagination_cache.clear_all_cache()
                        self.pagination_cache._refresh_pending = False
                        self.logger.debug("🆕 [新架构] 已完成缓存清理")
                
                # 🆕 [新架构] 执行组件状态一致性验证
                self._validate_component_state_consistency()
                
                # 释放迁移锁
                self._navigation_transition_lock = False
                self.logger.info("🆕 [新架构] 导航迁移完成")
                
        except Exception as e:
            self.logger.error(f"完成导航迁移失败: {e}")
            # 确保释放锁
            if hasattr(self, '_navigation_transition_lock'):
                self._navigation_transition_lock = False

    def _validate_component_state_consistency(self):
        """🆕 [新架构] 验证组件状态一致性"""
        try:
            self.logger.debug("🆕 [新架构] 开始组件状态一致性验证")
            
            validation_results = {
                'table_pagination_sync': False,
                'data_state_sync': False,
                'ui_component_sync': False,
                'overall_consistency': False
            }
            
            # 1. 验证表格与分页组件状态同步
            validation_results['table_pagination_sync'] = self._validate_table_pagination_sync()
            
            # 2. 验证数据状态同步
            validation_results['data_state_sync'] = self._validate_data_state_sync()
            
            # 3. 验证UI组件状态同步
            validation_results['ui_component_sync'] = self._validate_ui_component_sync()
            
            # 4. 计算整体一致性
            validation_results['overall_consistency'] = all([
                validation_results['table_pagination_sync'],
                validation_results['data_state_sync'],
                validation_results['ui_component_sync']
            ])
            
            if validation_results['overall_consistency']:
                self.logger.info("✅ [新架构] 组件状态一致性验证通过")
            else:
                self.logger.warning(f"⚠️ [新架构] 组件状态不一致: {validation_results}")
                # 尝试自动修复
                self._auto_fix_state_inconsistency(validation_results)
                
        except Exception as e:
            self.logger.error(f"组件状态一致性验证失败: {e}")

    def _validate_table_pagination_sync(self) -> bool:
        """验证表格与分页组件状态同步"""
        try:
            if not (hasattr(self, 'main_workspace') and self.main_workspace):
                return True  # 没有组件时认为是一致的
                
            table = getattr(self.main_workspace, 'expandable_table', None)
            pagination_widget = getattr(self.main_workspace, 'pagination_widget', None)
            
            if not (table and pagination_widget):
                return True  # 组件不存在时认为是一致的
                
            # 检查表格分页状态与分页组件状态是否一致
            table_pagination_state = getattr(table, 'pagination_state', None)
            pagination_state = pagination_widget.state
            
            if table_pagination_state and pagination_state:
                sync_check = (
                    table_pagination_state.get('current_page') == pagination_state.current_page and
                    table_pagination_state.get('total_records') == pagination_state.total_records
                )
                
                if not sync_check:
                    self.logger.warning(f"表格分页状态不同步: 表格={table_pagination_state}, 分页组件={pagination_state.__dict__}")
                    
                return sync_check
                
            return True
            
        except Exception as e:
            self.logger.error(f"验证表格分页同步失败: {e}")
            return False

    def _validate_data_state_sync(self) -> bool:
        """验证数据状态同步"""
        try:
            # 检查主窗口表名与表格组件表名是否一致
            main_table_name = getattr(self, 'current_table_name', None)
            
            if hasattr(self, 'main_workspace') and self.main_workspace:
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    table_name = getattr(self.main_workspace.expandable_table, 'current_table_name', None)
                    
                    if main_table_name != table_name:
                        self.logger.warning(f"表名状态不同步: 主窗口={main_table_name}, 表格组件={table_name}")
                        return False
                        
            return True
            
        except Exception as e:
            self.logger.error(f"验证数据状态同步失败: {e}")
            return False

    def _validate_ui_component_sync(self) -> bool:
        """验证UI组件状态同步"""
        try:
            # 检查是否有数据但UI显示为空的情况
            if hasattr(self, 'main_workspace') and self.main_workspace:
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    table = self.main_workspace.expandable_table
                    
                    # 检查表格是否有行但显示为空
                    if hasattr(table, 'original_data'):
                        has_original_data = len(getattr(table, 'original_data', [])) > 0
                        has_ui_rows = table.rowCount() > 0
                        
                        if has_original_data and not has_ui_rows:
                            self.logger.warning("UI组件状态不同步: 有原始数据但UI显示为空")
                            return False
                            
            return True
            
        except Exception as e:
            self.logger.error(f"验证UI组件同步失败: {e}")
            return False

    def _auto_fix_state_inconsistency(self, validation_results: dict):
        """自动修复状态不一致问题"""
        try:
            self.logger.info("🔧 [新架构] 开始自动修复状态不一致")
            
            # 修复表格分页同步问题
            if not validation_results['table_pagination_sync']:
                self._fix_table_pagination_sync()
                
            # 修复数据状态同步问题
            if not validation_results['data_state_sync']:
                self._fix_data_state_sync()
                
            # 修复UI组件同步问题
            if not validation_results['ui_component_sync']:
                self._fix_ui_component_sync()
                
            self.logger.info("🔧 [新架构] 状态不一致自动修复完成")
            
        except Exception as e:
            self.logger.error(f"自动修复状态不一致失败: {e}")

    def _fix_table_pagination_sync(self):
        """修复表格分页同步问题"""
        try:
            if hasattr(self, 'main_workspace') and self.main_workspace:
                pagination_widget = getattr(self.main_workspace, 'pagination_widget', None)
                table = getattr(self.main_workspace, 'expandable_table', None)
                
                if pagination_widget and table:
                    # 以分页组件状态为准，同步到表格组件
                    pagination_state = {
                        'current_page': pagination_widget.state.current_page,
                        'page_size': pagination_widget.state.page_size,
                        'total_records': pagination_widget.state.total_records,
                        'start_record': (pagination_widget.state.current_page - 1) * pagination_widget.state.page_size + 1,
                        'end_record': min(pagination_widget.state.current_page * pagination_widget.state.page_size, pagination_widget.state.total_records)
                    }
                    table.set_pagination_state(pagination_state)
                    self.logger.info("🔧 已修复表格分页同步问题")
                    
        except Exception as e:
            self.logger.error(f"修复表格分页同步失败: {e}")

    def _fix_data_state_sync(self):
        """修复数据状态同步问题"""
        try:
            # 以主窗口表名为准，同步到表格组件
            main_table_name = getattr(self, 'current_table_name', None)
            
            if main_table_name and hasattr(self, 'main_workspace') and self.main_workspace:
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    self.main_workspace.expandable_table.current_table_name = main_table_name
                    self.logger.info(f"🔧 已修复数据状态同步: {main_table_name}")
                    
        except Exception as e:
            self.logger.error(f"修复数据状态同步失败: {e}")

    def _fix_ui_component_sync(self):
        """修复UI组件同步问题"""
        try:
            # 对于有数据但UI显示为空的情况，尝试重新设置数据
            if hasattr(self, 'main_workspace') and self.main_workspace:
                if hasattr(self.main_workspace, 'expandable_table') and self.main_workspace.expandable_table:
                    table = self.main_workspace.expandable_table
                    
                    if hasattr(table, 'original_data') and len(getattr(table, 'original_data', [])) > 0:
                        if table.rowCount() == 0:
                            # 重新设置数据
                            original_data = table.original_data
                            original_headers = getattr(table, 'original_headers', [])
                            
                            if original_data and original_headers:
                                table.set_data(original_data, original_headers, current_table_name=table.current_table_name)
                                self.logger.info("🔧 已修复UI组件同步问题：重新设置数据")
                    
        except Exception as e:
            self.logger.error(f"修复UI组件同步失败: {e}")
    
    # 🔧 [新架构] 旧的分页状态隔离方法已移除，新架构统一管理分页状态
    
    def _register_all_tables_to_header_manager(self):
        """
        注册所有表格组件到表头管理器
        
        这个方法会扫描窗口中所有的QTableWidget组件并注册到表头管理器
        """
        try:
            # 查找所有QTableWidget组件
            all_tables = self.findChildren(QTableWidget)
            registered_count = 0
            
            for i, table in enumerate(all_tables):
                # 生成唯一的表格ID
                table_id = f"table_{i}_{id(table)}"
                
                # 尝试获取更有意义的ID
                if hasattr(table, 'objectName') and table.objectName():
                    table_id = f"{table.objectName()}_{id(table)}"
                elif hasattr(table, 'table_name'):
                    table_id = f"{table.table_name}_{id(table)}"
                
                # 注册到表头管理器
                if self.header_manager.register_table(table_id, table):
                    registered_count += 1
                    self.logger.debug(f"表格 {table_id} 已注册到表头管理器")
            
            self.logger.info(f"已注册 {registered_count} 个表格到表头管理器")
            
        except Exception as e:
            self.logger.error(f"注册表格到表头管理器失败: {e}")
    
    def _conditional_header_cleanup(self):
        """
        有条件的表头清理（仅在真正需要时执行）
        """
        try:
            # 先检测是否有重影问题
            self._register_all_tables_to_header_manager()

            # 使用增强版检测和修复
            if hasattr(self.header_manager, 'enhanced_auto_detect_and_fix_shadows'):
                report = self.header_manager.enhanced_auto_detect_and_fix_shadows()

                if report.get('shadow_tables'):
                    fixed_count = len(report.get('fixed_tables', []))
                    failed_count = len(report.get('failed_tables', []))

                    self.logger.info(f"检测到表头重影，修复成功: {fixed_count} 个，修复失败: {failed_count} 个")

                    if hasattr(self, 'main_workspace'):
                        if fixed_count > 0:
                            self.main_workspace._update_status_label(
                                f"已修复 {fixed_count} 个表格的表头重影", "info"
                            )
                        if failed_count > 0:
                            self.main_workspace._update_status_label(
                                f"警告：{failed_count} 个表格修复失败", "warning"
                            )
                else:
                    self.logger.debug("未检测到表头重影问题，跳过清理")
            else:
                # 回退到普通版本
                shadow_tables = self.header_manager.auto_detect_and_fix_shadows()

                if shadow_tables:
                    self.logger.info(f"检测到表头重影，已自动修复: {list(shadow_tables.keys())}")
                    if hasattr(self, 'main_workspace'):
                        self.main_workspace._update_status_label(
                            f"已修复 {len(shadow_tables)} 个表格的表头重影", "info"
                        )
                else:
                    self.logger.debug("未检测到表头重影问题，跳过清理")
            
        except Exception as e:
            self.logger.error(f"条件表头清理失败: {e}")
            # 回退到原有的清理方法
            try:
                if hasattr(self, 'menu_bar_manager'):
                    self.menu_bar_manager._clear_table_header_cache()
            except Exception as backup_error:
                self.logger.error(f"🔧 [P1-移除旧架构] 备用清理方法也失败: {backup_error}")

    # 🗑️ [新架构] 旧的_pre_clear_headers_on_navigation_change方法已移除，新架构不需要复杂的表头预清理

    def _enhanced_header_cleanup(self, table_id: Optional[str] = None, delay_ms: int = 100):
        """
        增强版表头清理（已废弃，使用_conditional_header_cleanup代替）
        
        Args:
            table_id: 指定表格ID，None表示清理所有
            delay_ms: 延迟清理时间（毫秒）
        """
        self.logger.warning("_enhanced_header_cleanup已废弃，改用_conditional_header_cleanup")
        self._conditional_header_cleanup()
    
    @pyqtSlot(str)
    def _on_header_cleaned(self, table_id: str):
        """
        表头清理完成信号处理
        
        Args:
            table_id: 完成清理的表格ID
        """
        try:
            self.logger.debug(f"表格 {table_id} 表头清理完成")
            
            # 更新状态显示
            if hasattr(self, 'main_workspace'):
                self.main_workspace._update_status_label(
                    f"表头重影修复完成", "info"
                )
            
        except Exception as e:
            self.logger.error(f"处理表头清理完成信号失败: {e}")
    
    @pyqtSlot(str, list)
    def _on_header_shadow_detected(self, table_id: str, duplicate_labels: List[str]):
        """
        表头重影检测信号处理（记录信息，不自动触发修复）
        
        Args:
            table_id: 检测到重影的表格ID
            duplicate_labels: 重复的标签列表
        """
        try:
            self.logger.warning(f"检测到表格 {table_id} 表头重影: {duplicate_labels}")
            
            # 仅显示信息，不自动触发修复
            if hasattr(self, 'main_workspace'):
                self.main_workspace._update_status_label(
                    f"检测到表头重影问题", "warning"
                )
            
        except Exception as e:
            self.logger.error(f"处理表头重影检测信号失败: {e}")
    
    def force_header_cleanup_all_tables(self):
        """
        强制清理所有表格的表头重影
        
        这是一个公共方法，可以在需要时手动调用
        """
        try:
            self.logger.info("开始强制清理所有表格的表头重影")
            
            # 先检测重影
            shadow_tables = self.header_manager.auto_detect_and_fix_shadows()
            
            if shadow_tables:
                self.logger.warning(f"检测到 {len(shadow_tables)} 个表格存在重影，已自动修复")
                
                # 显示修复结果
                if hasattr(self, 'main_workspace'):
                    self.main_workspace._update_status_label(
                        f"已修复 {len(shadow_tables)} 个表格的表头重影问题", "info"
                    )
            else:
                self.logger.info("未检测到表头重影问题")
                
                # 显示正常状态
                if hasattr(self, 'main_workspace'):
                    self.main_workspace._update_status_label(
                        "表头状态正常，无重影问题", "info"
                    )
            
            # 执行一次全面清理
            self._enhanced_header_cleanup(None, 100)
            
        except Exception as e:
            self.logger.error(f"强制清理表头重影失败: {e}")
            self._show_error_message("表头清理失败", f"清理过程中发生错误: {e}")
    
    def get_header_manager_statistics(self) -> Dict[str, Any]:
        """
        获取表头管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            if hasattr(self, 'header_manager'):
                return self.header_manager.get_statistics()
            else:
                return {'error': '表头管理器未初始化'}
        except Exception as e:
            self.logger.error(f"获取表头管理器统计信息失败: {e}")
            return {'error': str(e)}
    
    def validate_all_table_headers(self) -> Dict[str, Dict[str, Any]]:
        """
        验证所有表格的表头状态
        
        Returns:
            Dict[str, Dict[str, Any]]: 验证结果，键为表格ID，值为验证结果
        """
        validation_results = {}
        
        try:
            # 确保表格已注册
            self._register_all_tables_to_header_manager()
            
            # 验证每个注册的表格
            for table_id in self.header_manager.registered_tables.keys():
                result = self.header_manager.validate_header_state(table_id)
                validation_results[table_id] = result
            
            # 统计验证结果
            valid_count = sum(1 for r in validation_results.values() if r.get('is_valid', False))
            total_count = len(validation_results)
            
            self.logger.info(f"表头验证完成: {valid_count}/{total_count} 个表格状态正常")
            
        except Exception as e:
            self.logger.error(f"验证表头状态失败: {e}")
            validation_results['error'] = str(e)
        
        return validation_results

    def _show_header_customization_dialog(self):
        """显示表级字段偏好设置对话框"""
        try:
            # 检查是否有当前表
            if not hasattr(self, 'current_table_name') or not self.current_table_name:
                QMessageBox.information(self, "提示", "请先选择一个数据表")
                return

            # 获取当前表的所有可用字段
            all_fields_dict = self._get_all_available_fields(self.current_table_name)
            if not all_fields_dict:
                QMessageBox.warning(self, "错误", "无法获取表格字段信息")
                return

            # 转换为字段列表
            all_fields = list(all_fields_dict.keys())

            # 获取字段映射
            from src.modules.data_import.config_sync_manager import ConfigSyncManager
            # 🔧 [P1修复] 使用单例模式
            config_sync = self.config_sync_manager if hasattr(self, "config_sync_manager") else ConfigSyncManager.get_instance()
            field_mappings = config_sync.get_mapping(self.current_table_name) or {}

            # 创建并显示表级字段偏好设置对话框
            from src.gui.table_field_preference_dialog import TableFieldPreferenceDialog
            dialog = TableFieldPreferenceDialog(
                table_name=self.current_table_name,
                all_fields=all_fields,
                field_mappings=field_mappings,
                parent=self
            )

            # 连接信号
            dialog.preference_saved.connect(self._on_table_field_preference_saved)
            dialog.preference_deleted.connect(self._on_table_field_preference_deleted)

            # 显示对话框
            dialog.exec_()

        except Exception as e:
            self.logger.error(f"显示表级字段偏好设置对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"显示字段偏好设置对话框失败: {e}")

    def _on_table_field_preference_saved(self, table_name: str, preferred_fields: list):
        """处理表级字段偏好保存事件"""
        try:
            self.logger.info(f"表级字段偏好已保存: {table_name}, {len(preferred_fields)} 个字段")

            # 如果是当前表，刷新显示
            if hasattr(self, 'current_table_name') and self.current_table_name == table_name:
                self._reload_current_table_data()

                # 更新状态信息
                if hasattr(self, 'main_workspace'):
                    self.main_workspace._update_status_label(
                        f"表级字段偏好已更新，显示 {len(preferred_fields)} 个字段", "success"
                    )

        except Exception as e:
            self.logger.error(f"处理表级字段偏好保存事件失败: {e}")

    def _on_table_field_preference_deleted(self, table_name: str):
        """处理表级字段偏好删除事件"""
        try:
            self.logger.info(f"表级字段偏好已删除: {table_name}")

            # 如果是当前表，刷新显示
            if hasattr(self, 'current_table_name') and self.current_table_name == table_name:
                self._reload_current_table_data()

                # 更新状态信息
                if hasattr(self, 'main_workspace'):
                    self.main_workspace._update_status_label(
                        "表级字段偏好已删除，恢复显示所有字段", "info"
                    )

        except Exception as e:
            self.logger.error(f"处理表级字段偏好删除事件失败: {e}")

    def _reset_headers_to_default(self):
        """重置表头为默认显示"""
        try:
            # 检查是否有当前表
            if not hasattr(self, 'current_table_name') or not self.current_table_name:
                QMessageBox.information(self, "提示", "请先选择一个数据表")
                return

            # 确认重置操作
            reply = QMessageBox.question(
                self, "确认重置",
                "确定要重置表头为默认显示吗？这将清除您的自定义设置。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 删除用户表头偏好
                from src.modules.data_import.config_sync_manager import ConfigSyncManager
                # 🔧 [P1修复] 使用单例模式
                config_sync = self.config_sync_manager if hasattr(self, "config_sync_manager") else ConfigSyncManager.get_instance()
                success = config_sync.remove_user_header_preference(self.current_table_name)

                if success:
                    # 刷新表格显示
                    self._reload_current_table_data()

                    self.logger.info(f"表头已重置为默认显示: {self.current_table_name}")
                    QMessageBox.information(self, "成功", "表头已重置为默认显示")
                else:
                    QMessageBox.warning(self, "警告", "重置表头失败，请重试")

        except Exception as e:
            self.logger.error(f"重置表头失败: {e}")
            QMessageBox.critical(self, "错误", f"重置表头失败: {e}")

    def _get_all_available_fields(self, table_name: str) -> Dict[str, str]:
        """获取表的所有可用字段

        Args:
            table_name: 表名

        Returns:
            Dict[str, str]: {字段名: 显示名}
        """
        try:
            # 获取数据库字段
            table_columns = self.dynamic_table_manager.get_table_columns(table_name)
            db_fields = [col['name'] for col in table_columns if 'name' in col]

            # 获取字段映射
            # 🆕 [新架构] 通过架构工厂获取ConfigSyncManager
            if hasattr(self, "config_sync_manager") and self.config_sync_manager:
                config_sync = self.config_sync_manager
            elif hasattr(self, 'architecture_factory') and self.architecture_factory:
                config_sync = self.architecture_factory.get_config_sync_manager()
            else:
                self.logger.error("无法获取ConfigSyncManager，跳过字段映射")
                return {}
            mapping = config_sync.load_mapping(table_name)

            # 构建字段字典
            all_fields = {}
            for field in db_fields:
                display_name = mapping.get(field, field) if mapping else field
                all_fields[field] = display_name

            self.logger.debug(f"获取表 {table_name} 可用字段: {len(all_fields)} 个")
            return all_fields

        except Exception as e:
            self.logger.error(f"获取表 {table_name} 可用字段失败: {e}")
            return {}

    def _get_current_displayed_fields(self) -> List[str]:
        """获取当前显示的字段列表

        Returns:
            List[str]: 当前显示的字段名列表
        """
        try:
            if not hasattr(self, 'current_table_name') or not self.current_table_name:
                return []

            # 获取用户表头偏好
            from src.modules.data_import.config_sync_manager import ConfigSyncManager
            # 🔧 [P1修复] 使用单例模式
            config_sync = self.config_sync_manager if hasattr(self, "config_sync_manager") else ConfigSyncManager.get_instance()
            user_fields = config_sync.get_user_header_preference(self.current_table_name)

            if user_fields:
                return user_fields
            else:
                # 如果没有用户偏好，返回所有字段
                table_columns = self.dynamic_table_manager.get_table_columns(self.current_table_name)
                return [col['name'] for col in table_columns if 'name' in col]

        except Exception as e:
            self.logger.error(f"获取当前显示字段失败: {e}")
            return []

    def _save_user_header_preference(self, selected_fields: List[str]):
        """保存用户表头偏好

        Args:
            selected_fields: 用户选择的字段列表
        """
        try:
            if not hasattr(self, 'current_table_name') or not self.current_table_name:
                return False

            # 保存用户偏好
            from src.modules.data_import.config_sync_manager import ConfigSyncManager
            # 🔧 [P1修复] 使用单例模式
            config_sync = self.config_sync_manager if hasattr(self, "config_sync_manager") else ConfigSyncManager.get_instance()
            success = config_sync.save_user_header_preference(self.current_table_name, selected_fields)

            if success:
                self.logger.info(f"用户表头偏好保存成功: {self.current_table_name}, {len(selected_fields)} 个字段")
            else:
                self.logger.error(f"用户表头偏好保存失败: {self.current_table_name}")

            return success

        except Exception as e:
            self.logger.error(f"保存用户表头偏好失败: {e}")
            return False

    def _reload_current_table_data(self):
        """重新加载当前表格数据"""
        try:
            if not hasattr(self, 'current_table_name') or not self.current_table_name:
                self.logger.warning("没有当前表名，无法重新加载数据")
                return

            self.logger.info(f"重新加载表格数据: {self.current_table_name}")

            # 检查是否在分页模式
            if (hasattr(self.main_workspace, 'pagination_widget') and
                self.main_workspace.pagination_widget and
                self.main_workspace.pagination_widget.isVisible()):

                # 分页模式：重新加载当前页
                current_page = self.main_workspace.pagination_widget.get_current_page()
                page_size = self.main_workspace.pagination_widget.get_page_size()
                self._load_data_with_pagination(self.current_table_name, current_page, page_size)
            else:
                # 普通模式：重新加载全部数据
                worker = Worker(self._load_database_data_with_mapping, self.current_table_name)
                worker.signals.result.connect(lambda df: self.main_workspace.set_data(df, preserve_headers=True, table_name=self.current_table_name, current_table_name=self.current_table_name))
                self.thread_pool.start(worker)

        except Exception as e:
            self.logger.error(f"重新加载当前表格数据失败: {e}", exc_info=True)
    
    # 🔧 [兼容性修复] 老架构期望的属性和方法
    # 🔧 [P1-移除旧架构] 删除 _setup_legacy_compatibility 方法

    def _setup_error_handling(self):
        """
        🔧 [P2-3] 设置错误处理机制
        """
        try:
            # 连接错误处理信号
            self.error_handler.error_occurred.connect(self._on_error_occurred)
            self.error_handler.error_recovered.connect(self._on_error_recovered)

            # 注册恢复策略
            self._register_recovery_strategies()

            # 设置状态栏消息处理
            self._setup_status_message_handling()

            self.logger.info("🔧 [P2-3] 错误处理机制设置完成")

        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 设置错误处理机制失败: {e}")

    def _init_recursive_call_protection(self):
        """
        🔧 [P1-2] 初始化递归调用防护机制
        """
        try:
            # 方法调用计数器
            self._method_call_counts = {}

            # 时间窗口调用历史
            self._method_call_history = {}

            # 防护配置
            self._protection_config = {
                '_fix_display_brightness_after_data_refresh': {
                    'max_calls_per_minute': 30,  # 🔧 [P1-1修复] 放宽每分钟调用限制，减少WARNING
                    'max_calls_per_second': 3,   # 🔧 [P1-1修复] 放宽每秒调用限制，允许合理的连续调用
                    'cooldown_period': 1.0       # 🔧 [P1-1修复] 减少冷却期，提高响应性
                },
                '_apply_brightness_fixes': {
                    'max_calls_per_minute': 20,  # 🔧 [P1-1修复] 适度增加限制
                    'max_calls_per_second': 5,   # 🔧 [P1-1修复] 放宽每秒限制
                    'cooldown_period': 2.0       # 🔧 [P1-1修复] 减少冷却期
                },
                '_verify_brightness_fix': {
                    'max_calls_per_minute': 15,  # 🔧 [P1-1修复] 适度增加限制
                    'max_calls_per_second': 2,   # 🔧 [P1-1修复] 放宽每秒限制
                    'cooldown_period': 1.5       # 🔧 [P1-1修复] 减少冷却期
                }
            }

            # 冷却状态
            self._method_cooldowns = {}

            self.logger.info("🔧 [P1-2] 递归调用防护机制初始化完成")

        except Exception as e:
            self.logger.error(f"🔧 [P1-2] 初始化递归调用防护失败: {e}")

    def _check_method_call_protection(self, method_name: str) -> bool:
        """
        🔧 [P1-2] 检查方法调用是否被防护限制

        Args:
            method_name: 方法名

        Returns:
            bool: True表示可以调用，False表示被限制
        """
        try:
            current_time = time.time()

            # 检查是否在冷却期
            if method_name in self._method_cooldowns:
                cooldown_end = self._method_cooldowns[method_name]
                if current_time < cooldown_end:
                    remaining = cooldown_end - current_time
                    self.logger.warning(f"🔧 [P1-2] 方法 {method_name} 在冷却期，剩余 {remaining:.1f}秒")
                    return False
                else:
                    # 冷却期结束，移除记录
                    del self._method_cooldowns[method_name]

            # 获取防护配置
            config = self._protection_config.get(method_name, {})
            if not config:
                return True  # 没有配置的方法不限制

            # 初始化调用历史
            if method_name not in self._method_call_history:
                self._method_call_history[method_name] = []

            call_history = self._method_call_history[method_name]

            # 清理过期的调用记录
            call_history[:] = [t for t in call_history if current_time - t < 60]  # 保留1分钟内的记录

            # 检查每秒调用限制
            recent_calls = [t for t in call_history if current_time - t < 1.0]
            if len(recent_calls) >= config.get('max_calls_per_second', 999):
                self.logger.warning(f"🔧 [P1-2] 方法 {method_name} 每秒调用次数超限: {len(recent_calls)}")
                self._apply_cooldown(method_name, config.get('cooldown_period', 5.0))
                return False

            # 检查每分钟调用限制
            if len(call_history) >= config.get('max_calls_per_minute', 999):
                self.logger.warning(f"🔧 [P1-2] 方法 {method_name} 每分钟调用次数超限: {len(call_history)}")
                self._apply_cooldown(method_name, config.get('cooldown_period', 5.0))
                return False

            # 记录本次调用
            call_history.append(current_time)

            return True

        except Exception as e:
            self.logger.error(f"🔧 [P1-2] 检查方法调用防护失败: {e}")
            return True  # 出错时允许调用，避免阻塞正常功能

    def _apply_cooldown(self, method_name: str, cooldown_period: float):
        """
        🔧 [P1-2] 对方法应用冷却期

        Args:
            method_name: 方法名
            cooldown_period: 冷却期时长（秒）
        """
        try:
            cooldown_end = time.time() + cooldown_period
            self._method_cooldowns[method_name] = cooldown_end
            self.logger.info(f"🔧 [P1-2] 对方法 {method_name} 应用 {cooldown_period}秒 冷却期")

        except Exception as e:
            self.logger.error(f"🔧 [P1-2] 应用冷却期失败: {e}")

    def _register_recovery_strategies(self):
        """
        🔧 [P2-3] 注册错误恢复策略
        """
        try:
            # 数据错误恢复策略
            self.error_handler.register_recovery_strategy(
                ErrorCategory.DATA_ERROR,
                ErrorSeverity.MEDIUM,
                self._recover_data_error
            )

            # UI错误恢复策略
            self.error_handler.register_recovery_strategy(
                ErrorCategory.UI_ERROR,
                ErrorSeverity.MEDIUM,
                self._recover_ui_error
            )

            # 系统错误恢复策略
            self.error_handler.register_recovery_strategy(
                ErrorCategory.SYSTEM_ERROR,
                ErrorSeverity.MEDIUM,
                self._recover_system_error
            )

            self.logger.info("🔧 [P2-3] 错误恢复策略注册完成")

        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 注册错误恢复策略失败: {e}")

    def _setup_status_message_handling(self):
        """
        🔧 [P2-3] 设置状态栏消息处理
        """
        try:
            # 监听状态消息事件
            from src.core.event_bus import EventBus
            event_bus = EventBus()

            def handle_status_message(event):
                try:
                    if event.event_type == "status_message":
                        data = event.data
                        message = data.get('message', '')
                        timeout = data.get('timeout', 3000)
                        severity = data.get('severity', 'info')

                        # 在状态栏显示消息
                        if hasattr(self, 'footer') and hasattr(self.footer, 'navigation_label'):
                            self.footer.navigation_label.setText(message)

                            # 根据严重程度设置颜色
                            color_map = {
                                'low': '#4CAF50',      # 绿色
                                'medium': '#FF9800',   # 橙色
                                'high': '#F44336',     # 红色
                                'critical': '#9C27B0'  # 紫色
                            }
                            color = color_map.get(severity, '#4CAF50')
                            self.footer.navigation_label.setStyleSheet(
                                f"color: {color}; font-size: 12px; font-weight: bold;"
                            )

                            # 设置定时器恢复默认状态
                            safe_single_shot(timeout, lambda: self._reset_status_message())

                except Exception as e:
                    self.logger.error(f"🔧 [P2-3] 处理状态消息失败: {e}")

            # 🔧 [P0-2] 修复事件处理器注册方式
            # 使用正确的事件总线订阅方法
            handler_id = event_bus.subscribe(
                event_type="status_message",
                handler_func=handle_status_message
            )
            self.logger.debug(f"🔧 [P0-2] 状态消息处理器已注册，ID: {handler_id}")

            self.logger.debug("🔧 [P2-3] 状态栏消息处理设置完成")

        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 设置状态栏消息处理失败: {e}")

    def _reset_status_message(self):
        """重置状态栏消息为默认状态"""
        try:
            if hasattr(self, 'footer') and hasattr(self.footer, 'navigation_label'):
                self.footer.navigation_label.setText("就绪")
                self.footer.navigation_label.setStyleSheet(
                    "color: #4CAF50; font-size: 12px; font-weight: bold;"
                )
        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 重置状态消息失败: {e}")

    def _on_error_occurred(self, error_info):
        """
        🔧 [P2-3] 错误发生时的处理
        """
        try:
            self.logger.info(f"🔧 [P2-3] 收到错误事件: {error_info.error_id}")

            # 可以在这里添加额外的错误处理逻辑
            # 例如：更新UI状态、记录用户操作等

        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 处理错误事件失败: {e}")

    def _on_error_recovered(self, error_id):
        """
        🔧 [P2-3] 错误恢复时的处理
        """
        try:
            self.logger.info(f"🔧 [P2-3] 错误已恢复: {error_id}")

            # 显示恢复成功消息
            if hasattr(self, 'footer') and hasattr(self.footer, 'navigation_label'):
                self.footer.navigation_label.setText("错误已自动修复")
                self.footer.navigation_label.setStyleSheet(
                    "color: #4CAF50; font-size: 12px; font-weight: bold;"
                )
                safe_single_shot(3000, lambda: self._reset_status_message())

        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 处理错误恢复失败: {e}")

    def _recover_data_error(self, error_info) -> bool:
        """
        🔧 [P2-3] 数据错误恢复策略
        """
        try:
            self.logger.info(f"🔧 [P2-3] 尝试恢复数据错误: {error_info.error_id}")

            # 尝试刷新数据
            if hasattr(self, 'main_workspace') and hasattr(self.main_workspace, 'refresh_current_data'):
                self.main_workspace.refresh_current_data()
                return True

            # 尝试重新加载表格
            if hasattr(self, 'navigation_panel') and hasattr(self.navigation_panel, 'refresh_navigation_data'):
                self.navigation_panel.refresh_navigation_data()
                return True

            return False

        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 数据错误恢复失败: {e}")
            return False

    def _recover_ui_error(self, error_info) -> bool:
        """
        🔧 [P2-3] UI错误恢复策略
        """
        try:
            self.logger.info(f"🔧 [P2-3] 尝试恢复UI错误: {error_info.error_id}")

            # 尝试刷新UI状态
            if hasattr(self, 'refresh_global_state'):
                self.refresh_global_state()
                return True

            # 尝试重新应用样式
            self.setStyleSheet(self.styleSheet())
            return True

        except Exception as e:
            self.logger.error(f"🔧 [P2-3] UI错误恢复失败: {e}")
            return False

    def _recover_system_error(self, error_info) -> bool:
        """
        🔧 [P2-3] 系统错误恢复策略
        """
        try:
            self.logger.info(f"🔧 [P2-3] 尝试恢复系统错误: {error_info.error_id}")

            # 尝试重新初始化关键组件
            if 'table_manager' in error_info.details:
                # 重新初始化表管理器
                return self._reinitialize_table_manager()

            if 'database' in error_info.details:
                # 重新连接数据库
                return self._reconnect_database()

            return False

        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 系统错误恢复失败: {e}")
            return False

    def _reinitialize_table_manager(self) -> bool:
        """重新初始化表管理器"""
        try:
            # 这里可以添加表管理器重新初始化逻辑
            self.logger.info("🔧 [P2-3] 表管理器重新初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 表管理器重新初始化失败: {e}")
            return False

    def _reconnect_database(self) -> bool:
        """重新连接数据库"""
        try:
            if hasattr(self, 'db_manager'):
                # 重新连接数据库
                self.db_manager.reconnect()
                self.logger.info("🔧 [P2-3] 数据库重新连接成功")
                return True
            return False
        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 数据库重新连接失败: {e}")
            return False

    # 🧹 [代码清理] _process_imported_data方法已移除
    # 🎯 [重构完成] 数据格式化已统一到统一格式管理器，此方法为冗余代码
    
