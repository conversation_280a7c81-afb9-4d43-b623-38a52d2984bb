#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表头点击样式损坏问题 - 调用关系预测分析器

使用调用关系预测分析方法，深度追踪表头点击事件的完整调用链路，
预测性发现所有可能的问题点和级联影响。
"""

import sys
from pathlib import Path
from typing import Dict, List, Tuple, Set, Any
from dataclasses import dataclass
from enum import Enum

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class RiskLevel(Enum):
    """风险等级"""
    CRITICAL = "🔥 严重"
    HIGH = "⚠️ 高风险" 
    MEDIUM = "🟡 中风险"
    LOW = "🟢 低风险"
    INFO = "📋 信息"

@dataclass
class CallNode:
    """调用节点"""
    name: str
    file_path: str
    line_number: int
    function_name: str
    risk_level: RiskLevel
    issues: List[str]
    parameters: Dict[str, Any]
    side_effects: List[str]

@dataclass
class CallChain:
    """调用链"""
    entry_point: str
    chain: List[CallNode]
    total_risk_score: int
    predicted_issues: List[str]
    cascade_effects: List[str]

class StyleCorruptionCallChainAnalyzer:
    """表头点击样式损坏调用关系预测分析器"""
    
    def __init__(self):
        self.call_chains: List[CallChain] = []
        self.risk_patterns: Dict[str, RiskLevel] = {}
        self.style_state_tracking: Dict[str, Any] = {}
        
        self._initialize_risk_patterns()
    
    def _initialize_risk_patterns(self):
        """初始化风险模式"""
        self.risk_patterns = {
            # 样式管理相关
            'apply_component_style': RiskLevel.HIGH,
            '_merge_styles': RiskLevel.CRITICAL,
            'setStyleSheet': RiskLevel.HIGH,
            'enable_hot_reload': RiskLevel.MEDIUM,
            '_on_style_file_changed': RiskLevel.HIGH,
            
            # 事件处理相关
            '_on_header_clicked': RiskLevel.MEDIUM,
            'blockSignals': RiskLevel.MEDIUM,
            'setSortIndicator': RiskLevel.LOW,
            
            # 渲染相关
            '_apply_table_style': RiskLevel.HIGH,
            'update': RiskLevel.LOW,
            'processEvents': RiskLevel.MEDIUM,
            
            # 状态管理相关
            '_clear_cache': RiskLevel.MEDIUM,
            'importlib.reload': RiskLevel.CRITICAL,
        }
    
    def analyze_header_click_chain(self) -> CallChain:
        """分析表头点击的完整调用链"""
        
        # 🎯 Step 1: 构建主要调用链路
        main_chain = self._build_main_call_chain()
        
        # 🎯 Step 2: 分析样式应用子链路
        style_chains = self._analyze_style_application_chains()
        
        # 🎯 Step 3: 分析热重载风险链路
        hotreload_chains = self._analyze_hotreload_risk_chains()
        
        # 🎯 Step 4: 分析信号处理链路
        signal_chains = self._analyze_signal_processing_chains()
        
        # 🎯 Step 5: 综合预测分析
        comprehensive_chain = self._synthesize_call_chains(
            main_chain, style_chains, hotreload_chains, signal_chains
        )
        
        return comprehensive_chain
    
    def _build_main_call_chain(self) -> List[CallNode]:
        """构建主要调用链路"""
        
        return [
            CallNode(
                name="用户点击表头",
                file_path="PyQt5.QtWidgets.QHeaderView",
                line_number=0,
                function_name="mousePressEvent",
                risk_level=RiskLevel.INFO,
                issues=[],
                parameters={"logical_index": "int"},
                side_effects=["触发sectionClicked信号"]
            ),
            
            CallNode(
                name="表头点击事件处理",
                file_path="src/gui/prototype/widgets/virtualized_expandable_table.py",
                line_number=7809,
                function_name="_on_header_clicked",
                risk_level=RiskLevel.MEDIUM,
                issues=[
                    "设置_processing_header_click标志可能影响后续事件",
                    "信号阻塞可能影响样式更新事件"
                ],
                parameters={
                    "logical_index": "int",
                    "_processing_header_click": "True",
                    "_is_sorting_in_progress": "True"
                },
                side_effects=[
                    "阻塞header和table信号",
                    "设置处理标志",
                    "调用排序管理器"
                ]
            ),
            
            CallNode(
                name="排序管理器处理",
                file_path="src/gui/prototype/widgets/column_sort_manager.py",
                line_number=128,
                function_name="handle_header_click",
                risk_level=RiskLevel.MEDIUM,
                issues=[
                    "排序状态变化可能触发UI重新渲染",
                    "多列排序逻辑复杂，可能有时序问题"
                ],
                parameters={
                    "logical_index": "int",
                    "column_name": "str"
                },
                side_effects=[
                    "更新排序状态",
                    "触发sort_state_changed信号",
                    "可能触发表格重新渲染"
                ]
            ),
            
            CallNode(
                name="排序状态变化处理",
                file_path="src/gui/prototype/widgets/virtualized_expandable_table.py",
                line_number=7161,
                function_name="_on_sort_state_changed",
                risk_level=RiskLevel.HIGH,
                issues=[
                    "🚨 关键风险点：可能触发数据重新加载",
                    "🚨 可能导致表格完全重新渲染",
                    "🚨 重新渲染会调用样式应用方法"
                ],
                parameters={
                    "sort_columns": "List[Dict]"
                },
                side_effects=[
                    "发布排序请求事件",
                    "可能触发数据重新加载",
                    "🔥 关键：触发表格重新渲染"
                ]
            ),
            
            CallNode(
                name="数据重新加载",
                file_path="src/services/table_data_service.py",
                line_number=147,
                function_name="_handle_sort_request",
                risk_level=RiskLevel.MEDIUM,
                issues=[
                    "数据请求可能导致UI状态变化",
                    "异步操作可能与样式应用产生时序冲突"
                ],
                parameters={
                    "table_name": "str",
                    "sort_columns": "List[Dict]"
                },
                side_effects=[
                    "执行数据库查询",
                    "发布数据更新事件",
                    "🔥 关键：触发UI数据更新"
                ]
            ),
            
            CallNode(
                name="数据更新事件处理",
                file_path="src/gui/prototype/prototype_main_window.py",
                line_number=3432,
                function_name="_on_new_data_updated",
                risk_level=RiskLevel.HIGH,
                issues=[
                    "🚨 严重风险：每次数据更新都会重新设置表格数据",
                    "🚨 set_data调用会触发样式重新应用",
                    "🚨 这是样式累积问题的核心触发点"
                ],
                parameters={
                    "table_name": "str",
                    "data": "DataFrame",
                    "operation_type": "str"
                },
                side_effects=[
                    "调用set_data方法",
                    "🔥 关键：触发样式重新应用",
                    "可能重复应用表头样式"
                ]
            ),
            
            CallNode(
                name="表格数据设置",
                file_path="src/gui/prototype/prototype_main_window.py",
                line_number=763,
                function_name="set_data",
                risk_level=RiskLevel.HIGH,
                issues=[
                    "🚨 严重风险：每次调用都会触发_apply_table_field_preference",
                    "🚨 会调用_apply_table_style重新应用样式",
                    "🚨 这是样式重复应用的直接原因"
                ],
                parameters={
                    "data": "DataFrame"
                },
                side_effects=[
                    "🔥 调用_apply_table_field_preference",
                    "🔥 最终调用_apply_table_style",
                    "🔥 重复应用组件样式"
                ]
            ),
            
            CallNode(
                name="🚨 样式应用 - 问题核心",
                file_path="src/gui/prototype/prototype_main_window.py", 
                line_number=5083,
                function_name="_apply_table_style",
                risk_level=RiskLevel.CRITICAL,
                issues=[
                    "🔥 CRITICAL: 每次调用都会重新应用表格和表头样式",
                    "🔥 CRITICAL: 调用StyleManager.apply_component_style",
                    "🔥 CRITICAL: 样式会与现有样式合并，导致累积"
                ],
                parameters={
                    "table_widget": "QTableWidget"
                },
                side_effects=[
                    "🔥 调用style_manager.apply_component_style(table)",
                    "🔥 调用style_manager.apply_component_style(header)",
                    "🔥 样式累积开始"
                ]
            ),
            
            CallNode(
                name="🔥 样式管理器 - 累积根源",
                file_path="src/gui/style_manager.py",
                line_number=143,
                function_name="apply_component_style",
                risk_level=RiskLevel.CRITICAL,
                issues=[
                    "🔥 CRITICAL: 获取component.styleSheet()当前样式",
                    "🔥 CRITICAL: 调用_merge_styles合并样式",
                    "🔥 CRITICAL: 每次调用都会累积样式内容"
                ],
                parameters={
                    "component": "QWidget",
                    "style_type": "str"
                },
                side_effects=[
                    "🔥 获取现有样式",
                    "🔥 与新样式合并",
                    "🔥 设置累积后的样式"
                ]
            ),
            
            CallNode(
                name="💥 样式合并 - 损坏源头",
                file_path="src/gui/style_manager.py",
                line_number=324,
                function_name="_merge_styles",
                risk_level=RiskLevel.CRITICAL,
                issues=[
                    "💥 CRITICAL: 简单字符串拼接 f'{base_style}\\n{custom_style}'",
                    "💥 CRITICAL: 无CSS规则去重机制",
                    "💥 CRITICAL: 无冲突检测和解决",
                    "💥 CRITICAL: 相同选择器规则会重复累积"
                ],
                parameters={
                    "base_style": "str",
                    "custom_style": "str"
                },
                side_effects=[
                    "💥 样式内容不断增长",
                    "💥 CSS规则冲突",
                    "💥 样式权重混乱",
                    "💥 UI渲染异常"
                ]
            )
        ]
    
    def _analyze_style_application_chains(self) -> List[CallNode]:
        """分析样式应用的子链路"""
        
        return [
            CallNode(
                name="样式缓存获取",
                file_path="src/gui/style_manager.py",
                line_number=156,
                function_name="_get_cached_style",
                risk_level=RiskLevel.MEDIUM,
                issues=[
                    "样式缓存可能与实际应用的样式不同步",
                    "缓存失效时机可能不当"
                ],
                parameters={"style_type": "str"},
                side_effects=["返回缓存的样式内容"]
            ),
            
            CallNode(
                name="样式编译",
                file_path="src/gui/style_manager.py",
                line_number=278,
                function_name="_compile_style",
                risk_level=RiskLevel.MEDIUM,
                issues=[
                    "响应式调整可能影响样式一致性",
                    "样式编译错误可能导致空样式"
                ],
                parameters={"style_key": "str"},
                side_effects=["编译动态样式参数"]
            ),
            
            CallNode(
                name="QWidget.setStyleSheet",
                file_path="PyQt5.QtWidgets.QWidget",
                line_number=0,
                function_name="setStyleSheet",
                risk_level=RiskLevel.HIGH,
                issues=[
                    "🚨 PyQt内部样式解析可能被损坏的CSS影响",
                    "🚨 样式设置可能触发widget重新渲染",
                    "🚨 重新渲染可能影响子组件样式"
                ],
                parameters={"styleSheet": "str"},
                side_effects=[
                    "PyQt内部样式解析",
                    "Widget重新绘制",
                    "可能影响子组件"
                ]
            )
        ]
    
    def _analyze_hotreload_risk_chains(self) -> List[CallNode]:
        """分析热重载风险链路"""
        
        return [
            CallNode(
                name="文件系统监控",
                file_path="src/gui/style_manager.py",
                line_number=418,
                function_name="_setup_file_watcher",
                risk_level=RiskLevel.MEDIUM,
                issues=[
                    "文件监控可能在用户操作过程中意外触发",
                    "QFileSystemWatcher可能产生误报"
                ],
                parameters={"style_file_path": "str"},
                side_effects=["建立文件监听", "可能误触发重载"]
            ),
            
            CallNode(
                name="样式文件变化检测",
                file_path="src/gui/style_manager.py",
                line_number=437,
                function_name="_on_style_file_changed",
                risk_level=RiskLevel.CRITICAL,
                issues=[
                    "🔥 CRITICAL: 清除所有样式缓存",
                    "🔥 CRITICAL: 重新导入模块会重置样式组件",
                    "🔥 CRITICAL: 已应用的组件样式不会自动重新应用",
                    "🚨 可能在表头操作过程中触发"
                ],
                parameters={"path": "str"},
                side_effects=[
                    "🔥 _clear_cache()",
                    "🔥 importlib.reload(modern_style)",
                    "🔥 重新初始化样式组件",
                    "💥 已应用样式失效"
                ]
            ),
            
            CallNode(
                name="模块重载",
                file_path="importlib",
                line_number=0,
                function_name="reload",
                risk_level=RiskLevel.CRITICAL,
                issues=[
                    "💥 CRITICAL: 完全重置模块状态",
                    "💥 CRITICAL: 所有类实例和变量被重新创建",
                    "💥 CRITICAL: 与现有组件的引用关系可能断裂"
                ],
                parameters={"module": "module"},
                side_effects=[
                    "💥 模块完全重置",
                    "💥 样式定义重新加载",
                    "💥 可能导致样式引用失效"
                ]
            )
        ]
    
    def _analyze_signal_processing_chains(self) -> List[CallNode]:
        """分析信号处理链路"""
        
        return [
            CallNode(
                name="信号阻塞",
                file_path="src/gui/prototype/widgets/virtualized_expandable_table.py",
                line_number=8018,
                function_name="blockSignals",
                risk_level=RiskLevel.MEDIUM,
                issues=[
                    "信号阻塞可能阻止样式相关的更新事件",
                    "阻塞期间的样式变化可能不会及时反映"
                ],
                parameters={"block": "bool"},
                side_effects=["阻塞所有Qt信号"]
            ),
            
            CallNode(
                name="事件处理循环",
                file_path="PyQt5.QtCore.QApplication",
                line_number=0,
                function_name="processEvents",
                risk_level=RiskLevel.MEDIUM,
                issues=[
                    "强制事件处理可能在不当时机触发样式更新",
                    "可能与信号阻塞产生时序冲突"
                ],
                parameters={},
                side_effects=["处理待处理的Qt事件"]
            )
        ]
    
    def _synthesize_call_chains(self, main_chain: List[CallNode], 
                               style_chains: List[CallNode],
                               hotreload_chains: List[CallNode],
                               signal_chains: List[CallNode]) -> CallChain:
        """综合分析所有调用链"""
        
        # 合并所有节点
        all_nodes = main_chain + style_chains + hotreload_chains + signal_chains
        
        # 计算总风险分数
        risk_scores = {
            RiskLevel.CRITICAL: 10,
            RiskLevel.HIGH: 7,
            RiskLevel.MEDIUM: 4,
            RiskLevel.LOW: 2,
            RiskLevel.INFO: 1
        }
        
        total_risk = sum(risk_scores[node.risk_level] for node in all_nodes)
        
        # 预测问题
        predicted_issues = [
            "💥 表头每次点击都会触发完整的数据更新和样式重新应用循环",
            "🔥 StyleManager._merge_styles使用简单字符串拼接，导致CSS规则无限累积",
            "⚠️ 样式热重载可能在操作过程中意外触发，重置样式状态",
            "🚨 信号阻塞可能与样式更新事件产生时序冲突",
            "💥 累积的CSS样式会导致选择器冲突和权重混乱",
            "🔥 PyQt的样式解析器在处理损坏CSS时可能产生不可预知的渲染结果"
        ]
        
        # 预测级联效应
        cascade_effects = [
            "🔗 第1次点击：正常样式 + 新样式 = 轻微累积",
            "🔗 第3-5次点击：样式内容开始显著增长，CSS解析变慢",
            "🔗 第10-15次点击：重复的CSS规则开始冲突，样式权重混乱",
            "🔗 第20+次点击：CSS文本过大，解析错误，样式系统崩溃",
            "💥 最终结果：样式降级到默认/旧版样式，且无法恢复",
            "🔄 恶性循环：用户尝试更多操作希望恢复，反而加剧问题"
        ]
        
        return CallChain(
            entry_point="用户点击表头",
            chain=all_nodes,
            total_risk_score=total_risk,
            predicted_issues=predicted_issues,
            cascade_effects=cascade_effects
        )
    
    def predict_additional_risks(self, chain: CallChain) -> List[str]:
        """预测额外风险"""
        
        additional_risks = [
            "🔥 内存泄漏风险：累积的样式字符串可能导致内存使用持续增长",
            "⚡ 性能下降：大量CSS规则会显著影响Qt样式解析和渲染性能",
            "🎭 用户困惑：样式突然变化会让用户以为软件出现了严重bug",
            "🔄 操作放大：用户可能因为样式问题尝试更多操作，进一步加剧问题",
            "🛠️ 维护困难：样式损坏问题难以复现和调试，增加维护成本",
            "📱 兼容性：不同Qt版本或系统主题对损坏CSS的处理可能不同",
            "🔐 状态污染：样式管理器的全局状态可能影响其他组件",
            "⏰ 时序敏感：问题的发生可能与用户操作的频率和时机相关"
        ]
        
        return additional_risks
    
    def generate_fix_priority_matrix(self, chain: CallChain) -> Dict[str, Any]:
        """生成修复优先级矩阵"""
        
        return {
            "P0_立即修复": {
                "target": "_merge_styles方法",
                "fix": "实现智能CSS规则合并，避免重复累积",
                "impact": "直接解决样式损坏根本原因",
                "effort": "中等",
                "risk": "低"
            },
            
            "P1_本周修复": {
                "target": "apply_component_style方法",
                "fix": "添加样式应用防重复机制",
                "impact": "防止样式重复应用",
                "effort": "中等",
                "risk": "低"
            },
            
            "P2_下周修复": {
                "target": "热重载机制",
                "fix": "生产环境禁用，开发环境优化触发条件",
                "impact": "消除意外样式重置风险",
                "effort": "低",
                "risk": "极低"
            },
            
            "P3_后续优化": {
                "target": "样式状态管理",
                "fix": "实现完整的样式状态跟踪和恢复机制",
                "impact": "提供样式损坏自愈能力",
                "effort": "高",
                "risk": "中等"
            }
        }
    
    def run_analysis(self):
        """运行完整的调用关系预测分析"""
        
        print("🔍 开始表头点击样式损坏问题的调用关系预测分析...")
        print("=" * 80)
        
        # 分析调用链
        chain = self.analyze_header_click_chain()
        
        # 预测额外风险
        additional_risks = self.predict_additional_risks(chain)
        
        # 生成修复矩阵
        fix_matrix = self.generate_fix_priority_matrix(chain)
        
        # 输出分析结果
        self._print_analysis_results(chain, additional_risks, fix_matrix)
    
    def _print_analysis_results(self, chain: CallChain, additional_risks: List[str], fix_matrix: Dict[str, Any]):
        """输出分析结果"""
        
        print(f"\n🎯 调用链入口点: {chain.entry_point}")
        print(f"📊 总风险分数: {chain.total_risk_score}/100")
        print(f"🔗 调用节点数量: {len(chain.chain)}")
        
        print("\n🔥 关键风险节点:")
        critical_nodes = [node for node in chain.chain if node.risk_level == RiskLevel.CRITICAL]
        for i, node in enumerate(critical_nodes, 1):
            print(f"{i}. {node.name} ({node.file_path}:{node.line_number})")
            for issue in node.issues:
                print(f"   • {issue}")
        
        print("\n💥 预测问题:")
        for i, issue in enumerate(chain.predicted_issues, 1):
            print(f"{i}. {issue}")
        
        print("\n🔗 级联效应预测:")
        for i, effect in enumerate(chain.cascade_effects, 1):
            print(f"{i}. {effect}")
        
        print("\n⚠️ 额外风险评估:")
        for i, risk in enumerate(additional_risks, 1):
            print(f"{i}. {risk}")
        
        print("\n🛠️ 修复优先级矩阵:")
        for priority, details in fix_matrix.items():
            print(f"\n{priority}:")
            print(f"  目标: {details['target']}")
            print(f"  修复: {details['fix']}")
            print(f"  影响: {details['impact']}")
            print(f"  工作量: {details['effort']}")
            print(f"  风险: {details['risk']}")

if __name__ == "__main__":
    analyzer = StyleCorruptionCallChainAnalyzer()
    analyzer.run_analysis()