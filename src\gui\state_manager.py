"""
状态管理系统模块

提供完整的应用程序状态保存和恢复功能，包括窗口状态、用户偏好、数据状态和会话管理。

主要组件:
- StateManager: 主状态管理器
- WindowStateManager: 窗口状态管理
- UserStateManager: 用户操作状态管理
- DataStateManager: 数据状态管理
- SessionManager: 会话状态管理
"""

import json
import os
import pickle
import time
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
from pathlib import Path

from PyQt5.QtWidgets import (
    QWidget, QMainWindow, QSplitter, QTableWidget, QTreeWidget,
    QHeaderView, QApplication
)
from PyQt5.QtCore import (
    Qt, QObject, pyqtSignal, QTimer, QSettings, QByteArray,
    QPoint, QSize, QRect
)
from src.utils.thread_safe_timer import safe_single_shot
from PyQt5.QtGui import QFont

from src.utils.log_config import get_module_logger

logger = get_module_logger(__name__)


class StateManager(QObject):
    """主状态管理器"""
    
    # 信号定义
    state_saved = pyqtSignal(str)  # 状态保存完成信号
    state_loaded = pyqtSignal(str)  # 状态加载完成信号
    state_error = pyqtSignal(str, str)  # 状态错误信号
    
    def __init__(self, app_name: str = "SalaryChangesSystem", parent=None):
        super().__init__(parent)
        
        self.app_name = app_name
        self.state_dir = Path("state")
        self.state_dir.mkdir(exist_ok=True)
        
        # 子管理器
        self.window_manager = WindowStateManager(self)
        self.user_manager = UserStateManager(self)
        self.data_manager = DataStateManager(self)
        self.session_manager = SessionManager(self)
        
        # 自动保存定时器
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save_state)
        self.auto_save_interval = 300000  # 5分钟
        
        # 状态缓存
        self.state_cache = {}
        
        # 数据变化标记 - 用于触发即时保存
        self.data_changed = False
        self.last_change_time = time.time()
        
        # 初始化
        self.init_state_system()
        
    def init_state_system(self):
        """初始化状态系统"""
        try:
            # 创建状态目录结构
            for subdir in ['windows', 'user', 'data', 'sessions', 'backups']:
                (self.state_dir / subdir).mkdir(exist_ok=True)
            
            # 启动自动保存
            self.start_auto_save()
            
            logger.info("状态管理系统初始化完成")
            
        except Exception as e:
            logger.error(f"初始化状态系统失败: {e}")
            self.state_error.emit("init", str(e))
    
    def start_auto_save(self):
        """启动自动保存"""
        try:
            self.auto_save_timer.start(self.auto_save_interval)
            logger.info(f"自动保存已启动，间隔: {self.auto_save_interval/1000}秒")
            
        except Exception as e:
            logger.error(f"启动自动保存失败: {e}")
    
    def stop_auto_save(self):
        """停止自动保存"""
        try:
            self.auto_save_timer.stop()
            logger.info("自动保存已停止")
            
        except Exception as e:
            logger.error(f"停止自动保存失败: {e}")
    
    def mark_data_changed(self):
        """标记数据已变化，触发即时保存"""
        self.data_changed = True
        self.last_change_time = time.time()
        
        # 如果变化频繁（1秒内），等待一段时间再保存（线程安全延迟）
        safe_single_shot(1000, self._save_if_changed)
    
    def _save_if_changed(self):
        """如果数据已变化则保存"""
        if self.data_changed and (time.time() - self.last_change_time >= 1.0):
            self.auto_save_state()
            self.data_changed = False
    
    def auto_save_state(self):
        """自动保存状态"""
        try:
            self.save_all_state(auto_save=True)
            logger.debug("自动保存状态完成")
            
        except Exception as e:
            logger.error(f"自动保存状态失败: {e}")
    
    def save_all_state(self, auto_save: bool = False):
        """保存所有状态"""
        try:
            saved_components = []
            
            # 保存窗口状态
            if self.window_manager.save_state():
                saved_components.append("windows")
            
            # 保存用户状态
            if self.user_manager.save_state():
                saved_components.append("user")
            
            # 保存数据状态
            if self.data_manager.save_state():
                saved_components.append("data")
            
            # 保存会话状态
            if self.session_manager.save_state():
                saved_components.append("sessions")
            
            # 创建状态快照
            if not auto_save:
                self.create_state_snapshot()
            
            self.state_saved.emit(f"保存了 {len(saved_components)} 个组件状态")
            logger.info(f"保存状态完成: {', '.join(saved_components)}")
            
        except Exception as e:
            logger.error(f"保存所有状态失败: {e}")
            self.state_error.emit("save_all", str(e))
    
    def load_all_state(self):
        """加载所有状态"""
        try:
            loaded_components = []
            
            # 加载窗口状态
            if self.window_manager.load_state():
                loaded_components.append("windows")
            
            # 加载用户状态
            if self.user_manager.load_state():
                loaded_components.append("user")
            
            # 加载数据状态
            if self.data_manager.load_state():
                loaded_components.append("data")
            
            # 加载会话状态
            if self.session_manager.load_state():
                loaded_components.append("sessions")
            
            self.state_loaded.emit(f"加载了 {len(loaded_components)} 个组件状态")
            logger.info(f"加载状态完成: {', '.join(loaded_components)}")
            
        except Exception as e:
            logger.error(f"加载所有状态失败: {e}")
            self.state_error.emit("load_all", str(e))
    
    def create_state_snapshot(self):
        """创建状态快照"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            snapshot_dir = self.state_dir / "backups" / timestamp
            snapshot_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制当前状态文件
            import shutil
            for subdir in ['windows', 'user', 'data', 'sessions']:
                src_dir = self.state_dir / subdir
                dst_dir = snapshot_dir / subdir
                if src_dir.exists():
                    shutil.copytree(src_dir, dst_dir, dirs_exist_ok=True)
            
            # 清理旧快照（保留最近10个）
            self.cleanup_old_snapshots()
            
            logger.info(f"状态快照已创建: {timestamp}")
            
        except Exception as e:
            logger.error(f"创建状态快照失败: {e}")
    
    def cleanup_old_snapshots(self, keep_count: int = 10):
        """清理旧快照"""
        try:
            backup_dir = self.state_dir / "backups"
            if not backup_dir.exists():
                return
            
            # 获取所有快照目录
            snapshots = [d for d in backup_dir.iterdir() if d.is_dir()]
            snapshots.sort(key=lambda x: x.name, reverse=True)
            
            # 删除多余的快照
            for snapshot in snapshots[keep_count:]:
                import shutil
                shutil.rmtree(snapshot)
                logger.debug(f"删除旧快照: {snapshot.name}")
            
        except Exception as e:
            logger.error(f"清理旧快照失败: {e}")
    
    def reset_all_state(self):
        """重置所有状态"""
        try:
            # 重置各个管理器
            self.window_manager.reset_state()
            self.user_manager.reset_state()
            self.data_manager.reset_state()
            self.session_manager.reset_state()
            
            logger.info("所有状态已重置")
            
        except Exception as e:
            logger.error(f"重置所有状态失败: {e}")
            self.state_error.emit("reset_all", str(e))


class WindowStateManager(QObject):
    """窗口状态管理器"""
    
    def __init__(self, state_manager: StateManager):
        super().__init__(state_manager)
        self.state_manager = state_manager
        self.state_file = state_manager.state_dir / "windows" / "window_state.json"
        self.registered_windows = {}
        
    def register_window(self, window_id: str, window: QWidget):
        """注册窗口"""
        try:
            self.registered_windows[window_id] = window
            logger.debug(f"注册窗口: {window_id}")
            
        except Exception as e:
            logger.error(f"注册窗口失败 {window_id}: {e}")
    
    def unregister_window(self, window_id: str):
        """注销窗口"""
        try:
            if window_id in self.registered_windows:
                del self.registered_windows[window_id]
                logger.debug(f"注销窗口: {window_id}")
                
        except Exception as e:
            logger.error(f"注销窗口失败 {window_id}: {e}")
    
    def save_state(self) -> bool:
        """保存窗口状态"""
        try:
            window_states = {}
            
            for window_id, window in self.registered_windows.items():
                if window and not window.isHidden():
                    state = self.get_window_state(window)
                    if state:
                        window_states[window_id] = state
            
            # 保存到文件
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(window_states, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"保存了 {len(window_states)} 个窗口状态")
            return True
            
        except Exception as e:
            logger.error(f"保存窗口状态失败: {e}")
            return False
    
    def load_state(self) -> bool:
        """加载窗口状态"""
        try:
            if not self.state_file.exists():
                return False
            
            with open(self.state_file, 'r', encoding='utf-8') as f:
                window_states = json.load(f)
            
            # 应用窗口状态
            applied_count = 0
            for window_id, state in window_states.items():
                if window_id in self.registered_windows:
                    window = self.registered_windows[window_id]
                    if window and self.apply_window_state(window, state):
                        applied_count += 1
            
            logger.debug(f"应用了 {applied_count} 个窗口状态")
            return applied_count > 0
            
        except Exception as e:
            logger.error(f"加载窗口状态失败: {e}")
            return False
    
    def get_window_state(self, window: QWidget) -> Optional[Dict]:
        """获取窗口状态"""
        try:
            state = {
                'geometry': {
                    'x': window.x(),
                    'y': window.y(),
                    'width': window.width(),
                    'height': window.height()
                },
                'window_state': int(window.windowState()),
                'visible': window.isVisible()
            }
            
            # 主窗口特殊处理
            if isinstance(window, QMainWindow):
                state['main_window'] = {
                    'toolbar_visible': window.toolBar() is not None and window.toolBar().isVisible() if hasattr(window, 'toolBar') else True,
                    'statusbar_visible': window.statusBar() is not None and window.statusBar().isVisible() if hasattr(window, 'statusBar') else True
                }
            
            # 分割器状态
            splitters = self.find_splitters(window)
            if splitters:
                state['splitters'] = {}
                for i, splitter in enumerate(splitters):
                    state['splitters'][f'splitter_{i}'] = {
                        'sizes': splitter.sizes(),
                        'orientation': int(splitter.orientation())
                    }
            
            # 表格状态
            tables = self.find_tables(window)
            if tables:
                state['tables'] = {}
                for i, table in enumerate(tables):
                    table_state = self.get_table_state(table)
                    if table_state:
                        state['tables'][f'table_{i}'] = table_state
            
            return state
            
        except Exception as e:
            logger.error(f"获取窗口状态失败: {e}")
            return None
    
    def apply_window_state(self, window: QWidget, state: Dict) -> bool:
        """应用窗口状态"""
        try:
            # 应用几何状态
            if 'geometry' in state:
                geom = state['geometry']
                window.setGeometry(geom['x'], geom['y'], geom['width'], geom['height'])
            
            # 应用窗口状态
            if 'window_state' in state:
                window.setWindowState(Qt.WindowState(state['window_state']))
            
            # 应用可见性
            if 'visible' in state and state['visible']:
                window.show()
            
            # 应用主窗口状态
            if 'main_window' in state and isinstance(window, QMainWindow):
                main_state = state['main_window']
                if 'toolbar_visible' in main_state and hasattr(window, 'toolBar'):
                    toolbar = window.toolBar()
                    if toolbar:
                        toolbar.setVisible(main_state['toolbar_visible'])
                
                if 'statusbar_visible' in main_state and hasattr(window, 'statusBar'):
                    statusbar = window.statusBar()
                    if statusbar:
                        statusbar.setVisible(main_state['statusbar_visible'])
            
            # 应用分割器状态
            if 'splitters' in state:
                splitters = self.find_splitters(window)
                for i, splitter in enumerate(splitters):
                    splitter_key = f'splitter_{i}'
                    if splitter_key in state['splitters']:
                        splitter_state = state['splitters'][splitter_key]
                        if 'sizes' in splitter_state:
                            splitter.setSizes(splitter_state['sizes'])
            
            # 应用表格状态
            if 'tables' in state:
                tables = self.find_tables(window)
                for i, table in enumerate(tables):
                    table_key = f'table_{i}'
                    if table_key in state['tables']:
                        self.apply_table_state(table, state['tables'][table_key])
            
            return True
            
        except Exception as e:
            logger.error(f"应用窗口状态失败: {e}")
            return False
    
    def find_splitters(self, widget: QWidget) -> List[QSplitter]:
        """查找分割器"""
        splitters = []
        try:
            if isinstance(widget, QSplitter):
                splitters.append(widget)
            
            for child in widget.findChildren(QSplitter):
                splitters.append(child)
                
        except Exception as e:
            logger.error(f"查找分割器失败: {e}")
        
        return splitters
    
    def find_tables(self, widget: QWidget) -> List[QTableWidget]:
        """查找表格"""
        tables = []
        try:
            if isinstance(widget, QTableWidget):
                tables.append(widget)
            
            for child in widget.findChildren(QTableWidget):
                tables.append(child)
                
        except Exception as e:
            logger.error(f"查找表格失败: {e}")
        
        return tables
    
    def get_table_state(self, table: QTableWidget) -> Optional[Dict]:
        """获取表格状态"""
        try:
            state = {
                'column_widths': [],
                'column_order': [],
                'sort_column': -1,
                'sort_order': 0,
                'selection': []
            }
            
            # 列宽
            header = table.horizontalHeader()
            for i in range(table.columnCount()):
                state['column_widths'].append(header.sectionSize(i))
            
            # 列顺序
            for i in range(table.columnCount()):
                state['column_order'].append(header.logicalIndex(i))
            
            # 排序状态
            state['sort_column'] = header.sortIndicatorSection()
            state['sort_order'] = int(header.sortIndicatorOrder())
            
            # 选中状态
            selected_items = table.selectedItems()
            for item in selected_items:
                state['selection'].append({
                    'row': item.row(),
                    'column': item.column()
                })
            
            return state
            
        except Exception as e:
            logger.error(f"获取表格状态失败: {e}")
            return None
    
    def apply_table_state(self, table: QTableWidget, state: Dict) -> bool:
        """应用表格状态"""
        try:
            header = table.horizontalHeader()
            
            # 应用列宽
            if 'column_widths' in state:
                for i, width in enumerate(state['column_widths']):
                    if i < table.columnCount():
                        header.resizeSection(i, width)
            
            # 应用排序状态
            if 'sort_column' in state and 'sort_order' in state:
                if state['sort_column'] >= 0:
                    table.sortItems(state['sort_column'], Qt.SortOrder(state['sort_order']))
            
            # 应用选中状态
            if 'selection' in state:
                table.clearSelection()
                for sel in state['selection']:
                    if (sel['row'] < table.rowCount() and 
                        sel['column'] < table.columnCount()):
                        item = table.item(sel['row'], sel['column'])
                        if item:
                            item.setSelected(True)
            
            return True
            
        except Exception as e:
            logger.error(f"应用表格状态失败: {e}")
            return False
    
    def save_table_state_by_name(self, table_name: str, table: QTableWidget) -> bool:
        """🔧 [P0-3修复] 根据表名保存表格状态"""
        try:
            if not table_name:
                return False
                
            # 获取当前表格状态
            state = self.get_table_state(table)
            if not state:
                return False
                
            # 保存到内存缓存
            if not hasattr(self, '_table_state_cache'):
                self._table_state_cache = {}
                
            self._table_state_cache[table_name] = state
            
            logger.info(f"🔧 [P0-3修复] 已保存表格状态: {table_name}, 列宽: {len(state.get('column_widths', []))}个")
            return True
            
        except Exception as e:
            logger.error(f"🔧 [P0-3修复] 保存表格状态失败 {table_name}: {e}")
            return False
    
    def restore_table_state_by_name(self, table_name: str, table: QTableWidget) -> bool:
        """🔧 [P0-3修复] 根据表名恢复表格状态"""
        try:
            if not table_name:
                return False
                
            # 检查缓存
            if not hasattr(self, '_table_state_cache') or table_name not in self._table_state_cache:
                logger.debug(f"🔧 [P0-3修复] 没有找到缓存的状态: {table_name}")
                return False
                
            state = self._table_state_cache[table_name]
            result = self.apply_table_state(table, state)
            
            if result:
                logger.info(f"🔧 [P0-3修复] 已恢复表格状态: {table_name}")
            
            return result
            
        except Exception as e:
            logger.error(f"🔧 [P0-3修复] 恢复表格状态失败 {table_name}: {e}")
            return False
    
    def get_saved_state(self, table_name: str) -> Optional[Dict]:
        """🔧 [P0-3修复] 获取保存的表格状态"""
        try:
            if not hasattr(self, '_table_state_cache'):
                return None
                
            return self._table_state_cache.get(table_name)
            
        except Exception as e:
            logger.error(f"🔧 [P0-3修复] 获取保存状态失败 {table_name}: {e}")
            return None
    
    def reset_state(self):
        """重置窗口状态"""
        try:
            if self.state_file.exists():
                self.state_file.unlink()
            logger.info("窗口状态已重置")
            
        except Exception as e:
            logger.error(f"重置窗口状态失败: {e}")


class UserStateManager(QObject):
    """用户状态管理器"""
    
    def __init__(self, state_manager: StateManager):
        super().__init__(state_manager)
        self.state_manager = state_manager
        self.state_file = state_manager.state_dir / "user" / "user_state.json"
        self.user_preferences = {}
        self.recent_actions = []
        self.max_recent_actions = 100
        
    def save_state(self) -> bool:
        """保存用户状态"""
        try:
            user_state = {
                'preferences': self.user_preferences,
                'recent_actions': self.recent_actions[-self.max_recent_actions:],
                'last_save_time': datetime.now().isoformat()
            }
            
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(user_state, f, indent=2, ensure_ascii=False)
            
            logger.debug("用户状态保存完成")
            return True
            
        except Exception as e:
            logger.error(f"保存用户状态失败: {e}")
            return False
    
    def load_state(self) -> bool:
        """加载用户状态"""
        try:
            if not self.state_file.exists():
                return False
            
            with open(self.state_file, 'r', encoding='utf-8') as f:
                user_state = json.load(f)
            
            self.user_preferences = user_state.get('preferences', {})
            self.recent_actions = user_state.get('recent_actions', [])
            
            logger.debug("用户状态加载完成")
            return True
            
        except Exception as e:
            logger.error(f"加载用户状态失败: {e}")
            return False
    
    def set_preference(self, key: str, value: Any):
        """设置用户偏好"""
        try:
            self.user_preferences[key] = value
            logger.debug(f"设置用户偏好: {key} = {value}")
            
        except Exception as e:
            logger.error(f"设置用户偏好失败 {key}: {e}")
    
    def get_preference(self, key: str, default: Any = None) -> Any:
        """获取用户偏好"""
        try:
            return self.user_preferences.get(key, default)
            
        except Exception as e:
            logger.error(f"获取用户偏好失败 {key}: {e}")
            return default
    
    def add_recent_action(self, action: Dict):
        """添加最近操作"""
        try:
            action['timestamp'] = datetime.now().isoformat()
            self.recent_actions.append(action)
            
            # 限制数量
            if len(self.recent_actions) > self.max_recent_actions:
                self.recent_actions = self.recent_actions[-self.max_recent_actions:]
            
            logger.debug(f"添加最近操作: {action.get('type', 'unknown')}")
            
        except Exception as e:
            logger.error(f"添加最近操作失败: {e}")
    
    def get_recent_actions(self, count: int = 10) -> List[Dict]:
        """获取最近操作"""
        try:
            return self.recent_actions[-count:] if self.recent_actions else []
            
        except Exception as e:
            logger.error(f"获取最近操作失败: {e}")
            return []
    
    def reset_state(self):
        """重置用户状态"""
        try:
            self.user_preferences.clear()
            self.recent_actions.clear()
            if self.state_file.exists():
                self.state_file.unlink()
            logger.info("用户状态已重置")
            
        except Exception as e:
            logger.error(f"重置用户状态失败: {e}")


class DataStateManager(QObject):
    """数据状态管理器"""
    
    def __init__(self, state_manager: StateManager):
        super().__init__(state_manager)
        self.state_manager = state_manager
        self.state_file = state_manager.state_dir / "data" / "data_state.json"
        self.temp_data = {}
        self.data_cache = {}
        
    def save_state(self) -> bool:
        """保存数据状态"""
        try:
            data_state = {
                'temp_data': self.temp_data,
                'cache_info': {
                    'cache_size': len(self.data_cache),
                    'cache_keys': list(self.data_cache.keys())
                },
                'last_save_time': datetime.now().isoformat()
            }
            
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(data_state, f, indent=2, ensure_ascii=False)
            
            # 保存缓存数据到单独文件
            cache_file = self.state_file.parent / "data_cache.pkl"
            with open(cache_file, 'wb') as f:
                pickle.dump(self.data_cache, f)
            
            logger.debug("数据状态保存完成")
            return True
            
        except Exception as e:
            logger.error(f"保存数据状态失败: {e}")
            return False
    
    def load_state(self) -> bool:
        """加载数据状态"""
        try:
            if not self.state_file.exists():
                return False
            
            with open(self.state_file, 'r', encoding='utf-8') as f:
                data_state = json.load(f)
            
            self.temp_data = data_state.get('temp_data', {})
            
            # 加载缓存数据
            cache_file = self.state_file.parent / "data_cache.pkl"
            if cache_file.exists():
                try:
                    with open(cache_file, 'rb') as f:
                        self.data_cache = pickle.load(f)
                except Exception as e:
                    logger.warning(f"加载数据缓存失败: {e}")
                    self.data_cache = {}
            
            logger.debug("数据状态加载完成")
            return True
            
        except Exception as e:
            logger.error(f"加载数据状态失败: {e}")
            return False
    
    def set_temp_data(self, key: str, data: Any):
        """设置临时数据"""
        try:
            self.temp_data[key] = {
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            logger.debug(f"设置临时数据: {key}")
            
        except Exception as e:
            logger.error(f"设置临时数据失败 {key}: {e}")
    
    def get_temp_data(self, key: str, default: Any = None) -> Any:
        """获取临时数据"""
        try:
            temp_item = self.temp_data.get(key)
            if temp_item:
                return temp_item.get('data', default)
            return default
            
        except Exception as e:
            logger.error(f"获取临时数据失败 {key}: {e}")
            return default
    
    def cache_data(self, key: str, data: Any):
        """缓存数据"""
        try:
            self.data_cache[key] = {
                'data': data,
                'timestamp': time.time(),
                'access_count': 0
            }
            logger.debug(f"缓存数据: {key}")
            
        except Exception as e:
            logger.error(f"缓存数据失败 {key}: {e}")
    
    def get_cached_data(self, key: str, default: Any = None) -> Any:
        """获取缓存数据"""
        try:
            if key in self.data_cache:
                cache_item = self.data_cache[key]
                cache_item['access_count'] += 1
                return cache_item.get('data', default)
            return default
            
        except Exception as e:
            logger.error(f"获取缓存数据失败 {key}: {e}")
            return default
    
    def clear_expired_cache(self, max_age_hours: int = 24):
        """清理过期缓存"""
        try:
            current_time = time.time()
            expired_keys = []
            
            for key, cache_item in self.data_cache.items():
                age_hours = (current_time - cache_item['timestamp']) / 3600
                if age_hours > max_age_hours:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.data_cache[key]
            
            if expired_keys:
                logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")
            
        except Exception as e:
            logger.error(f"清理过期缓存失败: {e}")
    
    def reset_state(self):
        """重置数据状态"""
        try:
            self.temp_data.clear()
            self.data_cache.clear()
            
            if self.state_file.exists():
                self.state_file.unlink()
            
            cache_file = self.state_file.parent / "data_cache.pkl"
            if cache_file.exists():
                cache_file.unlink()
            
            logger.info("数据状态已重置")
            
        except Exception as e:
            logger.error(f"重置数据状态失败: {e}")


class SessionManager(QObject):
    """会话管理器"""
    
    def __init__(self, state_manager: StateManager):
        super().__init__(state_manager)
        self.state_manager = state_manager
        self.state_file = state_manager.state_dir / "sessions" / "session_state.json"
        self.session_id = self.generate_session_id()
        self.session_start_time = datetime.now()
        self.session_data = {}
        
    def generate_session_id(self) -> str:
        """生成会话ID"""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def save_state(self) -> bool:
        """保存会话状态"""
        try:
            session_state = {
                'session_id': self.session_id,
                'start_time': self.session_start_time.isoformat(),
                'end_time': datetime.now().isoformat(),
                'duration_seconds': (datetime.now() - self.session_start_time).total_seconds(),
                'session_data': self.session_data
            }
            
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(session_state, f, indent=2, ensure_ascii=False)
            
            # 保存会话历史
            self.save_session_history(session_state)
            
            logger.debug(f"会话状态保存完成: {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存会话状态失败: {e}")
            return False
    
    def load_state(self) -> bool:
        """加载会话状态"""
        try:
            if not self.state_file.exists():
                return False
            
            with open(self.state_file, 'r', encoding='utf-8') as f:
                session_state = json.load(f)
            
            # 恢复会话数据（不恢复会话ID和时间，保持新会话）
            self.session_data = session_state.get('session_data', {})
            
            logger.debug("会话状态加载完成")
            return True
            
        except Exception as e:
            logger.error(f"加载会话状态失败: {e}")
            return False
    
    def save_session_history(self, session_state: Dict):
        """保存会话历史"""
        try:
            history_file = self.state_file.parent / "session_history.json"
            
            # 加载现有历史
            history = []
            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            # 添加当前会话
            history.append(session_state)
            
            # 限制历史数量（保留最近50个会话）
            if len(history) > 50:
                history = history[-50:]
            
            # 保存历史
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, indent=2, ensure_ascii=False)
            
            logger.debug("会话历史保存完成")
            
        except Exception as e:
            logger.error(f"保存会话历史失败: {e}")
    
    def set_session_data(self, key: str, value: Any):
        """设置会话数据"""
        try:
            self.session_data[key] = value
            logger.debug(f"设置会话数据: {key}")
            
        except Exception as e:
            logger.error(f"设置会话数据失败 {key}: {e}")
    
    def get_session_data(self, key: str, default: Any = None) -> Any:
        """获取会话数据"""
        try:
            return self.session_data.get(key, default)
            
        except Exception as e:
            logger.error(f"获取会话数据失败 {key}: {e}")
            return default
    
    def get_session_info(self) -> Dict:
        """获取会话信息"""
        try:
            return {
                'session_id': self.session_id,
                'start_time': self.session_start_time.isoformat(),
                'duration_seconds': (datetime.now() - self.session_start_time).total_seconds(),
                'data_keys': list(self.session_data.keys())
            }
            
        except Exception as e:
            logger.error(f"获取会话信息失败: {e}")
            return {}
    
    def reset_state(self):
        """重置会话状态"""
        try:
            self.session_data.clear()
            if self.state_file.exists():
                self.state_file.unlink()
            logger.info("会话状态已重置")
            
        except Exception as e:
            logger.error(f"重置会话状态失败: {e}")


# 全局状态管理器实例
_global_state_manager = None

def get_state_manager() -> StateManager:
    """获取全局状态管理器"""
    global _global_state_manager
    if _global_state_manager is None:
        _global_state_manager = StateManager()
    return _global_state_manager

def initialize_state_manager(app_name: str = "SalaryChangesSystem") -> StateManager:
    """初始化全局状态管理器"""
    global _global_state_manager
    _global_state_manager = StateManager(app_name)
    return _global_state_manager 