### 日志治理使用说明与测试指引

本文档说明本项目的日志治理能力与使用方式，并提供最小可行的测试指引。

#### 一、总体目标
- 区分开发与生产环境日志输出强度，避免控制台 I/O 造成卡顿。
- 降噪：对高频 INFO/DEBUG 日志进行节流/采样/去重/聚合。
- 安全：敏感信息脱敏；按需绑定上下文便于现场问题定位。
- 可观测：支持慢 SQL 统一阈值记录，可选 SQL 详日志；支持 RingBuffer 内存旁路。
- 热更新：`state/logging_policy.json` 策略热刷新与重建 sink。

#### 二、核心文件
- `src/utils/logging_utils.py`：提供 `log_throttle`、`log_sample`、`dedup_log`、`aggregate_register/aggregate_should_emit`、`log_once`、`redact`、`bind_context`、`DebugRingBuffer` 等。
- `src/utils/log_config.py`：集中化日志配置，加载策略、桥接环境变量、配置 file/console/perf/sql/RingBuffer sinks，提供 `refresh_and_rebuild_logging()`。
- 策略文件：`state/logging_policy.json`。

#### 三、快速使用
- 脱敏：`logger.info(f"用户: {redact(user_id)}")`
- 绑定上下文：`logger = bind_context(logger, table=table_name, comp="import")`
- 高频采样：`if log_sample("import-progress", 5): logger.debug("导入进度: %s", progress)`
- 时间节流：`if log_throttle("nav-retry", 2.0): logger.info("导航重试...")`
- 去重：`if dedup_log("status", 300, message): logger.info(message)`
- 聚合：`aggregate_register("event"); emit, cnt = aggregate_should_emit("event", 2000)`

#### 四、环境与策略
- 关键环境变量：
  - `LOG_SLOW_SQL_MS`：慢 SQL 阈值（ms）
  - `LOG_SQL_DETAIL`：是否输出 SQL 详日志（0/1）
  - `LOG_PERF_ENABLED`、`LOG_SQL_ENABLED`：是否启用 perf/sql 额外 sink
- 策略文件 `state/logging_policy.json` 字段：
  - `console_level`、`file_level`、`slow_sql_ms`、`sql_detail_enabled`、`enable_perf`、`enable_sql`、`enable_debug_ring`
- 热更新：
  - 代码调用：`from src.utils.log_config import refresh_and_rebuild_logging; refresh_and_rebuild_logging()`
  - UI：菜单“工具”→“刷新日志策略并重建”。

#### 五、典型落地点
- 检测与报表进度：采用 `log_sample` 每 N 次输出一次（默认 5）。
- 导航与自动选择重试：采用 `log_throttle` 按秒级节流 INFO。
- SQL：所有潜在输出点统一受 `LOG_SQL_DETAIL` 控制，慢 SQL 永远按阈值记录 INFO 并脱敏参数。

#### 六、最小测试用例建议
1) 慢 SQL 阈值生效：
   - 设置 `LOG_SLOW_SQL_MS=1`，执行简单查询，应在 `logs/salary_system.log` 出现 `[PERF][SLOW_SQL]` 记录。
2) SQL 详日志开关：
   - 置 `LOG_SQL_DETAIL=1`，执行带参数查询，应看到 `SQL(detail)`，且参数已脱敏。
3) RingBuffer 导出：
   - 在策略开启 `enable_debug_ring=true`，运行应用后通过菜单导出调试快照，验证 `temp/debug_ring_*.log` 生成且无异常字符。
4) 策略热更新：
   - 修改 `state/logging_policy.json` 的 `console_level`、`sql_detail_enabled`，触发“刷新日志策略并重建”，验证新级别和开关生效。

#### 七、注意事项
- Windows 控制台可能对 emoji/彩色输出不友好，已在 `log_config.py` 做兼容；生产环境建议下调控制台级别或关闭。
- 采样/节流 key 应按功能域命名，避免冲突（如 `report-async-progress`、`detect-progress`）。
- 大批量参数时 SQL 详日志仅采样前几条，避免日志爆炸。


