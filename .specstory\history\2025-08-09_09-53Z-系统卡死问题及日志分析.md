<!-- Generated by SpecStory -->

# 系统卡死问题及日志分析 (2025-08-09 09:53Z)

_**User**_

经过之前“日志治理”的相关修改，重新启动系统进行测试，发现下面问题：
1、本来系统操作挺顺畅的，不管从窗口中等大小放大到全屏，再从全屏缩放到中等大小，中间切换到控制台窗口看了一下信息，再切换回系统窗体，
也有最小化窗口，再放大窗口，在连续操作下，系统倒是没有问题。但是，把系统最小化后，放置了一会，然后，把窗口从中等大小放大到全屏时，系统就卡死了。

另外，请你好好分析日志文件，看看系统还存在哪些问题？


你不仅要查看全部日志文件，还需要仔细阅读项目代码，一步一步深入思考（urltrathink），找出可疑的地方（不仅仅是日志中分析出来的，还要根据分析项目代码找出潜在的问题，两者要相互印证），进行全局性的综合分析，给出有效解决方案。暂时不进行具体修复！待用户反馈后，再进行修复。


---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Investigating logging changes**

I'm thinking about the potential issues from the logging governance changes. It might have introduced logging in hot paths that use blocking I/O on the main thread, which could lead to freezes when many events fire during a resize. I should also check the logs for any errors, long gaps, or traces of deadlocks. It’s crucial to proceed with parallel tool calls to explore these issues efficiently. That should help in pinpointing the root cause!</details></think>

---

我先做一次快速的全局排查：并行读取关键日志与核心窗口/事件相关代码，关注最小化/还原/全屏时的事件处理、异常钩子、计时器与日志写入是否可能导致卡死。随后结合日志进行问题归因与方案建议。

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

<details>
                <summary>Searched codebase "Where are Qt window state changes (minimize/restore/maximize) handled? Look for WindowStateChange, changeEvent, eventFilter, showEvent." • **4** results</summary>
            
| File | Lines |
|------|-------|
| `src/gui/prototype/prototype_main_window.py` | L3401-10825 |
| `src/gui/state_manager.py` | L246-590 |
| `src/gui/state_manager.py` | L37-590 |
| `src/gui/state_manager.py` | L32-246 |

</details>

---

<details>
                <summary>Searched codebase "How are resize events handled across the UI? Look for resizeEvent handlers or event filters for resize." • **8** results</summary>
            
| File | Lines |
|------|-------|
| `src/gui/prototype/prototype_main_window.py` | L3401-10825 |
| `src/gui/prototype/managers/responsive_layout_manager.py` | L19-272 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L1520-1750 |
| `src/gui/prototype/managers/responsive_layout_manager.py` | Lundefined-272 |
| `docs/prd/draft/design/ui_design_specification.md` | L1914-1965 |
| `docs/prd/draft/design111/ui_design_specification.md` | L1522-1573 |
| `src/gui/prototype/widgets/material_tab_widget.py` | L243-423 |
| `docs/prd/draft/design/interaction_flow_design.md` | L313-366 |

</details>

---

<details>
                <summary>Searched codebase "How does the Qt exception handling wrap sys.excepthook or use event filters that may show dialogs?" • **10** results</summary>
            
| File | Lines |
|------|-------|
| `main.py` | L150-269 |
| `main.py` | L104-269 |
| `main.py` | L102-269 |
| `main.py` | L150-401 |
| `src/core/error_handler_manager.py` | L55-404 |
| `main.py` | L60-150 |
| `main.py` | L269-419 |
| `main.py` | L432-540 |
| `docs/problems/系统性Qt事件循环和绘制冲突CRITICAL修复报告.md` | Lundefined-57 |
| `docs/problems/系统问题全面分析报告.md` | Lundefined-58 |

</details>

---

<details>
                <summary>Searched codebase "Where are QTimer or custom timers used for UI updates that could backlog after minimize?" • **11** results</summary>
            
| File | Lines |
|------|-------|
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L481-1884 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2070-8841 |
| `src/utils/thread_safe_timer.py` | L258-300 |
| `src/utils/thread_safe_timer.py` | Lundefined-245 |
| `docs/draft/Qt开发安全规范.md` | Lundefined-91 |
| `src/utils/thread_safe_timer.py` | L38-245 |
| `docs/problems/分页和定时器问题立即修复总结.md` | Lundefined-36 |
| `temp/qt_timer_fix_report.md` | Lundefined-70 |
| `docs/problems/系统性Qt事件循环和绘制冲突CRITICAL修复报告.md` | Lundefined-57 |
| `docs/problems/系统性Qt事件循环和绘制冲突CRITICAL修复报告.md` | L51-121 |
| `temp/virtualized_expandable_table_backup_20250807_141629.py` | L2041-8760 |

</details>

---

<think><details><summary>Thought Process</summary>I'm planning to use the search tool to find specific patterns. I see that the developer tool has a grep function, which seems perfect for this. I'll set up multiple grep searches to run in parallel for different patterns, as that will be efficient and save time. Using multi_tool_use.parallel will help me get results more quickly, and I think this approach will work well for what I need!</details></think>

---

我将并行扫描日志中关键异常/告警关键词，以定位卡死相关线索（线程/定时器/事件循环/绘制），同时抓取与窗口状态变化相关的日志片段。随后给出综合分析和解决方案。

---



---



---



---

<think><details><summary>Thought Process</summary>**Analyzing system freeze**

I'm looking into our issue where the system freezes after minimizing and then restoring to fullscreen. I need a deep analysis, especially of logs for potential underlying issues. After reviewing the code and using grep on the logs, it appears we have mostly INFO lines, with few critical or error lines visible. This might mean our logging isn’t catching explicit exceptions. The freeze could be related to QTimer or event loops causing backlogs upon minimizing and resuming.
**Investigating UI issue**

I suspect that the heavy "UI fix" scheduled by _schedule_ui_fix is causing problems when the window state changes or during resizing. If the system was minimized for a while, restoring it to fullscreen might trigger _schedule_ui_fix(180), which could lead to multiple timers accumulating. However, it seems like _schedule_ui_fix is meant to debounce those calls. We haven't opened its implementation in PrototypeMainWindow yet, so I'll need to search for it to dig deeper into the issue.</details></think>

---



---

Read file: undefined

---

<think><details><summary>Thought Process</summary>**Checking process calls**

I'm looking into the _apply_layout function and the assigned appliers it uses. It might be a bit heavy, but hopefully, it won't cause any freezing. I also want to check for the usage of processEvents, as developer guidelines mention some concerns there. I think it would be smart to search for any instances of processEvents to ensure everything runs smoothly. Let's see what I can find on that!</details></think>

---



---

<think><details><summary>Thought Process</summary>**Analyzing layout updates**

I'm thinking about how the force update may call the _apply_layout method, which likely modifies the layout, but it doesn't seem heavy. 

The freeze tends to happen when I minimize the window and then maximize it after some time. This usually stems from heavy synchronous drawing, often due to a 'repaint' flood. I noticed many repaint calls scattered across various method lines, and it seems that during maximize, a changeEvent calls _schedule_ui_fix that triggers brightness detection, leading to multiple repaint calls that might be causing this heavy drawing issue.
**Understanding UI scheduling and performance freezes**

I'm considering how combining with debounce might affect the scheduling of UI updates. The _schedule_ui_fix has a cooldown, but multiple triggers like showEvent, changeEvent, and resizeEvent seem to fire it numerous times. The cooldown prevents duplicates, but pending flags suggest that triggers queue within 150 ms. As the window grows, more resize events occur, which can cause potential freezes.

I also see calls to header_manager.clear_table_header_immediately(), which could lead to performance issues, and there's a question about potential main thread blocking operations triggered by _schedule_ui_fix. They may call the table_data_service.refresh_table_data synchronously, which is something to keep an eye on, too.
**Investigating UI freezing issues**

I’m noting that the freezing issue seems to occur earlier in the _maybe_table_refresh. In the schedule, they only call brightness fixes, header cleanup, and responsive_manager.force_update, and not the database.

From the logs, after startup, they loaded data, but the 'auto_select_latest_data' failed to find the latest path. The freezing happens when I minimize and then maximize after a while; this might link to 'cleanup_timer' triggers since the header manager cleanup runs every 10 seconds. Also, there are unrelated concerns like 'QApplication.processEvents()' and QBasicTimer warnings that aren’t logged.
**Identifying potential issues in UI management**

I see that the ThreadSafeTimer aims to avoid conflicts, and the ColumnWidthManager uses timers safely for saving. However, the WindowStateManager's apply_window_state might cause issues when maximizing, especially since it's only loaded at startup.

Another concern is the damage caused by 'repaint' within loops, and the 'force_update_all' in the header_update_manager triggering multiple updates simultaneously. I’m proposing a solution: remove repaint calls, focus on unified updates, and set delays for state changes to avoid multiple triggers that could cause freezes. It might also help to skip scheduling when minimized and coalesce to a single schedule upon restoration.
**Addressing UI performance concerns**

I need to ensure that the header_cleanup timer is paused while minimized to prevent issues. Another potential freeze might happen during full-screen maximization, especially due to bright fix detection and repeated 'repaint' calls that cause synchronous painting, which we want to avoid.

I’ve also identified other potential issues such as logging noise, timer memory leaks, and the need to manage tasks from the EventBus while the app is minimized without causing freeze. I’ll develop a detailed action plan for improvements, ensuring we maintain a concise and thorough approach while avoiding any immediate code changes.</details></think>

---

### 整体分析思路
- 从日志入手，确认是否有异常/死锁/线程/定时器/事件循环相关告警；结合您描述的“最小化一段时间后，还原到全屏即卡死”的时序，定位窗口状态切换链路。
- 逐步阅读与窗口状态、重绘、响应式布局、防抖/定时器等强相关代码，交叉验证日志与代码路径，找出潜在的卡顿/假死根因。
- 汇总问题清单、触发条件与影响面，给出针对性的方案（不改代码，仅提出方案与实施位点）。

### 日志重点发现（文件：`logs/salary_system.log`）
- 启动与交互阶段未见明显异常、CRITICAL 或 QBasicTimer 警告。全局是 INFO 级别输出，系统运行较“安静”，无直接错误栈。
- 有效还原窗口状态与数据加载的 INFO，窗口自适应断点、表头/亮度修复与分页均可见持续输出，说明这几套机制在运行。
- 未看到“Timers can only be used with threads started with QThread”等定时器跨线程警告。ThreadSafeTimer 改造基本生效。
- 未见 WindowStateChange 的 DEBUG 日志（源码用 debug 级别），加之多处 UI 修复在窗口状态变化与 resize 时合并调度，因此卡死更可能来自“重绘/刷新合并调度”时机与负载，而不是显式异常。

### 代码侧关键勘察与可疑点
- 文件：`src/gui/prototype/prototype_main_window.py`
  - 窗口状态事件链路：
    - `showEvent`、`resizeEvent`、`changeEvent(QEvent.WindowStateChange/ActivationChange)` 在多处调用 `_schedule_ui_fix(...)`（参见 3156、3240、5465、5511、9192、9209、9215、9342 附近）。
    - 这意味着“显示/激活/最大化/还原/持续 resize”都会触发统一的 UI 修复调度。
  - 合并调度核心：
    - `_schedule_ui_fix` 使用 `safe_single_shot(delay_ms, execute_if_latest)` 做单入口合并调度，含冷却与 pending 防抖（8288-8374）。
    - 调度执行内容包括：
      - 亮度问题检测与修复 `_fix_display_brightness_after_data_refresh`；
      - 表头重影检测 + `header_manager.clear_table_header_immediately()`；
      - 响应式布局 `responsive_manager.force_update()`。
  - 反模式：大量同步重绘调用
    - 多处直接调用 `repaint()`（同步立即绘制），遍布表头、行号、viewport、主窗口与子组件，例如：
      - 3297、3301、3306、8093、8098、8125、8166、8171、8183、9017、9023、9034、9137、9181 行等。
    - 对于 Qt，`repaint()` 会在主线程直同步绘制，若在窗口最大化（触发大量 `resizeEvent`）与合并修复叠加下，极易造成主线程长时间忙绘，表现为“卡死/无响应”。项目文档也明确禁止 repaint（见 `docs/draft/Qt开发安全规范.md`）。
  - 典型时序（与您描述吻合）：
    - 窗口最小化一段时间 → 还原为全屏：
      - `changeEvent` + 一连串 `resizeEvent` → 多次请求 `_schedule_ui_fix`（虽有防抖，但会至少落一次）。
      - `execute_if_latest` 中会：亮度检测与修复、表头重影清理、响应式 force_update。
      - 上述修复过程中穿插多处 `repaint()`，恰逢窗口尺寸陡增、视图区域大、表格虚拟化需更新视口/表头，累积同步重绘负载，导致 UI 线程饱和，表现为卡顿/“卡死”。

- 文件：`src/gui/table_header_manager.py`
  - 存在 `cleanup_timer`（10s 周期），并在清理流程中有重绘操作（尽管多为 `update()`）。恢复窗口后与 `_schedule_ui_fix` 的一轮合并修复可能叠加，进一步增加重绘压力。
  - 该管理器对“已删除/弱引用表格”的清理做得较安全，但仍建议与窗口最小化状态联动，避免在最小化期间进行不必要的表头检测/清理。

- 文件：`src/gui/prototype/managers/responsive_layout_manager.py`
  - 使用 `QTimer` 做 resize 防抖（100ms）。force_update 可能与 `_schedule_ui_fix` 同时触发，增加布局变更与绘制的次数，但根因仍是 `repaint()` 引发的同步绘制压力。

- 文件：`src/utils/thread_safe_timer.py`
  - 定时器创建/启动均走主线程，避免 QBasicTimer 警告，方向正确；不是此次“卡死”的主因。

- 文件：`src/gui/prototype/widgets/virtualized_expandable_table.py`
  - 列宽保存/延迟更新的定时器安全性 OK；未见跨线程 QTimer 问题。

### 结论：根因与触发条件（相互印证）
- 根因更偏向重绘策略问题：窗口还原/最大化触发大量 UI 修复（亮度/表头/响应式），而这些修复路径里“多处同步 repaint”在大视口和表格组件上叠加，导致主线程长时间阻塞，表现为“卡死/无响应”（无异常日志，和您现象一致）。
- 最小化“放置一会”后再还原，恰逢管理器定时器（表头清理）与统一修复合并执行容易重叠，放大问题。

### 其他潜在问题（从代码预判）
- `_schedule_ui_fix` 未对“窗口处于最小化/不可见”做前置短路，最小化期间仍可能被触发（来源于其他路径），建议在状态检查后丢弃。
- `changeEvent` 中 ActivationChange 与 WindowStateChange 双重触发 `_schedule_ui_fix`，容易在还原/激活同一瞬间重复调度；虽然有 pending 防抖，但最好就近去重（例如只保留一种状态触发 + 淡化频率）。
- 表头清理器（`TableHeaderManager`）的 `cleanup_timer` 在窗口最小化期间可暂停，避免最小化→还原时与 `_schedule_ui_fix` 集中过载。

### 有效解决方案（暂不改代码）
- 优先级高（根因级）
  - 将 `prototype_main_window.py` 中所有同步 `repaint()` 调用替换为异步 `update()`（遵循项目规范文档）。关键位置：
    - 3297、3301、3306、8093、8098、8125、8166、8171、8183、9017、9023、9034、9137、9181 等行。
  - 在 `_schedule_ui_fix` 增加前置状态短路：
    - 若 `self.isMinimized()` 或窗口不可见，直接 return；在 `changeEvent` 检测到从最小化恢复（首次 NoState/Maximized）时，仅调度一次，延迟适当放大（如 250-300ms），以降低放大窗口瞬间并发开销。
- 优先级中
  - 简化 `changeEvent` 的触发源：仅在 `QEvent.WindowStateChange` 且状态为 `WindowMaximized` 或 `WindowNoState` 才触发；`ActivationChange` 可以不触发统一修复（或与 WindowStateChange 合并为同一路径，避免重复）。
  - `TableHeaderManager.cleanup_timer` 在窗口最小化时暂停；恢复后延迟一轮再启动，防止还原瞬间与 UI 修复叠加。
  - 对响应式 `force_update()` 也做“窗口可见 + 非最小化”前置条件判定，防止无谓触发。
- 优先级一般
  - 将 `_schedule_ui_fix` 的冷却窗口从 300ms 适度提高（比如 400-600ms），专门在 `WindowStateChange` 场景生效，进一步抑制还原/最大化时的多源触发。
  - 将 `DEBUG` 级大量日志在这一链路（亮度/表头/响应式）内做节流，以减少 I/O 干扰（虽不是主因，但可降噪）。

### 方案流程图（Mermaid）
```mermaid
flowchart TD
    A[窗口事件: 最小化/还原/最大化/激活/resize] --> B{是否最小化?}
    B -- 是 --> C[丢弃UI修复调度; 暂停表头清理Timer]
    B -- 否 --> D{事件类型}
    D -- WindowStateChange(还原/最大化) --> E[仅调度一次 _schedule_ui_fix(延迟250-300ms)]
    D -- 其他(resize/激活) --> F[依赖冷却窗口与pending防抖, 可选不触发]
    E --> G[execute_if_latest]
    F --> G
    G --> H[亮度检测]
    H --> I{有问题?}
    I -- 是 --> J[仅使用 update() 进行异步刷新]
    I -- 否 --> K[跳过亮度修复]
    G --> L[表头重影检测]
    L --> M{有重影?}
    M -- 是 --> N[header_manager.clear_table_header_immediately() 仅 update()]
    M -- 否 --> O[跳过清理]
    G --> P[响应式force_update: 仅窗口可见时执行]
    C --> Q[从最小化还原后，延迟一轮再恢复表头清理Timer]
```

### 时序图（Mermaid）
```mermaid
sequenceDiagram
    participant Win as 主窗口
    participant Sch as _schedule_ui_fix
    participant Fix as execute_if_latest
    participant HDR as HeaderManager
    participant Resp as ResponsiveLayoutManager

    Win->>Sch: changeEvent(WindowStateChange: 还原/最大化)
    Sch-->>Win: 若最小化: 丢弃；否则延迟250-300ms调度(带冷却)
    Sch->>Fix: 超时后执行(保证latest+防抖)

    Fix->>Fix: 亮度检测
    alt 有亮度问题
        Fix->>Win: 仅使用 update() 刷新（避免 repaint）
    else 无亮度问题
        Fix-->>Win: 跳过亮度修复
    end

    Fix->>HDR: 表头重影检测
    alt 有重影
        HDR->>HDR: 清理(仅 update，避免 repaint)
    else 无重影
        HDR-->>Fix: 跳过清理
    end

    Fix->>Resp: force_update（仅窗口可见/非最小化）
    Resp-->>Fix: 完成

    Note over Win,Fix: 不在 ActivationChange 或 resize 上重复触发；<br/>最小化期间暂停 Header 清理Timer
```

### 影响范围与风险评估
- 范围：仅 UI 修复合并调度、表头清理、响应式更新链路；不触碰业务逻辑、数据层或定时器安全改造。
- 风险：将同步 `repaint()` 全量替换为 `update()` 会改变绘制时机（从立即绘制改为队列刷新），但根据项目规范与 Qt 最佳实践，这是正确方向，可显著降低主线程阻塞风险。
- 验证点：窗口最小化≥30s → 还原至全屏；拖动窗口反复 resize；切换数据表头/分页；观察是否仍有卡顿。

### 后续建议（不改代码前的验证清单）
- 提升日志粒度：在 `_schedule_ui_fix` 前后记录“窗口状态 + 是否已最小化 + 是否合并丢弃 + 执行耗时”，以确认还原瞬间触发次数与执行体量。
- 启用渲染耗时打点：在重绘相关路径（目前的 repaint 所在函数/替换点）加轻量级耗时统计（DEBUG + 节流），锁定热点。
- 验证表头清理 Timer 暂停/恢复对体验的影响（可先人工在还原瞬间临时跳过一次清理，验证是否能显著缓解卡顿）。

### 项目位置与相关代码
- 窗口事件与合并调度：`src/gui/prototype/prototype_main_window.py`（`showEvent`、`resizeEvent`、`changeEvent`、`_schedule_ui_fix`、`_fix_display_brightness_after_data_refresh` 等）
- 重绘反模式（repaint 调用点）：同文件多处（示例行：3297、3301、3306、8093、8098、8125、8166、8171、8183、9017、9023、9034、9137、9181）
- 表头清理管理：`src/gui/table_header_manager.py`（`cleanup_timer`、`clear_table_header_cache_enhanced`、`_clear_single_table_header`）
- 响应式防抖/布局：`src/gui/prototype/managers/responsive_layout_manager.py`
- 定时器安全：`src/utils/thread_safe_timer.py`

### 注意事项
- Windows/PowerShell 环境无需特殊命令。本轮仅分析，不改动代码。
- 更改方案将遵循现有 `.cursor/rules` 与项目规范（SOLID、单一职责、防异常阻塞、统一日志治理）。

- **不进行具体修复**，待您确认后再分步提交具体“编辑方案”。

- **可复用提醒**：项目已有“避免 repaint 的规范文档”（`docs/draft/Qt开发安全规范.md`）；此次方案与其一致，建议后续统一治理 UI 层的 `repaint()` 使用。

- **边界检查**：还原/最大化的渠道（任务栏、Alt+Tab、Win+方向键）均应验证；极端大分辨率/多屏也要覆盖测试。

### 状态小结
- 已从日志与代码双向确认：“最小化一段时间→还原到全屏”触发统一 UI 修复，与大量同步 repaint 叠加，极可能造成主线程忙绘假死。
- 给出不改代码的完整解决方案与实施位点（以用 update 替代 repaint、在最小化状态短路、还原时合并单次、暂停/延后表头清理Timer）。待您确认后，我将分步提交具体“编辑”与测试指引。

- 若需要，我可先做“最小化状态短路 + 还原合并一次调度”的小步编辑，再验证是否能快速消除卡顿；随后再做 repaint→update 的全面治理。

---

_**User**_

按照你上面的规划来具体实施：
先做“最小化状态短路 + 还原合并一次调度”的小步编辑，再验证是否能快速消除卡顿；随后再做 repaint→update 的全面治理。

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Modifying scheduling for UI**

I'm considering disabling scheduling on ActivationChange entirely or setting conditions to avoid scheduling when restoring a window state. The plan involves modifying the ActivationChange function to prevent calling the UI fix if it's minimized or recently scheduled within a short timeframe. This could involve adding an early return or restricting calls only to deactivation events. For the showEvent and resizeEvent, I’ll make minimal updates, focusing on short-circuiting the process and leaving repaint changes for later. An early return will also be added in `_schedule_ui_fix` to handle visibility.
**Optimizing restore scheduling**

On restoration, I plan to schedule actions, but focus on minimizing operations to reduce potential freezes. The user specifically asked for minimal changes, so I’ll implement short-circuiting on minimize without altering the header manager. To accomplish this, I’ll check if the window is minimized using `self.windowState() & Qt.WindowMinimized` within the `changeEvent` method. I'll also introduce internal fields to track whether the window is minimized and when it was last restored, which will help manage scheduling effectively. Additionally, I’ll set a precondition in `_schedule_ui_fix` to return if minimized.
**Handling maximize and resize events**

When the window is minimized, Qt might not send resize events, but many resizes occur during the maximization process afterward. I'll implement a cooldown in `_schedule_ui_fix` to ensure that only one call is made after restoring. To avoid duplicate scheduling from other triggers right after restoration, I’m considering adding a variable `_restored_recently_until`, but it may not be strictly necessary. Still, I think it’s wise to use a time-based guard to manage this. 

Finally, I’ll log validation suggestions and make edits using the apply_patch tool, ensuring the file is read prior if it hasn’t been opened recently.
**Patching the changeEvent method**

I’ve read the code chunk, and now I need to patch it using the V4A diff format. I'll focus on modifications within the `changeEvent` and `_schedule_ui_fix` methods. Specifically, I’ll change how the window state is handled during `WindowStateChange`. If the new state indicates `Qt.WindowMinimized`, I’ll set `self._is_minimized` to True and return without scheduling. Conversely, if the state is maximized or no state, I’ll check if it was just restored, set `_is_minimized` to False, and adjust the cooldown timing accordingly while letting the scheduling handle the cooldown management itself.
**Implementing scheduling guards**

To ensure only one schedule occurs, I plan to use a guard flag `_just_restored` and rely on pending/cooldown to trigger the scheduling. My idea is to immediately schedule `_schedule_ui_fix(280)` and set another flag `_restored_guard_until` for 1 second to prevent any further scheduling during `ActivationChange`. Since we're already removing scheduling in that function, this seems like a solid approach.

I’ll also add a pre-check in `_schedule_ui_fix` to return if the window isn't visible or is minimized. We don’t need to import time for simplicity, so I’ll proceed with the patch implementation.</details></think>

---



---



---



---

### 实施步骤（已完成）
- 在 `src/gui/prototype/prototype_main_window.py` 做了小步编辑，仅改动窗口状态与合并调度逻辑：
  - changeEvent 最小化短路与还原单次调度：
    - 最小化时标记 `_is_minimized=True` 并直接 return，不触发任何 UI 修复。
    - 从最小化还原至正常/最大化时，仅合并调度一次 `_schedule_ui_fix(280)`，延时略大以避开尺寸剧变期；随后清空 `_is_minimized` 标记。
    - 普通 WindowStateChange（非从最小化还原）的情况仍按 `_schedule_ui_fix(180)` 执行。
    - ActivationChange 不再触发 UI 修复（避免与还原重复）；仅在失焦时触发列宽最终保存。
  - `_schedule_ui_fix` 增加最小化/不可见短路：
    - 当 `_is_minimized=True` 或 `not self.isVisible()` 时直接跳过合并调度，等待还原时机。
- 以上改动未引入新的 linter 报错。

### 验证建议
- 用例1：最小化≥30秒后，直接还原到全屏，观察是否仍卡顿。
- 用例2：连续最大化/还原/中等尺寸切换，确保无“重复触发”导致的抖动。
- 用例3：切换数据表、分页操作后，再进行最小化→还原，确认表头/亮度修复依旧正常。
- 若仍有卡顿迹象，再进入第二步“repaint → update 全面治理”。

### 下一步规划
- 如本次小步优化有效，继续推进 repaint→update 的全量替换与统一规范（仅在窗口与表格/表头相关路径），逐步消除同步重绘引起的主线程阻塞风险。

- 变更位置：
  - `src/gui/prototype/prototype_main_window.py` 中 `changeEvent` 和 `_schedule_ui_fix` 编辑，原因：
    - 避免最小化期间无意义调度；从最小化还原时只执行一次合并修复，降低高峰重绘压力。

- 请先按上述验证用例试跑，反馈结果后我再进行第二阶段“repaint→update”全面治理的具体编辑。

- 简要总结
  - 增加最小化短路与还原单次合并调度，预计显著降低“最小化后还原”瞬时重绘压力。
  - 保留原有 UI 修复逻辑，但避免 ActivationChange 重复调度，减少叠加效应。

---

_**User**_

经过之前修改，重新启动系统进行测试，发现下面问题：
1、本来系统操作挺顺畅的，不管从窗口中等大小放大到全屏，再从全屏缩放到中等大小，中间切换到控制台窗口看了一下信息，再切换回系统窗体，
也有最小化窗口，再放大窗口，在连续操作下，系统倒是没有问题。但是，把系统最小化后，放置了一会，然后，把窗口从中等大小放大到全屏时，系统就卡死了。
上次也是搁置一会后，从最小化状态激活，进行窗口缩放时，系统就卡死了。

另外，请你好好分析日志文件，看看系统还存在哪些问题？


你不仅要查看全部日志文件，还需要仔细阅读项目代码，一步一步深入思考（urltrathink），找出可疑的地方（不仅仅是日志中分析出来的，还要根据分析项目代码找出潜在的问题，两者要相互印证），进行全局性的综合分析，给出有效解决方案。暂时不进行具体修复！待用户反馈后，再进行修复。


---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Analyzing system freeze issues**

I need to take another look at the system freeze issue that happens when minimizing and maximizing the window. Even though we added a minimized short-circuit and scheduled restoration, the freeze still occurs. I should analyze logs and code for other potential issues. It seems crucial to propose next steps: updating the repainting process, disabling timers while minimized, avoiding heavy operations during size changes, and ensuring the event bus is clear. I also want to check the overall call path in the code.</details></think>

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>**Planning virtual table operations**

I want to ensure that virtualized table-heavy operations don't clear contents during header cleanup. It's also important to track the cause of the last fix and avoid unnecessary brightness fixes by caching detection issues. The user asked for log analysis, and the findings show no critical errors and normal event bus activity. It seems there might be a CPU-bound freeze from synchronous repaint storms. I'll present a plan with flowcharts, detailed step-by-step changes, and a test plan without implementing anything just yet.</details></think>

---

### 结论要点
- 复测场景的“卡死”主要出现在：窗口最小化一段时间后，还原（中等→全屏的窗口缩放）时。日志无异常/致命报错，属于UI主线程被重绘/修复链路“占满”的表现，而非崩溃。
- 代码侧最可疑点仍在统一修复链路与同步重绘：
  - `src/gui/prototype/prototype_main_window.py` 存在多处同步 `repaint()`；在还原/放大过程中，`resizeEvent` + `changeEvent` 驱动 `_schedule_ui_fix(...)`，再叠加响应式更新、亮度修复、表头清理，极易形成“同步重绘风暴”，主线程长期忙绘，表现为无响应。
  - 我们已实现“最小化短路 + 还原单次调度”，但当用户“还原后继续手动放大到全屏”时，`resizeEvent` 仍会触发合并调度，且当前修复链路内仍有同步重绘点，故仍可能卡顿。

### 日志检查要点
- `logs/salary_system.log`：
  - 未见 QBasicTimer/QObject::startTimer 之类跨线程定时器告警；`ThreadSafeTimer` 生效。
  - 无 CRITICAL/ERROR 栈。事件总线、数据加载、分页、排序流程均可见正常 INFO 输出。
  - 从日志节奏看，窗口状态变化/数据刷新与响应式更新均按预期触发，无异常路径。
- 结论：不存在显式异常；“卡死”源自 UI 重绘/修复在恢复+放大时的叠加开销过大。

### 代码侧深入可疑点（和日志相互印证）
- 文件：`src/gui/prototype/prototype_main_window.py`
  - 合并调度 `_schedule_ui_fix` 被 `showEvent`/`resizeEvent`/`changeEvent` 多路驱动；且执行内容包括：
    - 亮度检测+修复（会调用多个更新/重绘路径）
    - 表头重影检测+清理（进一步触发绘制）
    - 响应式布局强制更新
  - 同时，本文件中仍有大量同步 `repaint()` 调用（示例行：3297、3301、3306、8093、8098、8125、8166、8171、8183、9017、9023、9034、9137、9181 …）。这在放大到全屏的大视口下，容易让主线程陷入长时间同步绘制。
- 文件：`src/gui/table_header_manager.py`
  - 10秒周期清理定时器 + 强化清理流程；在还原/放大时若与合并修复并发，可能叠加绘制负载（虽多用 update，但内部仍有 reset/clearContents 的重操作）。
- 文件：`src/gui/prototype/managers/responsive_layout_manager.py`
  - 100ms 防抖的 `force_update()`；与 `_schedule_ui_fix` 的“响应式强制更新”叠加时，会增加布局/绘制次数（虽不一定是根因，但加重负载）。

### 有效解决方案（本轮仅给方案，不做具体修复）
为快速止血与根治，建议分两步推进：

一、快速止血（优先执行）
- 限制修复合并调度在“窗口尺寸稳定后”触发
  - 在 `resizeEvent` 路径仅“启动/刷新一次 250~400ms 的稳定计时器”，只在“最后一次 resize 后的稳定期”触发 `_schedule_ui_fix`。避免持续拖动/放大过程触发修复（当前是 150ms，有可能在放大过程就触发）。
  - 针对从最小化还原后用户“手动放大到全屏”的场景，能显著减少修复重叠发生的概率。
- 分相修复：把 `_schedule_ui_fix` 拆成两个阶段
  - 阶段1（尺寸稳定后 0ms）：仅执行“响应式 force_update”（布局）；跳过亮度/表头修复。
  - 阶段2（尺寸稳定后再延迟 400~600ms）：视布局完成后再执行“亮度检测+修复、表头重影检测+清理”（两者都可能触发多组件更新，放在布局完成后更稳）。
- 暂停/延后表头清理定时器
  - 在最小化状态暂停 `TableHeaderManager.cleanup_timer`；还原则延迟 600~800ms 再恢复，避免和阶段2重叠。
- 针对“最小化期间/不可见”短路（我们已实现）
  - 保持已实现的 `_is_minimized` 短路；并对 `_schedule_ui_fix` 的“不可见状态”短路保持生效。

二、根本治理（紧随其后）
- 全面替换同步 `repaint()` 为异步 `update()`（严格执行项目规范）
  - 文件：`src/gui/prototype/prototype_main_window.py`，替换示例行：3297、3301、3306、8093、8098、8125、8166、8171、8183、9017、9023、9034、9137、9181 … 全部改为 `update()`。
  - 原则：只请求重绘，不同步阻塞 UI 线程。交由 Qt 事件循环择机绘制。
- 减重统一修复内容
  - 亮度检测：引入“最近结果缓存”和“变更标记”（无变更则跳过），避免每次都全量检测。
  - 表头重影清理：仅在检测到重影时执行，且避免清空/强制重设内容类重操作；尽量以 `update()`、轻量 refresh 替代 reset/clearContents。
- 触发源去重
  - `ActivationChange` 不再触发统一修复（已调整）；`WindowStateChange` 与 `resizeEvent` 只保留“窗口稳定后”的单次触发。
- 进一步提高 `_schedule_ui_fix` 冷却窗口（例如：300→500ms），仅对 `resizeEvent` 场景生效；保证还原/放大阶段只触发一次。

### 方案对比与优缺点
- 仅做“尺寸稳定后单次触发 + 分相修复 + 暂停定时器”
  - 优点：侵入小、见效快；立刻减少高峰叠加负载。
  - 风险：如果内部仍有同步 `repaint()`，在极大视口下仍存在阻塞的可能。
- 全面“repaint→update”治理
  - 优点：根因解决，统一符合 Qt 最佳实践和项目规范；长期最稳。
  - 风险：改动面较广，需要回归测试，特别是某些地方历史上用 repaint 掩盖过的刷新时序问题，需要验证。

### 实施建议与文件位点
- 快速止血（建议本轮先执行，变更小）
  - `src/gui/prototype/prototype_main_window.py`
    - 在 `resizeEvent` 中通过内部稳定定时器（如 300ms）“只在停止拖动后的稳定期”调用 `_schedule_ui_fix`。
    - 在 `_schedule_ui_fix` 内分相执行：布局优先执行，亮度/表头修复延后 400~600ms。
  - `src/gui/table_header_manager.py`
    - 在窗口最小化事件到来时暂停清理计时器；还原后延迟一轮再恢复。
- 根本治理（次轮）
  - `src/gui/prototype/prototype_main_window.py` 全量替换 `repaint()`→`update()`，并梳理亮度/表头修复内部的“重操作”（清空/重设）使用条件。
  - 审视 `TableHeaderManager._deep_header_cleanup()` 中 `clearContents()`、`reset()` 的必要性，尽量避免在常规修复路径中做大开销操作。

### 测试建议
- 场景1：最小化≥30s → 还原 → 手动拖动到全屏；确认无卡顿。
- 场景2：中等↔全屏连续切换；在期间多次切换到控制台窗口再切回；确认响应仍流畅。
- 场景3：加载记录较多的表后（如 ≥5000 行），重复场景1/2，观察是否仍流畅。
- 场景4：表头/亮度修复效果是否仍在（无重影、无暗淡）。
- 性能观察：在 `_schedule_ui_fix` 前后打点日志（ms级），确认执行时长下降。

### 说明
- 当前日志无异常/致命错误，系统稳定性本身较好。问题主要来自“窗口还原/放大瞬间的 UI 修复叠加负载”，通过“稳定期单次触发 + 分相修复 + repaint→update 根治”可解决。
- 本轮仅提供方案，不修改代码。您确认后，我将先实施“快速止血”三项，再回归测试；若效果确认，再实施“repaint→update 全面治理”。

- 方案流程图
```mermaid
flowchart TD
    A[resizeEvent / WindowStateChange] --> B{窗口可见且非最小化?}
    B -- 否 --> C[丢弃/延后执行]
    B -- 是 --> D[启动稳定定时器(300ms)]
    D --> E[稳定后: 阶段1 执行响应式force_update]
    E --> F[延迟400~600ms: 阶段2 亮度检测+修复, 表头重影检测+清理]
    C --> G[最小化期间暂停表头清理Timer]
    G --> H[还原后再延迟开启Timer]
```

- 时序（从最小化还原后手动放大到全屏）
```mermaid
sequenceDiagram
    participant W as 窗口
    participant R as resize稳定定时器
    participant S as _schedule_ui_fix
    participant Resp as 响应式
    participant Fix as 亮度/表头修复

    W->>R: 连续resize触发（只刷新定时器）
    R-->>S: 稳定期到（仅一次触发）
    S->>Resp: 阶段1：force_update（无重绘风暴）
    S->>Fix: 延迟400~600ms后：亮度检测->必要时update；表头检测->必要时update
```

如果同意，我将按“快速止血（优先）→ repaint→update（根治）”两阶段推进，并提供每一步的最小编辑清单与回归用例。

---

_**User**_

按“快速止血（优先）→ repaint→update（根治）”两阶段推进，并提供每一步的最小编辑清单与回归用例。

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Evaluating user request**

The user is asking for a plan with a minimal edit list and test cases, specifically in two phases. Initially, they wanted no repairs made, but now they're shifting their request. It seems they want to proceed with a plan, providing a checklist and test cases without executing modifications yet. Their phrasing suggests they’re looking for a structure to modify, even if it’s just the plan for now. I'll need to clarify and present the best steps forward.
**Planning code modifications**

I need to follow the guidelines for complex tasks, starting with an analysis before detailing the solution and implementation steps. The user wants a plan and edit lists for both phases, focusing on minimal changes and test cases without making actual code changes now. I'll provide logical diffs per step and ensure the response is concise yet technical, using Chinese for clarity. I should include examples, accurate file paths, and mention specific changes like replacing repaint calls and adding timers. Let's break it down into sections clearly for Phase 1.
**Detailing implementation steps**

I need to outline how to use the member variable _resize_stable_timer as a QTimer with singleShot set to true. It’ll start with a 300-millisecond delay, connected to a lambda for invoking _schedule_ui_fix. I'll ensure this timer initializes properly and is thread-safe. 

Next, I’ll split _schedule_ui_fix into two phases, enabling immediate layout updates and scheduling additional fixes safely. I should add pause and resume methods in TableHeaderManager. Since I'm not coding yet, I'll just note these changes and clarify repaint to update adjustments for Phase 2, alongside automated search patterns and safety notes. I also need to include stepwise regression tests.
**Preparing detailed steps for files**

Let’s break down the steps for each file involved.

For Phase 1, in `prototype_main_window.py`, I'll implement a stable timer with a 300ms delay in resizeEvent, avoiding unnecessary calls to the schedule during resizing. I'll restructure _schedule_ui_fix into two phases and update the changeEvent to include header manager pause and resume calls. 

In `table_header_manager.py`, I’ll introduce public methods for pausing and resuming cleanup, alongside an internal flag to manage table cleanups. 

In `responsive_layout_manager.py`, I’ll add a guard to skip updates when the main window isn't visible, which reduces wasted updates.

Then for Phase 2, I’ll replace repaint with update in the relevant files, listing line numbers and identifying usage patterns. I'll also remove or gate clearContents calls and provide notes on keeping the event loop free.
**Creating regression tests**

I need to outline performance regression tests that cover various steps, measuring event logs and times to ensure everything functions efficiently. It’s important to establish clear acceptance criteria so that the user knows what to expect in terms of performance. I'll summarize these tests effectively, ensuring all relevant details are included. Additionally, I should include Mermaid diagrams to visually represent the processes and flow of the tests. Let’s get this organized and ready for the user!</details></think>

---

### 总体推进方式
- 阶段A（快速止血，优先落地）：减少“还原/放大”瞬时的重绘叠加与修复重叠，先稳住不卡死。
- 阶段B（根治，随后实施）：去掉所有同步重绘（repaint），统一用异步 update，配合轻量修复策略，长期稳定。

### 阶段A（快速止血）最小编辑清单
目标：窗口尺寸稳定后再执行一次修复；修复拆两阶段；最小化期间停表头清理，还原后延迟恢复。

- 文件：`src/gui/prototype/prototype_main_window.py`
  1) resize 稳定定时器（只在停止拖动后的稳定期触发）
     - 函数：`resizeEvent(self, event)`
     - 编辑：
       - 移除/注释当前 `self._schedule_ui_fix(150)` 的直接调用。
       - 新增一个仅在 UI 线程使用的稳定定时器（单次 300ms，命名 `_resize_stable_timer`），每次 resize 时只刷新它；到时后调用一次 `self._schedule_ui_fix(0)`。
       - 要点：成员级 QTimer 单实例；在首次使用前创建好；如使用 `ThreadSafeTimer.create_timer` 也可，不过 resize 在主线程可用直接 `QTimer(self)`。
  2) 合并调度两阶段拆分（布局先行，修复延后）
     - 函数：`_schedule_ui_fix(self, delay_ms: int = 120)`
     - 编辑：
       - 保留已有“最小化/不可见短路”“冷却与挂起防抖”。
       - 将 `execute_if_latest` 内部改为“两阶段执行”：
         - 阶段1（立即执行）：仅做响应式更新（`responsive_manager.force_update()`），不做亮度/表头修复。
         - 阶段2（延迟 400~600ms）：再做“亮度检测+必要时修复”“表头重影检测+必要时清理”。
       - 注意：两个阶段均只用 `update()`（阶段B会全面替换）。
  3) 最小化期间暂停表头清理，还原后延迟恢复
     - 函数：`changeEvent(self, event)`（你已加了最小化短路与还原单次调度）
     - 编辑：
       - 在检测到 `Qt.WindowMinimized` 时：调用全局表头管理器 `get_global_header_manager().stop_shadow_monitoring()` 并显式 `cleanup_timer.stop()` 或新增 `pause_cleanup()`。
       - 在从最小化还原（NoState/Maximized 并且 `_is_minimized==True`）时：延迟 600~800ms 调用 `resume_cleanup()`（或 `start_shadow_monitoring(interval_ms)` + `cleanup_timer.start()`），避免与统一修复重叠。

- 文件：`src/gui/table_header_manager.py`
  1) 暂停/恢复清理（最小侵入）
     - 新增方法：
       - `def pause_cleanup(self):` 内部设置 `_paused=True`；如有 `cleanup_timer.isActive()` 则 `cleanup_timer.stop()`。
       - `def resume_cleanup(self, delay_ms: int = 600):` 使用 `safe_single_shot(delay_ms, _start_cleanup)`；`_start_cleanup()` 内 `_paused=False` 且 `cleanup_timer.start(10000)`。
     - 在清理入口（如 `_auto_cleanup_deleted_tables`、`clear_table_header_cache_enhanced`）开头增加 `if getattr(self,'_paused', False): return` 的保护，避免暂停期间误清理（最小改动，不破坏逻辑）。

- 文件：`src/gui/prototype/managers/responsive_layout_manager.py`
  1) 可见性保护（可选，最小变更）
     - 在 `force_update()`（如有）或 `_apply_layout` 开头增加 `if not main_window.isVisible(): return`；避免最小化/不可见期间的布局重算。

注意：
- 不修改业务逻辑、不改数据层；仅在 GUI 层做调度与节流。
- 本阶段不做 repaint→update 全量替换，仅避免在高峰期叠加。

### 阶段B（根治）最小编辑清单
目标：去掉同步重绘，统一异步刷新，避免主线程阻塞。此阶段落地后彻底解决“卡死”。

- 文件：`src/gui/prototype/prototype_main_window.py`
  1) 全量替换同步重绘
     - 搜索替换以下同步重绘：
       - 行示例（不保证完全一致，以源码为准）：3297、3301、3306、8093、8098、8125、8166、8171、8183、9017、9023、9034、9137、9181 等。
     - 替换策略：
       - `xxx.repaint()` → `xxx.update()`
       - 对 `header`、`viewport`、主窗口与 `main_workspace`、导航面板等所有 repaint 调用统一替换。
     - 规范来源：`docs/draft/Qt开发安全规范.md`（明确禁止 repaint，使用 update）。
  2) 亮度/表头修复轻量化
     - 亮度检测：为 `_detect_brightness_issues` 增加“最近结果缓存+变更标记”，如果样式/图形特效未变则跳过修复。
     - 表头清理：避免 `reset()`/`clearContents()` 等重操作的常态化调用，仅在确有重影时执行；能 update 就不要清空；必要时将深度清理挪到“闲时”（计时器周期）而非交互时刻。

- 文件：`src/gui/table_header_manager.py`
  1) 检查 `_deep_header_cleanup` / `_clear_single_table_header` 里是否仍有 `repaint()`；统一替换为 `update()`。
  2) 减少 `reset()`/`clearContents()` 的触发场景；必要时改为“仅请求刷新（update）+ 下个周期再验证”。

- 其他
  - 全项目 grep `repaint\\(`，逐一替换为 `update()`，但优先 GUI 层（prototype_main_window 与 header_manager）。先小面，回归通过后再扩大面。

### 阶段A 回归用例（执行顺序）
- A-1 基线启动
  - 启动应用，观察日志无异常；打开/切换一两个表，确保正常。
- A-2 最小化-还原压力测试
  - 操作：最小化 ≥ 30s → 还原为中等 → 手动拖动到全屏。
  - 期望：无“无响应”；窗口平滑；日志可看到“从最小化还原，合并调度一次UI修复”，无频繁触发。
- A-3 连续 resize 测试
  - 操作：中等 ↔ 全屏反复快速切换（拖拽），期间切换到控制台再切回。
  - 期望：无明显卡顿；合并调度只在“停止拖动后”触发一次。
- A-4 表头/亮度验证
  - 操作：加载不同表（在职/离休/退休/A岗），观察表头无重影、亮度正常。
- A-5 计时验证（可选）
  - 在 `_schedule_ui_fix` 两阶段前后加 ms 级日志（限速 log_throttle），记录执行耗时：目标 < 50ms（阶段1） + < 150ms（阶段2，非绘制场景）。
- A-6 最小化定时器验证
  - 操作：最小化 → 等待一个清理周期（>10s）→ 还原 → 检查表头清理计时器延迟恢复（600~800ms）后再运行。

### 阶段B 回归用例
- B-1 全尺寸/多屏
  - 大分辨率（≥2K/4K）窗口从中等→全屏、全屏→中等，重复 10 次，观察无卡顿。
- B-2 大数据表
  - 加载行数较多的表（≥5000 行），重复 B-1 测试；确保滚动/排序/分页也流畅。
- B-3 全功能巡检
  - 导航切换、字段映射变化、分页与排序交替、导入/导出等常用路径；确认无“亮度变暗/表头重影/刷新不及时”。

### 风险与注意
- 阶段A 影响范围小，基本不会破坏业务逻辑。主要风险在定时器调度的时机，请用日志限速做观测。
- 阶段B 替换 repaint 后，个别历史代码可能依赖“立即绘制”的副作用；如出现“刷新不及时”，应通过“触发 update 对象范围扩大”（header→table→viewport 逐级）来补足，而非回退 repaint。

### 里程碑与交付
- 里程碑1（A完成）：最小化→还原→放大全屏不再卡顿。
- 里程碑2（B完成）：在所有尺寸/数据体量下，不再出现“无响应”；CPU 占用峰值降低，用户操作顺滑。

如确认，我先落地阶段A（3处最小编辑：resize 稳定定时器、_schedule_ui_fix 两阶段、表头清理暂停/恢复），提交后配合上述回归用例验证；通过后，再执行阶段B（repaint→update 全量治理）。

---

_**User**_

按照你上述规划开始具体实施

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Analyzing code structure**

I noticed in the `responsive_layout_manager` file that methods like `handle_resize` and `_apply_layout` were present, but there was no `force_update` method. Since we previously encountered a call to `self.responsive_manager.force_update()` in the `prototype_main_window`, I need to check if it's implemented elsewhere. I'll consider adding a `force_update` method to recompute the layout based on current size, but if that feels risky, I might trust that it exists. I'll also take care of the `_resize_stable_timer` and other modifications. After making necessary edits, I’ll run lints for the files.</details></think>

---

我将执行阶段A的具体编辑：在 `prototype_main_window.py` 为 resize 引入稳定定时器并将合并修复拆为两阶段，同时在 `table_header_manager.py` 增加暂停/恢复清理的能力，并在最小化/还原时调用暂停/延迟恢复。随后检查静态检查结果。

---



---



---

Read file: undefined

---



---



---

Read file: undefined

---



---



---



---

<details>
                <summary>Searched codebase "def changeEvent(self, event): 窗口状态变化 最小化 WindowMinimized 还原 合并调度" • **2** results</summary>
            
| File | Lines |
|------|-------|
| `src/gui/prototype/prototype_main_window.py` | L3401-10869 |
| `src/gui/prototype/prototype_main_window.py` | L2479-3340 |

</details>

---

Read file: undefined

---



---



---



---



---

Read file: undefined

---



---



---

<think><details><summary>Thought Process</summary>**Planning implementation steps**

The user asked me to implement Stage A, following earlier requests to analyze logs comprehensively. Now, I need to ensure I provide a full analysis along with solutions while focusing on implementing the plan. My next step involves validating the implementation and offering any necessary Windows PowerShell instructions, but without running commands. I will summarize past edits, their reasoning, and include testing components. Additionally, I’m expected to create Mermaid diagrams according to the user's guide.
**Outlining next steps**

I need to identify potential issues beyond just the freeze, like synchronous repaint usage, lack of throttling, and event filter loops. I should cross-check these with code references. However, the user asked for specific fixes only after Stage A, so I should propose an edit list for Stage B but hold off on implementation for now. We previously drafted minimal edit checklists and regression cases, so I’ll finalize Stage A, propose the Stage B plan with diagrams, and ask the user to test for freeze resolution.
**Detailing code changes**

I need to specify the exact file paths changed: `src/gui/prototype/prototype_main_window.py` and `src/gui/table_header_manager.py`, along with concise reasoning for each edit. I'll outline test cases that include minimal steps for reproduction and verification, plus rollback instructions and safety boundary cases. I’ll also add log indicators to monitor and define acceptance criteria. While keeping it succinct, I should include a Mermaid flowchart and sequence diagram, using code fences for clarity and citing relevant lines with the citing_code feature. Lastly, I must mention any Windows OS specifics related to commands.
**Summarizing changes and next steps**

I should wrap up with a summary of key changes and their impacts. For the status update, I need to mention the addition of a stable resize timer, implementation of delayed heavy fixes, and minimizing the pause/resume header, while also noting that linting has been completed. Next, I'll propose the Stage B plan for repaint updates and compile a list of files likely containing repaint calls. I’ll efficiently run parallel grep commands across the src and gui modules to build that list. Time to proceed with the grep!</details></think>

---

我将并行检索代码中同步重绘与事件循环相关的可疑调用，以产出阶段B（repaint→update）需要治理的清单，并不给出具体修改，仅列出位置与建议。随后给出阶段A已实施的变更说明、验证用例与回滚指引。

---



---



---



---



---

<think><details><summary>Thought Process</summary>**Organizing code responses**

I need to provide code citations according to the rules, and ensure I'm sticking to the formatting guidelines, such as using bullet lists and code citations. There’s mention of creating mermaid diagrams, which is fine to include. 

Additionally, I will provide relevant log analysis and include notes about remaining issues. Since they asked not to use tools again, I’ll avoid that. Lastly, I’ll ensure clarity with Chinese characters and include file paths for any modifications, as requested. I’ll finish with a summary that captures all key points.
**Clarifying implementation steps**

I think it’s crucial to provide test scenarios, mentioning a minimal test suite and including manual test instructions as well. It’s also important to outline a risk and rollback strategy in a simple way—if issues arise, I should suggest reverting a commit or changing constants to a previous state.

I'll confirm the command to run builds and note the manual test run. I may also include how to disable the header cleanup if necessary. Lastly, I’ll check on the definition of the get_global_header_manager and ensure any new methods are lint-free.
**Citing code accurately**

I need to include small code citations related to several methods like resizeEvent, _schedule_ui_fix, and the table_header_manager's pause/resume methods. I want to provide a Stage B list of repaint uses along with occurrence counts from grep output, but I can't guarantee the line numbers will match precisely. 

I can cite specific lines from prototype_main_window.py for references, and while I haven't located the line numbers for table_header_manager, I could describe the methods included instead. Since the user requested the exact file paths, I should ensure these are accurate.
**Preparing response outline**

I think we should start with a status update at the top, like, "已完成阶段A最小编辑...正在准备回归测试清单..." Then, I need to include a "变更清单" with file paths, what changes were made, and the reasons behind them. 

I’ll provide Mermaid flow and sequence diagrams, along with regression test cases and their steps. A risk and rollback section is important too. 

Finally, I’ll list painting sites for Stage B adjustments and summarize essential log analysis to address any debug instructions without going into too much detail. Let’s get that all written!</details></think>

---

我先做了阶段A的最小变更，并保持所有改动“可退可控”。接下来给你变更点、验证要点与后续阶段B的治理清单。暂不继续改动，等待你复测反馈。

### 状态
- 已将 resize 高频触发改为“稳定期后仅合并一次调度”；合并调度拆成“轻-重两阶段”；最小化期间暂停表头清理与监控，还原后延迟恢复。
- 静态检查通过，无新增告警或语法错误。

### 本次编辑与原因
- 文件：`src/gui/prototype/prototype_main_window.py`
  - 变更1：`resizeEvent` 引入 300ms 稳定定时器，仅在停止拖动后执行一次合并修复，降低同步重绘风暴风险。
    - 原因：减少窗口还原后继续放大到全屏时的瞬时重绘叠加。
    - 位置引用:
      ```9218:9360:src/gui/prototype/prototype_main_window.py
      def resizeEvent(self, event):
          """处理窗口大小变化事件 - 统一调度、合并执行"""
          super().resizeEvent(event)
          try:
              self.window_resized.emit(event.size())
          except Exception:
              pass

          # 🔧 [统一调度-快速止血] 使用稳定定时器：仅在停止拖动后触发一次合并修复
          try:
              from PyQt5.QtCore import QTimer
              if not hasattr(self, '_resize_stable_timer') or self._resize_stable_timer is None:
                  self._resize_stable_timer = QTimer(self)
                  self._resize_stable_timer.setSingleShot(True)
                  self._resize_stable_timer.timeout.connect(lambda: self._schedule_ui_fix(0))
              if self._resize_stable_timer.isActive():
                  self._resize_stable_timer.stop()
              self._resize_stable_timer.start(300)
          except Exception:
              # 回退到原有机制
              self._schedule_ui_fix(180)
      ```
  - 变更2：`_schedule_ui_fix` 拆成“轻-重两阶段”
    - 阶段1（轻）：仅做响应式布局 `force_update()`；
    - 阶段2（重）：500ms 延迟做亮度修复与表头重影清理（且做前置检测、仅必要才执行）。
    - 原因：在还原/放大阶段先稳定布局，再做重操作，降低主线程占用和卡死概率。
    - 位置引用:
      ```8338:8396:src/gui/prototype/prototype_main_window.py
      # 阶段2：延迟执行亮度/表头修复（重操作）
      from src.utils.thread_safe_timer import safe_single_shot
      def delayed_heavy_fixes():
          try:
              # 亮度检测与修复
              table_widget = self._get_primary_table_widget()
              if table_widget and self.isVisible():
                  issues = self._detect_brightness_issues(table_widget)
                  if issues:
                      self.logger.info(f"UI亮度问题检测到并修复: {issues}")
                      self._fix_display_brightness_after_data_refresh()
                  else:
                      ...
              # 表头重影清理（仅必要时执行）
              if hasattr(self, 'header_manager') and self.header_manager and self.isVisible():
                  ...
          except Exception:
              pass

      safe_single_shot(500, delayed_heavy_fixes)
      ```
  - 变更3：`changeEvent` 最小化/还原控制
    - 最小化：标记 `_is_minimized=True`，并暂停表头清理与监控；
    - 还原：只合并调度一次 UI 修复，并在 700ms 后恢复表头清理与监控，避免与重操作重叠。
    - 原因：最小化期间易导致队列堆积；恢复时错峰执行，避免卡顿。
    - 位置引用:
      ```9224:9256:src/gui/prototype/prototype_main_window.py
      if event.type() == QEvent.WindowStateChange:
          ...
          if self.windowState() & Qt.WindowMinimized:
              setattr(self, "_is_minimized", True)
              self.logger.debug("🔧 [窗口状态] 进入最小化状态，跳过UI修复调度")
              try:
                  from src.gui.table_header_manager import get_global_header_manager
                  hdr = get_global_header_manager()
                  if hasattr(hdr, 'stop_shadow_monitoring'):
                      hdr.stop_shadow_monitoring()
                  if hasattr(hdr, 'pause_cleanup'):
                      hdr.pause_cleanup()
              except Exception:
                  pass
              return

          if (self.windowState() & Qt.WindowMaximized) or (self.windowState() == Qt.WindowNoState):
              if getattr(self, "_is_minimized", False):
                  setattr(self, "_is_minimized", False)
                  self._schedule_ui_fix(280)
                  self.logger.debug("🔧 [窗口状态] 从最小化还原，合并调度一次UI修复")
                  # 延迟恢复
                  try:
                      from src.utils.thread_safe_timer import safe_single_shot
                      from src.gui.table_header_manager import get_global_header_manager
                      def _resume_hdr():
                          try:
                              hdr = get_global_header_manager()
                              if hasattr(hdr, 'resume_cleanup'):
                                  hdr.resume_cleanup(0)
                              if hasattr(hdr, 'start_shadow_monitoring'):
                                  hdr.start_shadow_monitoring(2000)
                          except Exception:
                              pass
                      safe_single_shot(700, _resume_hdr)
                  except Exception:
                      pass
              else:
                  self._schedule_ui_fix(180)
      ```
- 文件：`src/gui/table_header_manager.py`
  - 新增：`pause_cleanup()`、`resume_cleanup(delay_ms=0)` 两个方法，用于最小化/还原期间暂停与延迟恢复清理。
  - 原因：避免最小化期间无意义的清理与重绘，恢复时错峰，降低 UI 争用。

### 阶段A回归用例（优先）
- 用例A1：最小化≥30秒 → 还原至中等 → 立即手动拖拽放大到全屏
  - 预期：无卡死，无明显卡顿；日志出现“进入最小化”“从最小化还原”“responsive-force-update”“延迟重操作执行”相关INFO/DEBUG。
- 用例A2：连续最大化↔中等尺寸切换 5 次
  - 预期：仅稳定期后触发一次 `_schedule_ui_fix`；无“修复风暴”；表头/亮度正常。
- 用例A3：切换表+分页后再最小化→还原
  - 预期：还原后 UI 可见，表头无重影，亮度正常，分页显示一致。
- 命令（PowerShell，Windows10）：
  - 运行程序：`python .\main.py`
  - 运行测试：`python -m pytest -q`

### 风险与回滚
- 风险：300ms 稳定窗可能对极端设备感觉“延迟”；500ms 重操作延迟可能在频繁缩放时稍滞后。
- 快速回滚：将 `resizeEvent` 中 `start(300)` 调回 `self._schedule_ui_fix(150)`，或将二阶段延迟 `safe_single_shot(500, ...)` 调回 0。

### 阶段B（repaint→update）治理清单（仅列位点，不做改动）
- 高优先（建议替换为仅 update，必要时配合小范围 updateGeometry）：
  - `src/gui/prototype/prototype_main_window.py`
    - 3297/3301/3306：`h_header.repaint()`/`v_header.repaint()`/`viewport.repaint()`
    - 8092/8098/8125：表头多处 `repaint()`
    - 8166/8171/8182：`self.repaint()`、`main_workspace.repaint()`、`navigation_panel.repaint()`
    - 9037/9043/9048/9054/9157/9201：表格与表头/viewport 多处 `repaint()`
- 保留 update 的地方（无需改动）：
  - 同文件中大量 `.update()` 已存在；`response_optimizer.py` 仅引用 `QEventLoop`，暂无 `processEvents()`。

### Mermaid 流程与时序（阶段A）
- 窗口缩放与最小化/还原处理流程
```mermaid
graph TD
A["resizeEvent"] --> B{"_resize_stable_timer 300ms"}
B -->|timeout| C["_schedule_ui_fix(0)"]
C --> D["Phase-1: responsive_manager.force_update()"]
D --> E{"500ms delay"}
E --> F["Phase-2: 检测并执行 亮度修复/表头清理"]
G["changeEvent(WindowStateChange) 最小化"] --> H["标记 _is_minimized=True"]
H --> I["暂停表头清理与监控"]
J["changeEvent(WindowStateChange) 还原"] --> K["_schedule_ui_fix(280)"]
K --> L["700ms 后恢复清理与监控"]
```
- 时序图（最小化→还原→手动放大）
```mermaid
sequenceDiagram
participant U as 用户
participant W as 主窗口
participant R as 响应式管理器
participant H as 表头管理器
U->>W: 最小化
W->>H: pause_cleanup(), stop_shadow_monitoring()
U->>W: 还原
W->>W: _schedule_ui_fix(280)
W->>R: force_update() (Phase-1)
Note right of W: 500ms 延迟
W->>W: 亮度检测/修复 + 表头重影清理 (Phase-2)
Note right of W: 700ms 延迟
W->>H: resume_cleanup(), start_shadow_monitoring(2000)
U->>W: 拖拽放大到全屏
W->>W: 300ms 稳定后仅一次 _schedule_ui_fix
```

### 建议的日志关注点
- 关键词：”进入最小化状态“、”从最小化还原“、”responsive-force-update“、”UI亮度问题检测到并修复“、”未检测到表头重影，跳过清理“。
- 若仍卡顿，请抓取卡顿前后 5 秒内日志片段（`logs/salary_system.log`），我将据此决定是否提前推进阶段B。

- 变更的文件路径
  - `src/gui/prototype/prototype_main_window.py`
  - `src/gui/table_header_manager.py`

- 验证完成后再决定是否执行阶段B的 repaint→update 全量治理。若你同意，我将按上述“治理清单”的位置逐点替换，并保持小步提交与回归。

---

_**User**_

经过上面修改，启动系统进行了测试，日志文件是最新测试结果。
请你分析一下，看看你上面的修复是否达到预期？

另外，请你好好分析日志文件，看看系统还存在哪些问题？

你不仅要查看全部日志文件，还需要仔细阅读项目代码，一步一步深入思考（urltrathink），找出可疑的地方（不仅仅是日志中分析出来的，还要根据分析项目代码找出潜在的问题，两者要相互印证），进行全局性的综合分析，给出有效解决方案。暂时不进行具体修复！待用户反馈后，再进行修复。

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Analyzing log files**

I need to analyze the latest logs, specifically the salary_system.log, by using the read_file tool as directed. Before I call the tool, I’ll provide a status update. I plan to search for certain patterns, like specific messages in the logs, and look for ERROR, CRITICAL, and traceback indications. I’ll also scan for any long operations noted by keywords. I discovered many repaint occurrences in prototype_main_window, so I’ll develop a plan to identify and address potential issues.
**Preparing log analysis and diagrams**

I need to create Mermaid diagrams for the plan and sequence according to the user rules, ensuring that the structure is clear with minimal explanations, especially in Chinese. I'll point out the project's content references and note reasons for future modifications, but for now, I’ll focus only on proposing changes without making them. First, I’ll read the entire salary_system.log using the read_file function. Then, I’ll run multiple grep searches simultaneously for key patterns, followed by an analysis of whether the fixes achieved the desired outcomes, including potential system issues.
**Reviewing code and proposing tasks**

I need to review the response_optimizer.py for QEventLoop usage and assess the wide use of repaint in prototype_main_window. I'll propose tasks for Stage B, such as replacing repaints, centralizing update calls, throttling heavy operations, and unifying header manager operations. I'll check the thread_safe_timer for safe_single_shot semantics to determine if it ensures execution on the UI thread. Additionally, I’ll examine environment_aware_logger configurations to see if log file rotation could cause issues, and I’ll search the logs for duration measurements related to long tasks.
**Running tool calls for analysis**

I'm set to run several parallel tool calls to analyze logs and patterns. I'll read the salary_system.log and grep for critical issues like CRITICAL, ERROR, and Traceback messages. Additionally, I want to search for new debug strings, repaint logs, SLOW_SQL occurrences, and Timer references. I also plan to investigate card freeze frameworks and double-check for processEvents in the codebase. After my analysis, I need to summarize whether the fixes worked, specifically looking for minimalization and resume flow execution while minimized. Let's go ahead and run these tools!</details></think>

---

我将先读取最新日志并并行检索关键模式（异常、我们新增的窗口状态日志、潜在性能与SQL锁问题），随后给出是否达成预期的评估与全局问题清单和解决方案建议。

---

Read file: undefined

---



---



---



---



---

<think><details><summary>Thought Process</summary>**Planning concise solutions**

I'm thinking about suggesting to add indexes for frequently sorted columns, which could really help performance. 

I also want to focus on the interplay of timers and the safe_single_shot function, ensuring it’s executed in the UI thread—sounds good! Previously, there was a P0 fix for the state manager, but it seems that's not needed anymore. 

I’ll provide a Mermaid flow and sequence for Stage B while keeping everything concise, including necessary diagrams and bullet points to meet the user’s request.</details></think>

---

### 结论（是否达成预期）
- 从最新日志看，未出现异常栈、定时器/事件循环/数据库锁警告，交互链路（分页、排序、渲染、状态保存等）持续输出，未中断，说明“最小化一段时间→还原→继续放大到全屏”的卡死现象已明显缓解，系统保持响应。
- 我们新增的“轻-重两阶段合并调度”已生效，能看到 Phase-2 的“亮度修复”在延迟后执行一次，无风暴迹象：
  ```158:161:logs/salary_system.log
  2025-08-09 19:42:58.716 | INFO | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:8353 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet', 'parent_opacity_effect']
  2025-08-09 19:42:58.717 | INFO | ... _fix_display_brightness_after_data_refresh | 开始智能显示亮度修复（增强防护）
  ```
- 但由于“最小化/还原”的日志在 DEBUG 级别，当前日志级别为 INFO，未能直接看到“进入最小化/从最小化还原”的标记，无法从日志文本层面100%佐证短路生效；从行为上（日志连贯，无中断）可间接判定生效。

### 日志要点（节选）
- 初始化/数据流/渲染/分页/排序均正常、时序正确、性能可接受：
  ```44:47:logs/salary_system.log
  架构重构系统初始化成功…
  ```
  ```614:621:logs/salary_system.log
  小数据集渲染完成: 50行 x 24列, 耗时19.5ms … 性能评级=excellent
  ```
  ```672:676:logs/salary_system.log
  分页决策输出: pagination … 预期性能=550ms
  ```
  ```1699:1703:logs/salary_system.log
  多列排序→发布排序请求→保持当前页码
  ```
- 表头重影检测按需运行，未发现重影：
  ```523:527:logs/salary_system.log
  智能表头重影检测完成 … 0 个重影表格
  ```
- 未见 SLOW_SQL/locked/事务阻塞、未见定时器/事件循环/死锁/卡死关键字。

### 仍需关注的可疑点（代码+日志交叉印证）
- 同步重绘残留（高风险源头未做根治，阶段B需处理）
  - `src/gui/prototype/prototype_main_window.py` 中多处 `repaint()` 仍存在，可能在极端窗口变化或复杂表格状态时放大主线程压力：
    - 3297/3301/3306、8092/8098/8125、8166/8171/8182、9037/9043/9048/9054/9157/9201 等行。
- 合并调度的“重操作”延迟执行未绑定 token（潜在竞态）
  - `_schedule_ui_fix` 只对 execute_if_latest 做了 token 校验；`delayed_heavy_fixes` 是另起 500ms 定时，缺少“过期判定”。连续 resize/还原下，旧的 heavy-fix 仍可能落地执行一次，带来冗余负载。
- 窗口状态日志级别（观测性不足）
  - 最小化/还原的关键日志为 DEBUG 级别，当前 INFO 级别下无法从日志直接佐证“短路/单次调度”行为，影响问题再现定位。
- 可视行调试与采样日志可能偏多
  - 如 `virtualized_expandable_table._update_visible_rows` 输出可见行信息，建议保持采样/节流，避免频繁 resize 时 IO 抖动。
- 性能余量与 SQL 排序索引
  - 当前分页/排序耗时看似良好，但如果后续常用排序列未建索引，数据量增长后会退化（需要核对 `dynamic_table_manager` 实际 SQL 和索引策略）。

### 有效解决方案（暂不改代码，供你确认后实施）
- 快速完善止血（A-补充）
  - 在 `src/gui/prototype/prototype_main_window.py::_schedule_ui_fix` 中给 `delayed_heavy_fixes` 加“同源 token 校验”，若 token 已变化（表示期间又有新一轮调度），则跳过旧的 heavy-fix。这样能杜绝延迟任务过期执行。
  - 将“还原后 280ms 的首次合并调度”与“resize 稳定定时器 300ms”之间的重叠再加一道 150~200ms 抖动窗口（仅对重操作），减少还原→立即拖拽放大场景中的叠加概率。
  - 临时将“最小化/还原”的关键节点提升到 INFO（或保留 DEBUG 但加一条 INFO 汇总日志），便于验证短路/单次调度是否按预期发生。
- 根治（阶段B：repaint→update 全面治理）
  - 政策：禁止在事件处理（`resizeEvent`/`changeEvent`/数据刷新回调）中使用 `repaint()`；统一改为 `update()`，让 Qt 合并绘制；必要时仅在非常小范围使用 `updateGeometry()` 或局部 `viewport().update()`。
  - 建议治理序列（按影响面从小到大）：
    1) `src/gui/prototype/prototype_main_window.py`
       - 移除/替换 3297/3301/3306、8092/8098/8125、8166/8171/8182、9037/9043/9048/9054/9157/9201 的 `repaint()` 为 `update()`。
    2) `src/gui/table_header_manager.py`
       - 已多处改为 `update()`，检查是否仍有 `repaint()` 残留；确保重影清理后仅请求刷新，不强制重绘。
    3) `src/gui/prototype/widgets/virtualized_expandable_table.py`
       - 检查是否有“渲染后强制重绘”的路径（`repaint()`/`reset()`），尽可能仅 `update()`。
    4) 统一规范：在“修复链路”（亮度、表头、分页状态）只发出一次合并后的刷新请求；避免多处模块各自 `repaint()`。
- 日志治理（与阶段B同步推进）
  - 将窗口状态关键路径增加一条 INFO 汇总日志（例如：Minimized→Restored→Maximized），其余细节仍在 DEBUG。
  - 对 `_update_visible_rows`、行/列细节输出引入 `log_throttle()` 或抽样策略；长操作增加“开始/结束+耗时”INFO，细节改 DEBUG。
- SQL/数据侧建议
  - 针对高频排序列（如 `position_salary_2025`, `grade_salary_2025` 等）核对 SQLite 索引；如果 `dynamic_table_manager` 采用 SQL 排序，应在初始化阶段按配置自动建索引（避免后期数据量上来性能退化）。

### 验证建议（你这边复测重点）
- 继续严格复现实验：
  - 最小化≥60秒 → 还原到中等 → 连续拖拽放大至全屏 → 切回控制台再切回系统窗体。
  - 观察：是否还有“瞬时无响应（>1s）”；日志应出现一次 `delayed_heavy_fixes`，且无密集重复。
- 如仍有卡顿，请截取“卡顿前后各5秒”的日志片段，我将基于 token 竞态或残留 repaint 点位进一步精确收敛。

### Mermaid（阶段B治理流程和时序）
- 重绘治理流程
```mermaid
graph TD
A["事件触发(resize/change/data)"] --> B["仅 update() 请求刷新"]
B --> C{"是否必须立即重绘？"}
C -- 否 --> D["交由Qt事件循环合并绘制"]
C -- 是(慎用) --> E["极小范围 updateGeometry()/局部update()"]
D --> F["一次合并后的稳定绘制"]
```
- 合并调度与 token 校验时序
```mermaid
sequenceDiagram
participant R as resize/change
participant S as _schedule_ui_fix
participant P1 as Phase-1(轻)
participant P2 as Phase-2(重, 延迟500ms)
R->>S: 调度(生成token T1)
S->>P1: responsive.force_update()
Note right of S: 记录当前token T1
S->>P2: safe_single_shot(500ms)
R->>S: 再次调度(生成token T2)
P2->>S: 到时检查token
S-->>P2: 若当前token!=T1则放弃执行（过期）
P2->>S: 若token匹配，执行亮度/表头修复
```

如果你确认以上方向，我再按“ A-补充（token校验/日志可观测性）→ 阶段B（repaint→update）”的顺序，小步提交，每一步都附回归用例与对照日志点位。

---

