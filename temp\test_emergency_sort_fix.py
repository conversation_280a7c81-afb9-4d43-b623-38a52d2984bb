#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急排序修复验证脚本

验证基于真正问题根源的修复方案是否生效
"""

import sys
import os
import ast
import py_compile
from pathlib import Path

def check_syntax():
    """检查修改后的文件语法"""
    print("🔍 检查修改后的文件语法...")
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    try:
        # 语法检查
        py_compile.compile(file_path, doraise=True)
        print("✅ 语法检查通过")
        
        # AST解析检查
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        print("✅ AST解析通过")
        
        return True
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def verify_emergency_fix():
    """验证紧急修复的关键要素"""
    print("\n🔍 验证紧急修复的关键要素...")
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = {
        "_ensure_sort_signal_connection方法存在": "def _ensure_sort_signal_connection(self):" in content,
        "信号接收者数量检查": "current_receivers = header.sortIndicatorChanged.receivers()" in content,
        "主窗口获取改进": "通过类名查找PrototypeMainWindow" in content,
        "信号连接验证": "new_receivers = header.sortIndicatorChanged.receivers()" in content,
        "在_set_data_impl中调用": "self._ensure_sort_signal_connection()" in content,
        "异常处理完善": "except Exception as signal_error:" in content,
        "紧急修复日志": "🔧 [紧急修复]" in content
    }
    
    all_passed = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}: {'通过' if result else '失败'}")
        if not result:
            all_passed = False
    
    return all_passed

def analyze_fix_logic():
    """分析修复逻辑的关键流程"""
    print("\n🔍 分析修复逻辑...")
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 查找关键代码段
    in_ensure_method = False
    in_set_data_call = False
    ensure_method_lines = []
    set_data_call_lines = []
    
    for i, line in enumerate(lines):
        line_num = i + 1
        line_stripped = line.strip()
        
        # 查找_ensure_sort_signal_connection方法
        if "def _ensure_sort_signal_connection(self):" in line:
            in_ensure_method = True
            ensure_method_lines.append((line_num, line_stripped))
        elif in_ensure_method and line_stripped.startswith("def "):
            in_ensure_method = False
        elif in_ensure_method and line_stripped:
            ensure_method_lines.append((line_num, line_stripped))
        
        # 查找_set_data_impl中的调用
        if "self._ensure_sort_signal_connection()" in line:
            in_set_data_call = True
            set_data_call_lines.append((line_num, line_stripped))
        elif in_set_data_call and "except Exception as signal_error:" in line:
            set_data_call_lines.append((line_num, line_stripped))
            in_set_data_call = False
    
    if ensure_method_lines:
        print("✅ 找到_ensure_sort_signal_connection方法:")
        key_lines = [line for line in ensure_method_lines if any(keyword in line[1] for keyword in ["current_receivers", "sortIndicatorChanged.connect", "紧急修复"])]
        for line_num, line in key_lines[:5]:  # 显示前5个关键行
            print(f"  第{line_num}行: {line}")
    else:
        print("❌ 未找到_ensure_sort_signal_connection方法")
        return False
    
    if set_data_call_lines:
        print("\n✅ 找到_set_data_impl中的调用:")
        for line_num, line in set_data_call_lines:
            print(f"  第{line_num}行: {line}")
    else:
        print("\n❌ 未找到_set_data_impl中的调用")
        return False
    
    return True

def check_critical_fix_points():
    """检查关键修复点"""
    print("\n🔍 检查关键修复点...")
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查_fix_header_signal_connections是否被注释
    commented_call_pattern = "# self._fix_header_signal_connections()"
    if commented_call_pattern in content:
        print("✅ 确认_fix_header_signal_connections()调用被注释（这是问题根源）")
        
        # 查找注释位置
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if commented_call_pattern in line:
                print(f"  位置: 第{i+1}行: {line.strip()}")
                break
    else:
        print("❌ 未找到被注释的_fix_header_signal_connections()调用")
    
    # 检查新的修复是否在正确的位置
    ensure_call_pattern = "self._ensure_sort_signal_connection()"
    if ensure_call_pattern in content:
        print("✅ 确认新的信号连接检查已添加")
        
        # 查找调用位置的上下文
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if ensure_call_pattern in line:
                print(f"  位置: 第{i+1}行")
                print(f"  上下文: ...UI强制刷新完成 → 排序信号连接检查")
                break
    else:
        print("❌ 未找到新的信号连接检查调用")
        return False
    
    return True

def create_test_guide():
    """创建测试指南"""
    print("\n📝 生成测试指南...")
    
    test_guide = """
# 🧪 紧急排序修复测试指南

## 🎯 修复原理
基于发现的真正问题根源：`_fix_header_signal_connections()`方法被注释掉，导致sortIndicatorChanged信号从未被连接。

## 🔧 修复方案
将关键的信号连接代码移动到确定会被执行的位置（`_set_data_impl`末尾）。

## 📋 测试步骤

### 1. 启动系统检查日志 ⭐⭐⭐
**关键日志指标**:
```
✅ "🔧 [紧急修复] sortIndicatorChanged信号已连接到主窗口"
✅ "🔧 [紧急修复] 信号接收者数量: 0 → 1"
✅ "🔧 [紧急修复] 排序信号连接检查完成"
```

**失败日志指标**:
```
❌ "🔧 [紧急修复] 无法获取主窗口或_on_sort_indicator_changed方法"
❌ "🔧 [紧急修复] 排序信号连接检查失败"
```

### 2. 立即排序功能测试 ⭐⭐⭐
1. **操作**: 点击任意列的表头
2. **预期**: 
   - ✅ 立即看到排序指示器（▲ 或 ▼）
   - ✅ 数据立即按该列排序显示
   - ✅ **无需点击刷新或分页按钮**
3. **响应时间**: < 100ms

### 3. 多种场景验证
- **单页数据**: 无需刷新按钮
- **多页数据**: 无需下一页按钮
- **连续排序**: 多次点击同一列头切换排序方向
- **不同列排序**: 切换不同列的排序

## 🎉 成功标准
- ✅ 日志显示信号连接成功
- ✅ 点击表头立即排序（< 100ms）
- ✅ 无需任何额外操作
- ✅ 所有列都支持排序
- ✅ 排序指示器正确显示

## 🚨 如果仍然失败
可能的原因：
1. **主窗口获取失败**: 检查调试日志中的主窗口获取信息
2. **_on_sort_indicator_changed方法不存在**: 检查主窗口类的实现
3. **信号连接被阻塞**: 可能存在其他代码干扰

## 📊 与之前修复的区别
- **之前**: 在被注释的方法中修复 → 代码不执行 → 无效
- **现在**: 在确定执行的方法中修复 → 代码必定执行 → 有效

---

**关键洞察**: 这次修复解决了执行路径问题，而不仅仅是代码逻辑问题。
"""
    
    with open("temp/emergency_fix_test_guide.md", "w", encoding="utf-8") as f:
        f.write(test_guide)
    
    print("✅ 测试指南已生成: temp/emergency_fix_test_guide.md")

def main():
    """主函数"""
    print("🚀 紧急排序修复验证开始")
    print("=" * 60)
    
    # 检查语法
    if not check_syntax():
        print("\n❌ 语法检查失败，请先修复语法错误")
        return False
    
    # 验证紧急修复
    if not verify_emergency_fix():
        print("\n❌ 紧急修复验证失败")
        return False
    
    # 分析修复逻辑
    if not analyze_fix_logic():
        print("\n❌ 修复逻辑分析失败")
        return False
    
    # 检查关键修复点
    if not check_critical_fix_points():
        print("\n❌ 关键修复点检查失败")
        return False
    
    # 生成测试指南
    create_test_guide()
    
    print("\n" + "=" * 60)
    print("🎉 紧急修复验证完成！")
    print("\n📋 修复摘要:")
    print("✅ 问题根源: _fix_header_signal_connections()被注释，信号连接从未建立")
    print("✅ 解决方案: 在_set_data_impl末尾确保信号连接")
    print("✅ 关键改进: 多策略主窗口获取，重复连接保护")
    print("✅ 执行保证: 每次设置数据时都检查信号连接")
    
    print("\n🎯 预期效果:")
    print("- 点击表头 → 立即排序（< 100ms）")
    print("- 无需刷新或分页操作")
    print("- 完全恢复用户预期的即时响应")
    
    print("\n🔍 关键区别:")
    print("- 之前: 修复代码不执行 → 无效果")
    print("- 现在: 修复代码必定执行 → 应该有效")
    
    print("\n📝 下一步:")
    print("1. 启动系统查看日志中的信号连接信息")
    print("2. 测试点击表头的立即排序功能")
    print("3. 参考 temp/emergency_fix_test_guide.md 进行全面测试")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)