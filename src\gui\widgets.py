"""
月度工资异动处理系统 - 自定义控件模块

本模块实现系统的各种自定义控件，提供专门的数据展示和交互功能。

主要控件:
1. DataTableWidget - 数据表格控件
2. ChangeDetectionWidget - 异动检测控件
3. ReportPreviewWidget - 报告预览控件
"""

from typing import List, Dict, Any, Optional, Union, Callable
from PyQt5.QtWidgets import (
    QWidget, QTableWidget, QTableWidgetItem, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QCheckBox, QSpinBox,
    QHeaderView, QAbstractItemView, QMenu, QAction, QMessageBox,
    QTextEdit, QSplitter, QGroupBox, QFrame, QProgressBar
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPoint, QSortFilterProxyModel
from src.utils.thread_safe_timer import safe_single_shot
from PyQt5.QtGui import QFont, QColor, QBrush, QCursor

from src.utils.log_config import setup_logger


class DataTableWidget(QTableWidget):
    """
    数据表格控件
    
    提供数据展示、编辑、筛选、排序等功能的表格控件。
    """
    
    # 信号定义
    data_changed = pyqtSignal(dict)  # 数据变化信号
    row_selected = pyqtSignal(int)   # 行选择信号
    
    def __init__(self, parent=None, headers: Optional[List[str]] = None):
        """
        初始化数据表格
        
        Args:
            parent: 父组件
            headers: 表头列表，如果为None则创建空表格
        """
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.original_data = []  # 原始数据
        self.filtered_data = []  # 筛选后的数据
        self._init_ui()
        self._setup_context_menu()
        
        if headers:
            self.setColumnCount(len(headers))
            self.setHorizontalHeaderLabels(headers)
        
        # 设置表格样式和行为
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSortingEnabled(True)
        
        # 设置列宽自适应
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
    def _init_ui(self):
        """初始化界面"""
        # 连接信号
        self.itemSelectionChanged.connect(self._on_selection_changed)
        self.itemChanged.connect(self._on_item_changed)
        
        # 设置样式
        self.setStyleSheet("""
            QTableWidget {
                border: 1px solid #d0d0d0;
                alternate-background-color: #f8f9fa;
                selection-background-color: #0078d4;
                gridline-color: #e0e0e0;
            }
            QTableWidget::item {
                padding: 4px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
                color: white;
            }
            QHeaderView::section {
                background-color: #f1f3f4;
                border: 1px solid #d0d0d0;
                padding: 6px;
                font-weight: bold;
            }
        """)
    
    def _setup_context_menu(self):
        """设置右键菜单"""
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)
    
    def _show_context_menu(self, position: QPoint):
        """显示右键菜单"""
        if self.itemAt(position) is None:
            return
            
        menu = QMenu(self)
        
        # 复制行
        copy_action = QAction("复制行", self)
        copy_action.triggered.connect(self._copy_selected_row)
        menu.addAction(copy_action)
        
        # 删除行
        delete_action = QAction("删除行", self)
        delete_action.triggered.connect(self._delete_selected_row)
        menu.addAction(delete_action)
        
        menu.addSeparator()
        
        # 导出数据
        export_action = QAction("导出数据", self)
        export_action.triggered.connect(self._export_data)
        menu.addAction(export_action)
        
        # 刷新数据
        refresh_action = QAction("刷新数据", self)
        refresh_action.triggered.connect(self.refresh_data)
        menu.addAction(refresh_action)
        
        menu.exec_(self.mapToGlobal(position))
    
    def set_data(self, data: List[Dict[str, Any]], headers: List[str] = None):
        """设置表格数据"""
        try:
            self.original_data = data.copy()
            self.filtered_data = data.copy()
            
            if not data:
                self.clear()
                return
            
            # 设置列数和表头
            if headers:
                self.setColumnCount(len(headers))
                self.setHorizontalHeaderLabels(headers)
            else:
                # 从第一行数据获取表头
                headers = list(data[0].keys())
                self.setColumnCount(len(headers))
                self.setHorizontalHeaderLabels(headers)
            
            # 设置行数
            self.setRowCount(len(data))
            
            # 填充数据
            self._populate_data(data)
            
            # 调整列宽
            self._adjust_column_widths()
            
            self.logger.info(f"表格数据已更新，共 {len(data)} 行")
            
        except Exception as e:
            self.logger.error(f"设置表格数据失败: {e}")
            QMessageBox.critical(self, "错误", f"设置表格数据失败: {e}")
    
    def _populate_data(self, data: List[Dict[str, Any]]):
        """填充数据到表格"""
        headers = [self.horizontalHeaderItem(i).text() for i in range(self.columnCount())]
        
        for row, record in enumerate(data):
            for col, header in enumerate(headers):
                value = record.get(header, "")
                item = self._create_number_cell(value) if isinstance(value, (int, float)) else self._create_text_cell(value)
                
                # 设置数值颜色
                if isinstance(value, (int, float)) and value < 0:
                    item.setForeground(QBrush(QColor(220, 20, 60)))  # 负数用红色
                elif isinstance(value, (int, float)) and value > 0:
                    item.setForeground(QBrush(QColor(34, 139, 34)))  # 正数用绿色
                
                self.setItem(row, col, item)
    
    def _adjust_column_widths(self):
        """调整列宽"""
        header = self.horizontalHeader()
        
        # 前几列使用内容适应宽度
        for i in range(min(3, self.columnCount())):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
        
        # 最后一列拉伸填充
        if self.columnCount() > 3:
            header.setSectionResizeMode(self.columnCount() - 1, QHeaderView.Stretch)
    
    def filter_data(self, column: str, value: str):
        """筛选数据"""
        try:
            if not value.strip():
                self.filtered_data = self.original_data.copy()
            else:
                self.filtered_data = [
                    record for record in self.original_data
                    if str(record.get(column, "")).lower().find(value.lower()) >= 0
                ]
            
            # 重新填充表格
            self.setRowCount(len(self.filtered_data))
            self._populate_data(self.filtered_data)
            
            self.logger.info(f"数据筛选完成，显示 {len(self.filtered_data)} 行")
            
        except Exception as e:
            self.logger.error(f"数据筛选失败: {e}")
    
    def get_selected_data(self) -> Optional[Dict[str, Any]]:
        """获取选中行的数据"""
        current_row = self.currentRow()
        if current_row >= 0 and current_row < len(self.filtered_data):
            return self.filtered_data[current_row].copy()
        return None
    
    def refresh_data(self):
        """刷新数据"""
        try:
            # 重新设置数据
            if self.original_data:
                self.set_data(self.original_data)
                self.logger.info("数据已刷新")
        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")
    
    def _on_selection_changed(self):
        """选择变化事件"""
        current_row = self.currentRow()
        if current_row >= 0:
            self.row_selected.emit(current_row)
    
    def _on_item_changed(self, item: QTableWidgetItem):
        """单元格内容变化事件"""
        row = item.row()
        column = item.column()
        new_value = item.text()
        
        # 更新内部数据
        if row < len(self.filtered_data):
            header = self.horizontalHeaderItem(column).text()
            old_value = self.filtered_data[row].get(header, "")
            self.filtered_data[row][header] = new_value
            
            # 发射数据变化信号
            self.data_changed.emit({
                'row': row,
                'column': header,
                'old_value': old_value,
                'new_value': new_value
            })
    
    def _copy_selected_row(self):
        """复制选中行"""
        try:
            selected_data = self.get_selected_data()
            if selected_data:
                # 这里可以实现复制到剪贴板的功能
                QMessageBox.information(self, "提示", "行数据已复制")
        except Exception as e:
            self.logger.error(f"复制行失败: {e}")
    
    def _delete_selected_row(self):
        """删除选中行"""
        try:
            current_row = self.currentRow()
            if current_row >= 0:
                reply = QMessageBox.question(
                    self, "确认删除", "确定要删除选中的行吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    # 从数据中删除
                    if current_row < len(self.filtered_data):
                        del self.filtered_data[current_row]
                    
                    # 从表格中删除
                    self.removeRow(current_row)
                    
                    self.logger.info(f"已删除第 {current_row + 1} 行")
        except Exception as e:
            self.logger.error(f"删除行失败: {e}")
    
    def _export_data(self):
        """导出数据"""
        try:
            # 这里可以实现数据导出功能
            QMessageBox.information(self, "提示", "导出功能开发中...")
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")

    def _create_number_cell(self, value: Union[int, float]) -> QTableWidgetItem:
        """创建数字类型的单元格"""
        item = QTableWidgetItem(str(value))
        item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        return item
    
    def _create_text_cell(self, value: str) -> QTableWidgetItem:
        """创建文本类型的单元格"""
        item = QTableWidgetItem(str(value))
        item.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        return item


class ChangeDetectionWidget(QWidget):
    """
    异动检测控件
    
    提供异动检测的参数设置和结果展示功能。
    """
    
    # 信号定义
    detection_started = pyqtSignal(dict)   # 检测开始信号
    detection_completed = pyqtSignal(list) # 检测完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.detection_results = []
        self._init_ui()
        
    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建参数设置区域
        self._create_parameters_area(layout)
        
        # 创建结果显示区域
        self._create_results_area(layout)
        
        # 设置样式
        self._set_widget_style()
    
    def _create_parameters_area(self, parent_layout):
        """创建参数设置区域"""
        params_group = QGroupBox("检测参数")
        params_layout = QVBoxLayout(params_group)
        
        # 第一行参数
        row1_layout = QHBoxLayout()
        
        row1_layout.addWidget(QLabel("检测类型:"))
        self.detection_type_combo = QComboBox()
        self.detection_type_combo.addItems([
            "全部异动", "工资增加", "工资减少", "新增人员", "离职人员"
        ])
        row1_layout.addWidget(self.detection_type_combo)
        
        row1_layout.addWidget(QLabel("变化阈值:"))
        self.threshold_spin = QSpinBox()
        self.threshold_spin.setRange(0, 10000)
        self.threshold_spin.setValue(100)
        self.threshold_spin.setSuffix(" 元")
        row1_layout.addWidget(self.threshold_spin)
        
        row1_layout.addStretch()
        params_layout.addLayout(row1_layout)
        
        # 第二行参数
        row2_layout = QHBoxLayout()
        
        self.include_new_check = QCheckBox("包含新增人员")
        self.include_new_check.setChecked(True)
        row2_layout.addWidget(self.include_new_check)
        
        self.include_quit_check = QCheckBox("包含离职人员")
        self.include_quit_check.setChecked(True)
        row2_layout.addWidget(self.include_quit_check)
        
        self.auto_analysis_check = QCheckBox("自动原因分析")
        self.auto_analysis_check.setChecked(True)
        row2_layout.addWidget(self.auto_analysis_check)
        
        row2_layout.addStretch()
        params_layout.addLayout(row2_layout)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.start_detection_btn = QPushButton("开始检测")
        self.start_detection_btn.clicked.connect(self.start_detection)
        button_layout.addWidget(self.start_detection_btn)
        
        self.reset_params_btn = QPushButton("重置参数")
        self.reset_params_btn.clicked.connect(self.reset_parameters)
        button_layout.addWidget(self.reset_params_btn)
        
        button_layout.addStretch()
        params_layout.addLayout(button_layout)
        
        parent_layout.addWidget(params_group)
    
    def _create_results_area(self, parent_layout):
        """创建结果显示区域"""
        results_group = QGroupBox("检测结果")
        results_layout = QVBoxLayout(results_group)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        
        self.total_count_label = QLabel("总计: 0")
        stats_layout.addWidget(self.total_count_label)
        
        self.increase_count_label = QLabel("增加: 0")
        stats_layout.addWidget(self.increase_count_label)
        
        self.decrease_count_label = QLabel("减少: 0")
        stats_layout.addWidget(self.decrease_count_label)
        
        self.new_count_label = QLabel("新增: 0")
        stats_layout.addWidget(self.new_count_label)
        
        stats_layout.addStretch()
        results_layout.addLayout(stats_layout)
        
        # 结果表格
        self.results_table = DataTableWidget()
        results_layout.addWidget(self.results_table)
        
        parent_layout.addWidget(results_group)
    
    def _set_widget_style(self):
        """设置控件样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QLabel {
                color: #333333;
            }
        """)
    
    def get_detection_parameters(self) -> Dict[str, Any]:
        """获取检测参数"""
        return {
            'detection_type': self.detection_type_combo.currentText(),
            'threshold': self.threshold_spin.value(),
            'include_new': self.include_new_check.isChecked(),
            'include_quit': self.include_quit_check.isChecked(),
            'auto_analysis': self.auto_analysis_check.isChecked()
        }
    
    def start_detection(self):
        """开始检测"""
        try:
            params = self.get_detection_parameters()
            self.logger.info(f"开始异动检测，参数: {params}")
            
            # 禁用开始按钮
            self.start_detection_btn.setEnabled(False)
            self.start_detection_btn.setText("检测中...")
            
            # 发射检测开始信号
            self.detection_started.emit(params)
            
            # 模拟检测过程
            safe_single_shot(2000, self._simulate_detection_complete)
            
        except Exception as e:
            self.logger.error(f"开始检测失败: {e}")
            self._reset_detection_ui()
    
    def _simulate_detection_complete(self):
        """模拟检测完成"""
        # 模拟检测结果
        sample_results = [
            {
                "工号": "001", "姓名": "张三", "异动类型": "工资增加",
                "变化金额": 500, "原因分析": "岗位工资调整", "置信度": "95%"
            },
            {
                "工号": "002", "姓名": "李四", "异动类型": "工资减少",
                "变化金额": -200, "原因分析": "绩效扣减", "置信度": "88%"
            },
            {
                "工号": "003", "姓名": "王五", "异动类型": "新增人员",
                "变化金额": 3000, "原因分析": "新入职", "置信度": "100%"
            }
        ]
        
        self.set_detection_results(sample_results)
        self._reset_detection_ui()
    
    def set_detection_results(self, results: List[Dict[str, Any]]):
        """设置检测结果"""
        try:
            self.detection_results = results
            
            # 更新统计信息
            self._update_statistics(results)
            
            # 更新结果表格
            headers = ["工号", "姓名", "异动类型", "变化金额", "原因分析", "置信度"]
            self.results_table.set_data(results, headers)
            
            # 发射检测完成信号
            self.detection_completed.emit(results)
            
            self.logger.info(f"检测结果已更新，共 {len(results)} 条异动")
            
        except Exception as e:
            self.logger.error(f"设置检测结果失败: {e}")
    
    def _update_statistics(self, results: List[Dict[str, Any]]):
        """更新统计信息"""
        total_count = len(results)
        increase_count = len([r for r in results if r.get('异动类型') == '工资增加'])
        decrease_count = len([r for r in results if r.get('异动类型') == '工资减少'])
        new_count = len([r for r in results if r.get('异动类型') == '新增人员'])
        
        self.total_count_label.setText(f"总计: {total_count}")
        self.increase_count_label.setText(f"增加: {increase_count}")
        self.decrease_count_label.setText(f"减少: {decrease_count}")
        self.new_count_label.setText(f"新增: {new_count}")
    
    def reset_parameters(self):
        """重置参数"""
        self.detection_type_combo.setCurrentIndex(0)
        self.threshold_spin.setValue(100)
        self.include_new_check.setChecked(True)
        self.include_quit_check.setChecked(True)
        self.auto_analysis_check.setChecked(True)
        
        self.logger.info("检测参数已重置")
    
    def _reset_detection_ui(self):
        """重置检测UI状态"""
        self.start_detection_btn.setEnabled(True)
        self.start_detection_btn.setText("开始检测")


class ReportPreviewWidget(QWidget):
    """
    报告预览控件
    
    提供报告内容的预览和编辑功能。
    """
    
    # 信号定义
    content_changed = pyqtSignal(str)  # 内容变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.report_content = ""
        self._init_ui()
        
    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建工具栏
        self._create_toolbar(layout)
        
        # 创建预览区域
        self._create_preview_area(layout)
        
        # 设置样式
        self._set_widget_style()
    
    def _create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar_layout = QHBoxLayout()
        
        # 预览模式选择
        toolbar_layout.addWidget(QLabel("预览模式:"))
        self.preview_mode_combo = QComboBox()
        self.preview_mode_combo.addItems(["HTML预览", "纯文本", "Markdown"])
        self.preview_mode_combo.currentTextChanged.connect(self._on_mode_changed)
        toolbar_layout.addWidget(self.preview_mode_combo)
        
        toolbar_layout.addStretch()
        
        # 操作按钮
        self.edit_btn = QPushButton("编辑")
        self.edit_btn.setCheckable(True)
        self.edit_btn.toggled.connect(self._on_edit_toggled)
        toolbar_layout.addWidget(self.edit_btn)
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_preview)
        toolbar_layout.addWidget(self.refresh_btn)
        
        parent_layout.addLayout(toolbar_layout)
    
    def _create_preview_area(self, parent_layout):
        """创建预览区域"""
        # 创建分割器用于编辑和预览模式切换
        self.splitter = QSplitter(Qt.Horizontal)
        
        # 编辑区域
        self.edit_text = QTextEdit()
        self.edit_text.setVisible(False)
        self.edit_text.textChanged.connect(self._on_text_changed)
        self.splitter.addWidget(self.edit_text)
        
        # 预览区域
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.splitter.addWidget(self.preview_text)
        
        # 设置默认内容
        self._set_default_content()
        
        parent_layout.addWidget(self.splitter)
    
    def _set_widget_style(self):
        """设置控件样式"""
        self.setStyleSheet("""
            QTextEdit {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                font-family: 'Microsoft YaHei', 'Consolas';
                font-size: 12px;
                background-color: white;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:checked {
                background-color: #005a9e;
            }
        """)
    
    def _set_default_content(self):
        """设置默认内容"""
        default_html = """
        <h2 style="text-align: center; color: #2c3e50;">报告预览</h2>
        
        <p>这里将显示生成的报告内容。</p>
        
        <h3>功能说明:</h3>
        <ul>
            <li>支持HTML格式预览</li>
            <li>支持纯文本预览</li>
            <li>支持Markdown格式</li>
            <li>支持实时编辑</li>
        </ul>
        
        <p>请选择报告类型和参数，然后点击预览按钮。</p>
        """
        
        self.set_content(default_html, "HTML预览")
    
    def set_content(self, content: str, mode: str = "HTML预览"):
        """设置预览内容"""
        try:
            self.report_content = content
            
            # 设置预览模式
            if mode != self.preview_mode_combo.currentText():
                self.preview_mode_combo.setCurrentText(mode)
            
            # 更新预览内容
            self._update_preview_content()
            
            self.logger.info("报告预览内容已更新")
            
        except Exception as e:
            self.logger.error(f"设置预览内容失败: {e}")
    
    def _update_preview_content(self):
        """更新预览内容"""
        mode = self.preview_mode_combo.currentText()
        
        if mode == "HTML预览":
            self.preview_text.setHtml(self.report_content)
        else:
            self.preview_text.setPlainText(self.report_content)
        
        # 如果处于编辑模式，同步编辑区域
        if self.edit_btn.isChecked():
            self.edit_text.setPlainText(self.report_content)
    
    def _on_mode_changed(self, mode: str):
        """预览模式变化事件"""
        self._update_preview_content()
        self.logger.info(f"预览模式已切换到: {mode}")
    
    def _on_edit_toggled(self, checked: bool):
        """编辑模式切换"""
        if checked:
            # 进入编辑模式
            self.edit_text.setVisible(True)
            self.edit_text.setPlainText(self.report_content)
            self.splitter.setSizes([400, 400])
        else:
            # 退出编辑模式
            self.edit_text.setVisible(False)
            self.splitter.setSizes([0, 800])
    
    def _on_text_changed(self):
        """文本变化事件"""
        if self.edit_btn.isChecked():
            new_content = self.edit_text.toPlainText()
            if new_content != self.report_content:
                self.report_content = new_content
                self._update_preview_content()
                self.content_changed.emit(new_content)
    
    def get_content(self) -> str:
        """获取当前内容"""
        return self.report_content
    
    def refresh_preview(self):
        """刷新预览"""
        try:
            self._update_preview_content()
            self.logger.info("预览已刷新")
        except Exception as e:
            self.logger.error(f"刷新预览失败: {e}")
    
    def clear_content(self):
        """清空内容"""
        self.report_content = ""
        self.preview_text.clear()
        self.edit_text.clear()
        self.logger.info("预览内容已清空") 