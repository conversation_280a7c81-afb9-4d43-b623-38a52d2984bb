#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
立即排序修复验证脚本

用于验证恢复sortIndicatorChanged信号连接后的排序功能
"""

import sys
import os
import ast
import py_compile
from pathlib import Path

def check_syntax():
    """检查修改后的文件语法"""
    print("🔍 检查修改后的文件语法...")
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    try:
        # 语法检查
        py_compile.compile(file_path, doraise=True)
        print("✅ 语法检查通过")
        
        # AST解析检查
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        print("✅ AST解析通过")
        
        return True
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def verify_signal_connection_fix():
    """验证信号连接修复"""
    print("\n🔍 验证sortIndicatorChanged信号连接修复...")
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = {
        "sortIndicatorChanged.connect": "sortIndicatorChanged.connect(" in content,
        "_on_sort_indicator_changed": "_on_sort_indicator_changed" in content,
        "P0-CRITICAL修复": "P0-CRITICAL修复" in content,
        "恢复立即排序功能": "恢复立即排序功能" in content,
        "_get_main_window": "_get_main_window()" in content
    }
    
    all_passed = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}: {'通过' if result else '失败'}")
        if not result:
            all_passed = False
    
    return all_passed

def analyze_fix_implementation():
    """分析修复实现的细节"""
    print("\n🔍 分析修复实现...")
    
    file_path = "src/gui/prototype/widgets/virtualized_expandable_table.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 查找修复代码段
    in_fix_section = False
    fix_lines = []
    line_num = 0
    
    for i, line in enumerate(lines):
        line_num = i + 1
        if "恢复sortIndicatorChanged信号连接解决排序问题" in line:
            in_fix_section = True
            fix_lines.append((line_num, line.strip()))
        elif in_fix_section and ("header.sectionClicked.connect" in line or "保留自定义表头点击处理器" in line):
            fix_lines.append((line_num, line.strip()))
            break
        elif in_fix_section:
            fix_lines.append((line_num, line.strip()))
    
    if fix_lines:
        print("✅ 找到修复代码段:")
        for line_num, line in fix_lines:
            if line.strip():
                print(f"  第{line_num}行: {line}")
    else:
        print("❌ 未找到修复代码段")
        return False
    
    # 检查关键要素
    fix_text = ' '.join([line for _, line in fix_lines])
    
    key_elements = {
        "获取主窗口": "_get_main_window()" in fix_text,
        "检查方法存在": "_on_sort_indicator_changed" in fix_text,
        "连接信号": "sortIndicatorChanged.connect" in fix_text,
        "QueuedConnection": "Qt.QueuedConnection" in fix_text,
        "成功日志": "恢复立即排序功能" in fix_text,
        "错误处理": "warning" in fix_text.lower() or "无法获取主窗口" in fix_text
    }
    
    print("\n📋 关键要素检查:")
    all_passed = True
    for element, passed in key_elements.items():
        status = "✅" if passed else "❌"
        print(f"  {status} {element}")
        if not passed:
            all_passed = False
    
    return all_passed

def create_manual_test_guide():
    """创建手动测试指南"""
    print("\n📝 生成手动测试指南...")
    
    test_guide = """
# 🧪 立即排序功能测试指南

## 测试前提条件
✅ 确保系统已启动
✅ 确保已导入测试数据
✅ 确保表格中有数据显示

## 测试步骤

### 测试1: 基本排序功能
1. 🎯 **操作**: 点击任意列的表头
2. 🔍 **预期**: 
   - 立即看到排序指示器（▲ 或 ▼）
   - 数据立即按该列排序显示
   - **无需任何额外操作**
3. ⏱️ **响应时间**: < 100ms

### 测试2: 多次排序切换
1. 🎯 **操作**: 连续点击同一列表头2-3次
2. 🔍 **预期**: 
   - 每次点击都立即切换排序方向
   - 升序(▲) ↔ 降序(▼) 切换
   - 数据每次都立即重新排序

### 测试3: 不同列排序
1. 🎯 **操作**: 点击不同列的表头进行排序
2. 🔍 **预期**: 
   - 每列都能立即排序
   - 排序指示器正确显示在当前排序列
   - 其他列的排序指示器清除

### 测试4: 单页数据排序
1. 🎯 **条件**: 确保数据少于50条（单页显示）
2. 🎯 **操作**: 点击表头排序
3. 🔍 **预期**: 
   - 立即看到排序结果
   - **不需要点击刷新按钮**

### 测试5: 多页数据排序
1. 🎯 **条件**: 确保数据多于50条（多页显示）
2. 🎯 **操作**: 点击表头排序
3. 🔍 **预期**: 
   - 当前页立即按排序显示
   - **不需要点击下一页按钮**
   - 分页导航时保持排序状态

## 成功标准
- ✅ 所有排序操作响应时间 < 100ms
- ✅ 无需额外的刷新或分页操作
- ✅ 排序指示器正确显示
- ✅ 数据正确排序
- ✅ 无错误或异常

## 失败症状（如果修复无效）
- ❌ 点击表头后无立即响应
- ❌ 需要点击刷新/分页才能看到排序
- ❌ 排序指示器显示但数据未排序
- ❌ 出现错误对话框或日志错误

## 日志监控
在测试过程中，查看日志中的关键信息：
- ✅ "sortIndicatorChanged信号已连接到主窗口"
- ✅ "恢复立即排序功能"
- ✅ "全局排序 排序请求"
- ❌ 如果看到"无法获取主窗口"则表示连接失败

## 回滚方案
如果修复无效，可以还原之前的备份文件。
"""
    
    with open("temp/manual_test_guide.md", "w", encoding="utf-8") as f:
        f.write(test_guide)
    
    print("✅ 测试指南已生成: temp/manual_test_guide.md")

def main():
    """主函数"""
    print("🚀 立即排序修复验证开始")
    print("=" * 50)
    
    # 检查语法
    if not check_syntax():
        print("\n❌ 语法检查失败，请先修复语法错误")
        return False
    
    # 验证信号连接修复
    if not verify_signal_connection_fix():
        print("\n❌ 信号连接修复验证失败")
        return False
    
    # 分析修复实现
    if not analyze_fix_implementation():
        print("\n❌ 修复实现分析失败")
        return False
    
    # 生成测试指南
    create_manual_test_guide()
    
    print("\n" + "=" * 50)
    print("🎉 修复验证完成！")
    print("\n📋 修复摘要:")
    print("✅ 已恢复 sortIndicatorChanged 信号连接")
    print("✅ 连接到主窗口的 _on_sort_indicator_changed 方法")
    print("✅ 使用 Qt.QueuedConnection 确保线程安全")
    print("✅ 保留备用的自定义排序处理器")
    print("✅ 添加了完整的错误处理和日志")
    
    print("\n🎯 下一步:")
    print("1. 启动系统测试排序功能")
    print("2. 参考 temp/manual_test_guide.md 进行测试")
    print("3. 验证点击表头立即排序无需额外操作")
    
    print("\n💡 预期效果:")
    print("- 点击表头 → 立即排序（< 100ms）")
    print("- 无需点击刷新或分页按钮")
    print("- 完全符合用户预期的即时响应")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)