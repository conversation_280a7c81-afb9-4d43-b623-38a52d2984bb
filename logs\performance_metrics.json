{"session_start_time": "2025-08-12T10:34:27.458349", "last_updated": "2025-08-12T10:38:58.631317", "metrics": [{"operation_type": "render", "data_size": 50, "render_time_ms": 10.755300521850586, "strategy_used": "small_dataset", "user_wait_time_ms": 10.755300521850586, "timestamp": "2025-08-12T10:34:27.460419", "table_name": "", "additional_info": {"avg_ms_per_row": 0.21510601043701172}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.655279159545898, "strategy_used": "small_dataset", "user_wait_time_ms": 15.655279159545898, "timestamp": "2025-08-12T10:34:40.660492", "table_name": "", "additional_info": {"avg_ms_per_row": 0.31310558319091797}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 16.66545867919922, "strategy_used": "small_dataset", "user_wait_time_ms": 16.66545867919922, "timestamp": "2025-08-12T10:34:45.040951", "table_name": "", "additional_info": {"avg_ms_per_row": 0.3333091735839844}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 12.036800384521484, "strategy_used": "small_dataset", "user_wait_time_ms": 12.036800384521484, "timestamp": "2025-08-12T10:34:45.432150", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2407360076904297}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 13.864755630493164, "strategy_used": "small_dataset", "user_wait_time_ms": 13.864755630493164, "timestamp": "2025-08-12T10:34:47.685874", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2772951126098633}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 14.031648635864258, "strategy_used": "small_dataset", "user_wait_time_ms": 14.031648635864258, "timestamp": "2025-08-12T10:34:48.059027", "table_name": "", "additional_info": {"avg_ms_per_row": 0.28063297271728516}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 0.0, "strategy_used": "small_dataset", "user_wait_time_ms": 0.0, "timestamp": "2025-08-12T10:34:55.830702", "table_name": "", "additional_info": {"avg_ms_per_row": 0.0}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.68603515625, "strategy_used": "small_dataset", "user_wait_time_ms": 15.68603515625, "timestamp": "2025-08-12T10:35:03.511262", "table_name": "", "additional_info": {"avg_ms_per_row": 0.313720703125}}, {"operation_type": "render", "data_size": 12, "render_time_ms": 3.1015872955322266, "strategy_used": "small_dataset", "user_wait_time_ms": 3.1015872955322266, "timestamp": "2025-08-12T10:35:05.730990", "table_name": "", "additional_info": {"avg_ms_per_row": 0.25846560796101886}}, {"operation_type": "render", "data_size": 12, "render_time_ms": 2.6171207427978516, "strategy_used": "small_dataset", "user_wait_time_ms": 2.6171207427978516, "timestamp": "2025-08-12T10:35:06.012968", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2180933952331543}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 16.637802124023438, "strategy_used": "small_dataset", "user_wait_time_ms": 16.637802124023438, "timestamp": "2025-08-12T10:35:21.112918", "table_name": "", "additional_info": {"avg_ms_per_row": 0.33275604248046875}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 13.936042785644531, "strategy_used": "small_dataset", "user_wait_time_ms": 13.936042785644531, "timestamp": "2025-08-12T10:35:22.897742", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2787208557128906}}, {"operation_type": "render", "data_size": 12, "render_time_ms": 0.0, "strategy_used": "small_dataset", "user_wait_time_ms": 0.0, "timestamp": "2025-08-12T10:35:50.794137", "table_name": "", "additional_info": {"avg_ms_per_row": 0.0}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 11.754035949707031, "strategy_used": "small_dataset", "user_wait_time_ms": 11.754035949707031, "timestamp": "2025-08-12T10:35:51.016266", "table_name": "", "additional_info": {"avg_ms_per_row": 0.23508071899414062}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 10.787487030029297, "strategy_used": "small_dataset", "user_wait_time_ms": 10.787487030029297, "timestamp": "2025-08-12T10:35:51.571839", "table_name": "", "additional_info": {"avg_ms_per_row": 0.21574974060058594}}, {"operation_type": "render", "data_size": 12, "render_time_ms": 2.605438232421875, "strategy_used": "small_dataset", "user_wait_time_ms": 2.605438232421875, "timestamp": "2025-08-12T10:35:51.781748", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2171198527018229}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 0.0, "strategy_used": "small_dataset", "user_wait_time_ms": 0.0, "timestamp": "2025-08-12T10:36:05.505087", "table_name": "", "additional_info": {"avg_ms_per_row": 0.0}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.649557113647461, "strategy_used": "small_dataset", "user_wait_time_ms": 15.649557113647461, "timestamp": "2025-08-12T10:36:07.125517", "table_name": "", "additional_info": {"avg_ms_per_row": 0.3129911422729492}}, {"operation_type": "render", "data_size": 12, "render_time_ms": 0.0, "strategy_used": "small_dataset", "user_wait_time_ms": 0.0, "timestamp": "2025-08-12T10:36:22.081031", "table_name": "", "additional_info": {"avg_ms_per_row": 0.0}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 13.61393928527832, "strategy_used": "small_dataset", "user_wait_time_ms": 13.61393928527832, "timestamp": "2025-08-12T10:36:22.279949", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2722787857055664}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 13.662338256835938, "strategy_used": "small_dataset", "user_wait_time_ms": 13.662338256835938, "timestamp": "2025-08-12T10:36:22.843512", "table_name": "", "additional_info": {"avg_ms_per_row": 0.27324676513671875}}, {"operation_type": "render", "data_size": 12, "render_time_ms": 3.662586212158203, "strategy_used": "small_dataset", "user_wait_time_ms": 3.662586212158203, "timestamp": "2025-08-12T10:36:23.064152", "table_name": "", "additional_info": {"avg_ms_per_row": 0.3052155176798503}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.197992324829102, "strategy_used": "small_dataset", "user_wait_time_ms": 15.197992324829102, "timestamp": "2025-08-12T10:36:46.084014", "table_name": "", "additional_info": {"avg_ms_per_row": 0.30395984649658203}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 0.0, "strategy_used": "small_dataset", "user_wait_time_ms": 0.0, "timestamp": "2025-08-12T10:36:54.466994", "table_name": "", "additional_info": {"avg_ms_per_row": 0.0}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 13.302803039550781, "strategy_used": "small_dataset", "user_wait_time_ms": 13.302803039550781, "timestamp": "2025-08-12T10:37:06.557938", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2660560607910156}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 12.273311614990234, "strategy_used": "small_dataset", "user_wait_time_ms": 12.273311614990234, "timestamp": "2025-08-12T10:37:06.729032", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2454662322998047}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 16.164779663085938, "strategy_used": "small_dataset", "user_wait_time_ms": 16.164779663085938, "timestamp": "2025-08-12T10:37:07.261922", "table_name": "", "additional_info": {"avg_ms_per_row": 0.32329559326171875}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 8.811235427856445, "strategy_used": "small_dataset", "user_wait_time_ms": 8.811235427856445, "timestamp": "2025-08-12T10:37:07.512159", "table_name": "", "additional_info": {"avg_ms_per_row": 0.1762247085571289}}, {"operation_type": "render", "data_size": 13, "render_time_ms": 15.65861701965332, "strategy_used": "small_dataset", "user_wait_time_ms": 15.65861701965332, "timestamp": "2025-08-12T10:37:20.851435", "table_name": "", "additional_info": {"avg_ms_per_row": 1.2045090015117939}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.661001205444336, "strategy_used": "small_dataset", "user_wait_time_ms": 15.661001205444336, "timestamp": "2025-08-12T10:37:22.066296", "table_name": "", "additional_info": {"avg_ms_per_row": 0.3132200241088867}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.202999114990234, "strategy_used": "small_dataset", "user_wait_time_ms": 15.202999114990234, "timestamp": "2025-08-12T10:37:25.255700", "table_name": "", "additional_info": {"avg_ms_per_row": 0.3040599822998047}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 12.819766998291016, "strategy_used": "small_dataset", "user_wait_time_ms": 12.819766998291016, "timestamp": "2025-08-12T10:37:25.453790", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2563953399658203}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 12.638568878173828, "strategy_used": "small_dataset", "user_wait_time_ms": 12.638568878173828, "timestamp": "2025-08-12T10:37:26.004472", "table_name": "", "additional_info": {"avg_ms_per_row": 0.25277137756347656}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 10.350227355957031, "strategy_used": "small_dataset", "user_wait_time_ms": 10.350227355957031, "timestamp": "2025-08-12T10:37:26.257394", "table_name": "", "additional_info": {"avg_ms_per_row": 0.20700454711914062}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.665531158447266, "strategy_used": "small_dataset", "user_wait_time_ms": 15.665531158447266, "timestamp": "2025-08-12T10:37:33.850757", "table_name": "", "additional_info": {"avg_ms_per_row": 0.3133106231689453}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 20.433902740478516, "strategy_used": "small_dataset", "user_wait_time_ms": 20.433902740478516, "timestamp": "2025-08-12T10:37:34.053250", "table_name": "", "additional_info": {"avg_ms_per_row": 0.4086780548095703}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 12.842655181884766, "strategy_used": "small_dataset", "user_wait_time_ms": 12.842655181884766, "timestamp": "2025-08-12T10:37:34.576412", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2568531036376953}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 11.855125427246094, "strategy_used": "small_dataset", "user_wait_time_ms": 11.855125427246094, "timestamp": "2025-08-12T10:37:34.818846", "table_name": "", "additional_info": {"avg_ms_per_row": 0.23710250854492188}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 16.146183013916016, "strategy_used": "small_dataset", "user_wait_time_ms": 16.146183013916016, "timestamp": "2025-08-12T10:37:41.601909", "table_name": "", "additional_info": {"avg_ms_per_row": 0.3229236602783203}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 13.451576232910156, "strategy_used": "small_dataset", "user_wait_time_ms": 13.451576232910156, "timestamp": "2025-08-12T10:37:41.792708", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2690315246582031}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 11.726140975952148, "strategy_used": "small_dataset", "user_wait_time_ms": 11.726140975952148, "timestamp": "2025-08-12T10:37:42.330830", "table_name": "", "additional_info": {"avg_ms_per_row": 0.23452281951904297}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 11.835336685180664, "strategy_used": "small_dataset", "user_wait_time_ms": 11.835336685180664, "timestamp": "2025-08-12T10:37:42.576065", "table_name": "", "additional_info": {"avg_ms_per_row": 0.23670673370361328}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 0.0, "strategy_used": "small_dataset", "user_wait_time_ms": 0.0, "timestamp": "2025-08-12T10:38:33.345611", "table_name": "", "additional_info": {"avg_ms_per_row": 0.0}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.665769577026367, "strategy_used": "small_dataset", "user_wait_time_ms": 15.665769577026367, "timestamp": "2025-08-12T10:38:34.453896", "table_name": "", "additional_info": {"avg_ms_per_row": 0.31331539154052734}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 11.282682418823242, "strategy_used": "small_dataset", "user_wait_time_ms": 11.282682418823242, "timestamp": "2025-08-12T10:38:39.997872", "table_name": "", "additional_info": {"avg_ms_per_row": 0.22565364837646484}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 13.666152954101562, "strategy_used": "small_dataset", "user_wait_time_ms": 13.666152954101562, "timestamp": "2025-08-12T10:38:40.197295", "table_name": "", "additional_info": {"avg_ms_per_row": 0.27332305908203125}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 11.47913932800293, "strategy_used": "small_dataset", "user_wait_time_ms": 11.47913932800293, "timestamp": "2025-08-12T10:38:40.712719", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2295827865600586}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 11.373519897460938, "strategy_used": "small_dataset", "user_wait_time_ms": 11.373519897460938, "timestamp": "2025-08-12T10:38:40.958766", "table_name": "", "additional_info": {"avg_ms_per_row": 0.22747039794921875}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 30.89118003845215, "strategy_used": "small_dataset", "user_wait_time_ms": 30.89118003845215, "timestamp": "2025-08-12T10:38:53.231226", "table_name": "", "additional_info": {"avg_ms_per_row": 0.617823600769043}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 10.762691497802734, "strategy_used": "small_dataset", "user_wait_time_ms": 10.762691497802734, "timestamp": "2025-08-12T10:38:58.630307", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2152538299560547}}], "stats": {"total_operations": 50, "avg_render_time": 11.477642059326172, "max_render_time": 30.89118003845215, "min_render_time": 0.0, "operations_by_type": {"render": {"count": 50, "avg_time": 11.477642059326172, "total_time": 573.8821029663086}}, "performance_improvements": {}}}