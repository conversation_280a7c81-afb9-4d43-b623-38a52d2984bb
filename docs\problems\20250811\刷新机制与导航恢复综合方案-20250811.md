### 刷新机制与导航恢复综合方案（2025-08-11）

#### 背景与问题概述
- 实测与日志显示：点击“刷新”后，导航树会被重建但未可靠恢复展开状态，出现“路径应展开但未展开”的一致性告警，用户看到子导航折叠且点击根节点难以展开，导致无法在不同表间切换。
- 现有三个“刷新”按钮职责不清：
  - 分页组件刷新当前会触发全局刷新（越权）。
  - 表格工具栏“刷新数据”被重复绑定到“全局刷新+表格刷新”两个槽（范围冲突）。
  - 顶部控制面板“刷新数据”为全局刷新入口（预期合理）。
- 初始化阶段多处触发“备用空表”导致闪屏与误判；全局刷新阶段统计“已完成阶段数”和判定口径存在不一致。

#### 目标
- 按钮职责“各司其职”：
  - 分页刷新：仅刷新表格分页数据，不影响导航与样式等全局状态。
  - 表格刷新：仅刷新当前表格数据，不影响导航与样式等全局状态。
  - 全局刷新：可重建导航、刷新样式与状态、执行一致性与恢复补偿。
- 修复“刷新后导航无法展开”的根因：清理映射、可靠恢复、补偿展开、兜底自动选择、时序与交互控制。
- 引入“就绪检测网关”，统一初始化与全局刷新触发时机，避免闪屏与误判。
- 强化状态管理健壮性与日志可观测性，提供清晰测试与验收标准。

---

## 一、刷新按钮职责与命名定稿（必达）

- 分页刷新（PaginationWidget）
  - 新名称：分页刷新
  - 职责：仅刷新当前表格分页数据，不调用全局刷新，不触达导航或样式。
  - 行为约束：`_on_pagination_refresh` 不得调用 `_on_refresh_data()`；应仅调用表格数据/分页的局部重载 API（如 `table_data_service.refresh_table_data(current_table)` 或 `_reload_current_page()`）。

- 表格刷新（概览页表格工具栏）
  - 新名称：表格刷新
  - 职责：仅对当前表格执行数据重载与渲染（可含字段映射），不触达导航与样式。
  - 行为约束：工具栏 `refresh_btn` 仅绑定 `_refresh_table_data`，不得再绑定 `_on_refresh_data`（去重）。

- 全局刷新（顶部控制面板）
  - 新名称：全局刷新
  - 职责：执行综合刷新管线，包括就绪检测、导航树强制刷新/完整刷新、状态恢复与补偿、样式与表头管理同步、最终一致性验证与反馈。
  - 行为约束：全局刷新是唯一允许影响导航/样式/全局状态的入口。

日志打点（建议）：
- 分页刷新：tag=refresh.scope=page
- 表格刷新：tag=refresh.scope=table
- 全局刷新：tag=refresh.scope=global

---

## 二、导航刷新与恢复的可靠机制（修复主线）

核心链路：清空树 → 清理映射 → 重建 → 恢复展开与选择 → 一致性校验 → 补偿展开 → 兜底自动选择 → 最终复核。

1) 清空与映射清理的原子性
- 在 `QTreeWidget.clear()` 之后，立刻调用 `SmartTreeWidget.clear_mappings()`，确保 `path_item_map/item_path_map` 与 UI 同步清零，避免后续 `expand_path` 命中“旧引用”。

2) 重建完成后的状态恢复与补偿
- 先执行 `_restore_navigation_state()` 恢复历史 `expanded_paths/selected_path`。
- 立即调用 `_verify_navigation_state_consistency()`；若存在“路径应展开但未展开”，逐一 `expand_path` 进行补偿，再进行一次短延迟（100~200ms）复核。
- 若当前选择无效或为空，执行 `_post_refresh_auto_select()` 或 `auto_select_latest_data()`，逐级展开“最新月份 → 具体表项”，保证用户可立即使用。

3) 时序与交互控制
- 清空/重建/恢复阶段短暂禁用 `tree_widget`（或显示忙碌指示），完成后恢复启用，避免空窗期点击造成“无反应”的错觉。
- 含 `safe_single_shot` 的延迟逻辑采用合并调度（单次复核），降低刷新闪烁与竞态。

4) 日志与一致性
- 状态恢复后再次验证：若仍存在“未展开”路径，记录一次性 WARNING，并继续兜底自动选择，减少日志噪音、保留诊断线索。

---

## 三、初始化“就绪检测网关”（新增）

目的：统一管理“初始化/全局刷新”触发时机，避免数据库未就绪或数据源同步期触发“兜底空表”，引起闪屏与误判。

判定维度（示例）：
- 数据库连接与表元数据就绪（`DynamicTableManager.is_database_ready()`）。
- `get_navigation_tree_data()` 能返回结构化数据（或明确无数据而非暂不可用）。
- 关键组件注入完成（如 `navigation_panel`、`table_data_service` 等）。

策略：
- 未就绪：显示“加载中/等待就绪”提示，禁用导航交互，安排短延迟重试。
- 超时（如 3~5s）：显示“暂无数据/请导入”，此时才允许一次性启用兜底空表，且不重复闪屏。
- 就绪：触发“强制刷新 → 恢复 → 预测/自动选择”全流程。

集成点：
- `enhanced_navigation_panel._load_dynamic_salary_data()` 与 `_delayed_load_salary_data()` 将延迟逻辑统一托管给网关判定。
- `prototype_main_window._execute_global_state_refresh()` 的阶段1“预检查”复用同一网关。

---

## 四、状态管理与阶段统计的小修

- 统一状态管理器 `UnifiedStateManager.get_state()` 访问了 `self.global_state.custom_data`，但 `GlobalState` 未声明该字段。建议：
  - 为 `GlobalState` 增加 `custom_data: Dict[str, Any] = field(default_factory=dict)`；或调整 `get_state()` 的读取逻辑到已存在字段，避免潜在 AttributeError。

- 全局刷新阶段统计一致性：
  - 明确阶段进入/完成的计数口径，`success=True` 必须满足“完成数==总阶段数且 error_count==0”。

---

## 五、实施步骤（不立即改代码，供后续实施）

变更范围与落点：
- `src/gui/prototype/prototype_main_window.py`
  - 分页刷新：重写 `_on_pagination_refresh` 为“仅表格分页数据重载”，移除调用 `_on_refresh_data()`。
  - 表格刷新：解绑工具栏 `refresh_btn` 与 `_on_refresh_data` 的连接，仅保留 `_refresh_table_data`。
  - 全局刷新：保持 `_on_refresh_data()` → `_execute_global_state_refresh()` 管线不变；阶段1加入“就绪检测网关”。
  - 按钮文本修改：将三处按钮文本分别命名为“分页刷新”“表格刷新”“全局刷新”。

- `src/gui/prototype/widgets/enhanced_navigation_panel.py`
  - 在 `refresh_navigation_data()` 与 `force_refresh_salary_data()` 中：
    - `tree_widget.clear()` 之后调用 `tree_widget.clear_mappings()`；
    - 重建后执行“恢复→校验→补偿→兜底→复核”的完整链路；
    - 清空/重建期禁用 `tree_widget`，结束后启用。
  - `_verify_navigation_state_consistency()`：提供未展开路径列表的调用返回，供补偿逻辑使用。

- “就绪检测网关”（新增模块/函数）
  - 建议放置：`src/core/readiness_gateway.py` 或纳入 `src/modules/data_storage/`。
  - 暴露 `is_system_ready(timeout_policy)` 与原因诊断；供导航面板与全局刷新共用。

- `src/core/unified_state_manager.py`
  - 为 `GlobalState` 增加 `custom_data` 字段或修正 `get_state()` 访问。
  - 全局刷新阶段统计与 success 判定口径统一。

---

## 六、验收标准（可操作）

1) 按钮职责：
- 点击“分页刷新”：仅当前页数据刷新，导航展开与样式完全不变；日志仅出现 scope=page。
- 点击“表格刷新”：当前表数据集刷新，停留在同一表，导航展开与样式不变；日志仅出现 scope=table。
- 点击“全局刷新”：执行“就绪→重建→恢复→补偿→复核”完整链路，最终导航可用、可展开，日志 scope=global 且无一致性告警。

2) 导航一致性：
- 刷新后“路径应展开但未展开”的 WARNING 为 0；如偶发，补偿后不再出现且用户可正常展开。

3) 初始化体验：
- 启动期间若未就绪，不显示兜底空表，不闪屏；就绪后一次性构建并恢复可用导航。

4) 稳定性：
- `UnifiedStateManager.get_state()` 不会抛出 AttributeError；全局刷新日志“完成阶段数==总阶段数”。

---

## 七、测试用例与操作指引

手工测试（关键场景）：
- 切到第2页→点击“分页刷新”：页码与当前页数据重载，导航/样式不变。
- 选择“2025年>5月>全部在职人员”→点击“表格刷新”：数据更新、仍停留当前表，导航展开/选择不变。
- 点击“全局刷新”：导航重建与恢复，最终可展开并可在不同表间切换，无一致性告警。
- 应用启动→未就绪阶段：显示等待提示，不出现兜底空表；就绪后一次性正常展示。

自动化测试（建议放置于 `test/`）：
- `test_navigation_refresh_restore.py`
  - `test_clear_and_rebuild_keeps_expandable`
  - `test_compensation_expands_missing_paths`
  - `test_auto_select_latest_path_as_fallback`
- `test_readiness_gateway.py`
  - `test_ready_allows_build_without_fallback`
  - `test_not_ready_blocks_fallback_and_shows_wait`
- `test_button_refresh_scopes.py`
  - `test_pagination_refresh_is_local_only`
  - `test_table_toolbar_refresh_is_local_only`
  - `test_global_refresh_runs_full_pipeline`
- `test_unified_state_manager_safety.py`
  - `test_get_state_no_attribute_error`

---

## 八、Mermaid 流程图与时序图

整体全局刷新流程（含就绪网关与恢复补偿）：

```mermaid
flowchart TD
  A[全局刷新] --> B{就绪检测网关 is_system_ready?}
  B -- 否 --> C[显示加载提示/禁用导航交互/延迟重试或超时降级]
  C -->|就绪| B
  B -- 是 --> D[导航树清空]
  D --> E[clear_mappings 清理映射]
  E --> F[重建年份/月/表节点]
  F --> G[恢复展开与选择]
  G --> H{一致性校验通过?}
  H -- 否 --> I[补偿展开未展开路径]
  I --> J[短延迟复核一致性]
  H -- 是 --> J
  J --> K{当前选择有效?}
  K -- 否 --> L[自动选择最新路径并逐级展开]
  K -- 是 --> M[完成]
  L --> M[完成]
```

三类刷新按钮的职责范围：

```mermaid
flowchart LR
  P[分页刷新] -- 仅表格分页数据 --> TBL[表格/分页层]
  TB[表格刷新] -- 仅当前表数据集 --> TBL
  GLB[全局刷新] -- 导航/样式/状态 --> NAV[导航面板]
  GLB --> UI[样式/表头/状态管理]
  style P fill:#E3F2FD,stroke:#64B5F6
  style TB fill:#E8F5E9,stroke:#81C784
  style GLB fill:#FFF8E1,stroke:#FFD54F
```

全局刷新关键时序：

```mermaid
sequenceDiagram
  participant U as 用户
  participant M as PrototypeMainWindow
  participant N as EnhancedNavigationPanel
  participant T as SmartTreeWidget

  U->>M: 点击全局刷新
  M->>M: 就绪检测网关
  alt 未就绪
    M-->>U: 显示等待/禁用交互
    M->>M: 延迟重试或超时降级
  end
  M->>N: force/refresh_navigation_data
  N->>T: clear()
  N->>T: clear_mappings()
  N->>T: 重建节点
  N->>N: 恢复展开/选择
  N->>N: 一致性校验
  alt 未通过
    N->>T: 补偿展开
    N->>N: 短延迟复核
  end
  alt 当前选择无效
    N->>N: 自动选择最新路径
  end
  N-->>M: 导航恢复完成
  M-->>U: UI可用，可切换表
```

---

## 九、风险与回滚
- 若补偿展开仍失败：保留一次性 WARNING，自动选择最近活跃路径作为兜底，确保用户可用；收集统计便于后续定位。
- 若就绪网关长时间未就绪：明确超时降级策略，仅一次触发兜底空表，防止闪屏。
- 全局刷新失败：不影响分页/表格刷新的局部能力；用户仍可用微刷新作为退路。

---

## 十、文件与模块影响范围
- 交互与刷新：`src/gui/prototype/prototype_main_window.py`
- 导航面板与树控件：`src/gui/prototype/widgets/enhanced_navigation_panel.py`
- 就绪检测网关（新增）：`src/core/readiness_gateway.py`（建议）
- 统一状态管理器：`src/core/unified_state_manager.py`
- 测试与验收：`test/*.py`

---

#### 结语
本方案明确三类刷新按钮的职责边界，引入就绪检测网关与导航恢复补偿链路，配合状态与日志的稳健化，既解决“刷新后导航无法展开”的核心问题，也为后续演进提供清晰、可验证的边界与流程。


