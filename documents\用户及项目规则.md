



# CLAUDE.md 用户及项目规则
---
# 所有交互回答都使用中文
# 全程使用中文对话！
# 每次都用审视的目光，仔看我输入的潜在问题，你要指出我的问题，并给出明显在我思考框架之外的建议
# 如果你觉得我说的太离谱了，你就骂回来，帮我瞬间清醒


# 启动并行的代理执行任务

# 搜索约定
- 如需简单字符串匹配 → Grep
- 如需结构化/跨语言模式匹配 → 请优先使用 Bash（ast-grep .），示例：Bash(ast-grep -p '$FUNC($ARG)' src/)



# 经验教训：
- 永远不要让文件名和目录名相同
- 遇到导入错误要先检查模块结构，而不是盲目修改导入语句
- 设计项目结构时要考虑Python的模块解析规则
- 代码文件代码行数不应该太多


# 环境约定
- python环境：python3 pip3
- nodejs环境：node npm
- docker环境：docker


---










## 核心要求
- 当前操作系统为windows10，终端以 powershell 为主，在使用一些命令时，一定要注意操作系统以及终端的区别。
- 除非特别说明，默认使用简体中文回复。
- 在每次内容变动完成后，检查一下，看看是否存在语法错误，是否存在遗漏，是否存在重复，是否存在中文乱码（注意：绝对不能出现中文乱码！）。
- 编写简洁、技术性强且带有准确示例的回复。
- 回复中如果涉及当前项目内容，请给出相关内容在项目中的准确位置（文件路径）。
- 要修改当前项目的文件时，在回复中给出修改的原因。
- 在回复中，如果需要给出代码示例，请给出完整的代码示例，不要只给出部分代码，并且给出文件名和文件路径。
- 生成文件时，要思考按照“文档位置约定”文件应放置到哪个目录下。

## 复杂问题处理
- 在处理复杂问题或需求任务的回复中，先给出整体分析思路，再给出详细解决方案、操作步骤、注意事项，以及目录结构、文件功能说明，待用户确认后，分步给出具体实现代码。
- 解决同一问题，如果经过连续2次处理还没有完全解决：
    - 就要对前2次处理情况进行总结，以独立markdown格式文档保存到目录 docs/problems 下，文件名以中文命名。
    - 重新梳理思路，给出3种不同方案，对比优缺点，让用户进行选择。

## 方案指南
- 每次生成或修改方案时，给出该方案的mermaid流程图和时序图代码。
- 在具体实现方案时，要分步实施，一步一步来。
    - 一个功能模块完全完成，再开始下一个功能模块，避免生成一堆没有完全实现的功能模块。
    - 尽可能以功能相对独立的函数来实现，函数代码量尽可能不超过300行。

## 编程思路
- 先想边界情况再写主逻辑。
- 别整太复杂，宁可多写两行。
- 代码就是文档，自己得能看明白。
- 同样的代码出现三次就抽函数。

## 编程规矩
- 代码必须遵循SOLID原则。
- 每个函数只干一件事。
- 变量名要说人话（data→userList），函数名要能看出来干啥的。
- 变量作用域越小越好，别整全局的。
- 所有异常必须处理，出错时提示要有用。
- 项目中日志管理要统一。
- 注释是写给半年后的自己看的。

## 代码评审
- 看到能复用的代码就提醒我。
- 检查代码边界情况。
- 具体实现代码完成后，使用 .cursor/rules 中相应代码规则对具体代码进行检查，改进不符合规则的代码。

## 文档位置约定
- 如无特别说明，统一以当前项目根目录作为起始目录。
- 项目需求文档，如无特别说明，以独立markdown格式文档统一放置在项目根目录下的 docs/prd 目录中，以简体中文命名文件。
- 项目说明文档、功能文档、架构图、依赖关系等文档，如无特别说明，以独立markdown格式文档统一放置在项目根目录下的 docs/draft 目录中，以简体中文命名文件。
- 项目问题记录等文档，如无特别说明，以独立markdown格式文档统一放置在项目根目录下的 docs/problems 目录中，以简体中文命名文件。
- 项目高保真原型图相关文档，如无特别说明，统一放置在项目根目录下的 docs/design 目录中。
- 测试文件，统一放置在项目根目录下的 test 目录中。
- 临时文件，统一放置在项目根目录下的 temp 目录中。
- 示例文档，统一放置在项目根目录下的 docs/examples 目录中。
- 输入文件（数据文件），统一放置在项目根目录下的 data 目录中。
- 输出文件，统一放置在项目根目录下的 output 目录中。
- 日志文件，统一放置在项目根目录下的 logs 目录中。
- 部署文档，统一放置在项目根目录下的 deploy 目录中。




