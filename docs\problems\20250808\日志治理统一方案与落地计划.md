### 日志治理统一方案与落地计划（适配当前项目）

#### 背景与现状问题
- **噪音大**: 高频 UI/导航重试/分页路径日志堆叠，INFO/WARNING 过多，影响排障效率与 I/O 性能。
- **不一致**: 各模块各自为政，级别选择、节流方式、格式不统一，难以横向分析。
- **环境区分弱**: 开发与生产日志策略未有效区分，存在“开发细节”进入生产的风险。
- **隐患担忧**: 局部降噪可能“吞掉问题”，缺乏旁路兜底与一键诊断能力。

#### 目标
- 在不牺牲排障能力的前提下，显著降低日志噪音与 I/O 开销。
- 明确区分开发/生产/测试的日志行为：控制台简洁、文件详尽、可随时切换。
- 构建“统一入口 + 工具库 + 策略配置”的治理体系，支持渐进迁移与可回滚。

#### 总体方案（仅方案，不改代码）
- **统一入口**: 使用 `src/utils/log_config.py` 作为唯一入口，封装 `EnvironmentAwareLogger` 的环境检测能力，集中创建 console/file 多路 sink，支持动态开关。
- **工具库**: 新增 `src/utils/logging_utils.py`，提供节流、采样、聚合、去重、一次性日志、上下文关联、敏感信息脱敏、RingBuffer 等通用能力。
- **策略配置**: 在 `state/logging_policy.json` 定义环境策略（级别、阈值、白名单、开关），配合环境变量热控（优先级：env > policy）。
- **分流与分域**: 控制台简洁（prod=WARNING），文件详尽（app/debug/perf/sql），慢查询/性能阈值单独落盘。
- **旁路兜底**: DEBUG RingBuffer（内存）+ “一键诊断包”导出，避免“问题被吞没”。

#### 环境与级别分流
- 环境变量：`APP_ENV=development|production|testing`
- 控制台级别：
  - dev: console=INFO/DEBUG（可调）
  - prod: console=WARNING
  - testing: console=INFO
- 文件级别：
  - `logs/salary_system.log`: INFO（prod）/DEBUG（dev, test），滚动/压缩/保留7-14天
  - `logs/salary_system_debug.log`: DEBUG 全量（仅 dev/开关）
  - `logs/perf.log`: 性能阈值与关键指标
  - `logs/sql.log`: SQL 明细（默认 prod 关闭，仅慢查询以 INFO 告警）

#### 策略与工具（logging_utils 关键能力）
- 节流/采样/去重/聚合：
  - `log_throttle(key, min_interval_s)`：同一 key 最短间隔
  - `log_sample(key, every_n)`：每 N 次打印一次
  - `dedup_log(key, window_ms)`：窗口内重复内容丢弃
  - `aggregate_log(key, window_ms)`：窗口结束输出一条汇总（示例：“空数据提示 X 次/2s”）
  - `log_once(key)`：进程生命周期仅一次
- 上下文与可追溯：
  - `with_log_context(correlation_id=session_id, table=..., path=...)`：统一输出会话/表/路径/操作ID
- 敏感信息治理：
  - `redact(text)`：工号/手机号/身份证等脱敏（prod 默认启用，dev 可关闭）
- RingBuffer 旁路：
  - 最近 N 条 DEBUG 日志驻留内存，可通过开关导出入诊断包

#### 统一入口与动态控制（log_config 增强）
- 统一 `setup_logger(module_name)`/`get_logger(module_name)`
- 读取 `state/logging_policy.json` + 环境变量：
  - `LOG_CONSOLE_LEVEL`、`LOG_FILE_LEVEL`
  - `LOG_SQL_ENABLED`、`LOG_PERF_ENABLED`、`LOG_REDACT_SENSITIVE`
  - `LOG_DEBUG_RINGBUFFER_ENABLED`
  - `LOG_THROTTLE_DEFAULT_MS`
  - `ALLOW_DEBUG_MODULES`（模块白名单数组）

#### 模块落地指引（关键路径）
- `src/gui/prototype/prototype_main_window.py`
  - `_schedule_ui_fix`：检测无问题→DEBUG；执行修复→INFO；失败→WARNING；加 `log_throttle('ui-fix', 1s)`
  - 空数据路径：WARNING→DEBUG；使用 `aggregate_log('empty-data', 2000ms)` 汇总
- `src/gui/prototype/widgets/enhanced_navigation_panel.py`
  - 重试链：首次/最终→INFO，中间重试→DEBUG；统一 `log_throttle('nav-retry', 3s)`
- `src/modules/data_storage/dynamic_table_manager.py`
  - 慢查询阈值（如 >300ms）：INFO 告警；SQL 明细→DEBUG（prod 默认关闭）
- 后台 `time.sleep` 替换后
  - 重试用 `token+attempt` 作为 key 做节流/采样，避免日志风暴

#### 安全与合规
- 生产强制脱敏（工号仅后4位等），控制台屏蔽细节；文件保留必要上下文。
- 任何策略可通过 `LOG_POLICY_OVERRIDE=off` 回退为“全 INFO 文件日志 + DEBUG RingBuffer”。

#### 质量保障
- 预提交检查（建议模式，不阻塞）：
  - 禁止在 `resizeEvent`/`paintEvent`/高频路径打印 INFO+，建议改 DEBUG（输出建议项）
- 回归与阈值测试（pytest）：
  - 场景：多次缩放/空闲后点击/导航重试，验证 10s 内控制台行数与文件大小不超阈值
  - 必达：关键“成功/失败”节点必有一次 INFO/WARN

#### 分阶段实施计划
- 阶段1（安全落地，不破坏现状）
  - 增强 `log_config.py`：环境、sinks、动态开关、策略读取
  - 新增 `logging_utils.py`：节流/采样/聚合/去重/once/上下文/脱敏/RingBuffer
  - 不改业务调用点，仅在极少数高频模块维持“等价行为”的迁移（便于验证）
- 阶段2（高频点规范化）
  - UI/导航重试/列宽保存/分页渲染路径：替换工具函数，统一 key 与策略
  - 启用慢 SQL/性能阈值落盘（perf.log）
- 阶段3（全局策略化）
  - 模块仅调用 `setup_logger`，级别/节流/采样由 `logging_policy.json` 控制（热更）
  - 不改业务逻辑即可调优日志行为（A/B 对比）
- 阶段4（保障与回顾）
  - 预提交检查的“建议模式”启用
  - 加入“诊断包导出”工具；收集 7-14 天后输出“噪音 TopN/价值 TopN”报告

#### 风险与对策
- 降噪影响排障 → 文件保留详尽信息 + RingBuffer 兜底 + 一键诊断包 + 策略热控。
- 重复实现导致不一致 → 统一在 `logging_utils.py` 实现公共能力，禁止模块自实现。
- 兼容性风险 → 阶段1 仅新增不替换；关键路径先做等价替换，逐步推广。

#### 验证指标（建议）
- UI 高频场景 10s 内控制台输出 < 50 行；文件增长 < 200KB（阈值可配）。
- 导航重试 INFO 3s 内 ≤ 1 条，成功/失败节点必有一次 INFO/WARN。
- 慢查询报警命中率与误报率周度复盘；脱敏检查随机抽样通过率 ≥ 99%。

#### 目录与关键文件（本项目路径）
- `src/utils/log_config.py`：唯一入口（环境检测/多 sink/动态开关/策略加载）
- `src/utils/logging_utils.py`：节流、采样、聚合、去重、一次性、上下文、脱敏、RingBuffer
- `state/logging_policy.json`：策略配置（级别、阈值、白名单、开关）
- `logs/`：日志目录（app/debug/perf/sql）
- 重点模块接入：
  - `src/gui/prototype/prototype_main_window.py`
  - `src/gui/prototype/widgets/enhanced_navigation_panel.py`
  - `src/modules/data_storage/dynamic_table_manager.py`

#### Mermaid：日志生成与治理管线（流程图）
```mermaid
flowchart TD
    A[模块调用 logger] --> B[logging_utils 包装\n节流/采样/聚合/去重/脱敏/上下文]
    B --> C{策略中心 log_config\n读取 ENV + policy}
    C -->|控制台| D[Console Sink(简洁)]
    C -->|文件: app.log| E[File Sink(滚动/压缩/保留)]
    C -->|文件: debug.log| F[Debug Sink(按需/开关)]
    C -->|文件: perf.log| G[Perf Sink]
    C -->|文件: sql.log| H[SQL Sink]
    C -->|旁路| I[DEBUG RingBuffer(内存)]
```

#### Mermaid：高频事件日志控制（时序图）
```mermaid
sequenceDiagram
    participant UI as 高频事件(resize/activate)
    participant MW as PrototypeMainWindow
    participant LU as logging_utils
    participant LC as log_config

    UI->>MW: 触发逻辑
    MW->>LU: log_throttle(key=ui-fix, 1s)
    LU->>MW: 允许/拒绝打印
    MW->>LC: logger.info/debug(...)
    LC-->>MW: 分流到 console/file/perf/sql
    Note over MW,LC: 中间重试→DEBUG；成功/失败→INFO/WARN
```

#### 策略样例（建议）
```json
{
  "console_level": "WARNING",
  "file_level": "INFO",
  "enable_sql": false,
  "enable_perf": true,
  "redact_sensitive": true,
  "debug_ringbuffer": false,
  "default_throttle_ms": 1000,
  "allow_debug_modules": [
    "src.gui.prototype.widgets.enhanced_navigation_panel",
    "src.gui.prototype.prototype_main_window"
  ]
}
```

#### 环境变量示例（建议）
```bash
# Windows PowerShell 示例
$env:APP_ENV = "production"
$env:LOG_CONSOLE_LEVEL = "WARNING"
$env:LOG_FILE_LEVEL = "INFO"
$env:LOG_SQL_ENABLED = "0"
$env:LOG_PERF_ENABLED = "1"
$env:LOG_REDACT_SENSITIVE = "1"
$env:LOG_DEBUG_RINGBUFFER_ENABLED = "0"
```

#### 关键 API 约定（建议）
- `setup_logger(module_name: str)` → 统一获取 logger
- `log_throttle(key: str, min_interval_s: float)` → 节流
- `log_sample(key: str, every_n: int)` → 采样
- `aggregate_log(key: str, window_ms: int)` → 聚合输出
- `dedup_log(key: str, window_ms: int)` → 去重
- `log_once(key: str)` → 一次性
- `with_log_context(**kwargs)` → 统一上下文
- `redact(text: str)` → 敏感信息脱敏

—— 本文档适配当前项目现状，先“规范化与收敛”，再“分域细化与长期保障”；按阶段推进，随时可回滚。


