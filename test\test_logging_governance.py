import os
from pathlib import Path

from src.utils.logging_utils import get_debug_buffer, export_debug_ring_to_file
from src.utils.log_config import setup_logger, refresh_and_rebuild_logging


def test_ringbuffer_export(tmp_path):
    logger = setup_logger(__name__)
    # 写入一些DEBUG日志到ring（策略不开也不报错）
    try:
        logger.debug("test debug entry")
    except Exception:
        pass
    out = export_debug_ring_to_file()
    assert out == "" or Path(out).exists()


def test_policy_refresh_no_crash():
    # 调用热更新接口应不抛异常
    ok = refresh_and_rebuild_logging()
    assert isinstance(ok, bool)


def test_event_bus_smoke_publish_subscribe():
    # 事件总线冒烟测试：订阅->发布->无需断言具体日志，仅确保不抛异常
    from src.core.event_bus import get_event_bus, Event, SortRequestEvent
    bus = get_event_bus()
    results = {}

    def _handler(evt: Event):
        results['handled'] = evt.event_type

    handler_id = bus.subscribe('sort_request', _handler, async_handler=False)
    try:
        evt = SortRequestEvent(event_type='sort_request', table_name='dummy')
        ok = bus.publish(evt, wait_for_completion=True)
        assert ok is True
        assert results.get('handled') == 'sort_request'
    finally:
        bus.unsubscribe('sort_request', handler_id)


def test_udrm_throttle_smoke(monkeypatch):
    # 统一数据请求管理器：节流键位路径冒烟测试
    import pandas as pd
    from src.core.unified_data_request_manager import UnifiedDataRequestManager, DataRequest, RequestType
    from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
    from src.modules.system_config.config_manager import ConfigManager

    cfg = ConfigManager()
    # 使用真实 DynamicTableManager 可能访问DB，这里仅实例化后关键函数打桩
    dbm = DynamicTableManager(config_manager=cfg)
    udrm = UnifiedDataRequestManager(db_manager=dbm, config_manager=cfg)

    # 打桩：执行查询时返回小DataFrame，避免触库
    def _fake_query(request, sort_params):
        return pd.DataFrame({'a':[1,2]}), 2
    monkeypatch.setattr(udrm, '_execute_database_query', _fake_query)
    # 打桩：表存在校验恒为真，避免依赖真实数据库
    monkeypatch.setattr(udrm.db_manager, 'table_exists', lambda name: True)

    req = DataRequest(table_name='dummy_table', request_type=RequestType.INITIAL_LOAD, page=1, page_size=10)
    # 验证不抛异常
    resp = udrm.request_table_data(req)
    assert resp.success is True


def test_usm_throttle_smoke():
    # 统一状态管理器：状态更新节流键位冒烟测试
    from src.core.unified_state_manager import UnifiedStateManager, StateChangeType
    usm = UnifiedStateManager()
    usm.update_table_state('员工_2025_在职', {'current_page': 1, 'page_size': 50}, StateChangeType.PAGE_CHANGED)
    state = usm.get_table_state('员工_2025_在职')
    assert state.current_page == 1


def test_pytest_console_sink_suppressed(monkeypatch):
    # 在pytest环境下不应向stdout添加新的sink（函数内部会早退）
    monkeypatch.setenv("PYTEST_CURRENT_TEST", "1")
    from importlib import reload
    import src.utils.log_config as lc
    reload(lc)
    # 重新配置一次，不应抛异常
    logger = lc.setup_logger(__name__)
    logger.info("pytest sink suppression smoke")


def test_report_context_binding_smoke(tmp_path, monkeypatch):
    # 仅验证调用链不抛异常（任务上下文绑定在实现中完成）
    from src.modules.report_generation.report_manager import ReportManager
    rm = ReportManager(output_base_path=str(tmp_path))
    # 简单模板模拟：若模板引擎需要真实模板，此处调用可能失败，但不影响日志初始化
    try:
        rm.generate_report(template_key="dummy", data={}, output_filename="x.docx", async_mode=False)
    except Exception:
        # 模板不存在等原因导致的异常允许出现
        pass
