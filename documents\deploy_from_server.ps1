# 薪资变动系统部署脚本
# 功能：从服务器下载项目，清理不必要文件，部署到本地

param(
    [string]$ServerAlias = "cc",
    [string]$ServerIP = "*************",
    [string]$ServerUser = "ubuntu",
    [string]$ProjectPath = "/home/<USER>/salary_changes",
    [string]$LocalProjectName = "salary_changes"
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 生成时间戳
$timestamp = Get-Date -Format "yyyyMMddHH"
$zipFileName = "salary_changes_$timestamp.zip"

Write-Host "=== 薪资变动系统部署脚本 ===" -ForegroundColor Green
Write-Host "时间戳: $timestamp" -ForegroundColor Yellow

try {
    # 阶段1：远程清理和打包
    Write-Host "`n[阶段1] 远程服务器操作..." -ForegroundColor Cyan
    
    $remoteCommands = @(
        "cd $ProjectPath",
        "echo '清理Python缓存...'",
        "find . -name '__pycache__' -type d -exec rm -rf {} + 2>/dev/null || true",
        "find . -name '*.pyc' -delete 2>/dev/null || true",
        "echo '创建zip目录...'",
        "mkdir -p /home/<USER>/zip",
        "echo '创建压缩包...'",
        "zip -r /home/<USER>/zip/$zipFileName $ProjectPath -x '*.git*' 2>/dev/null || echo '压缩完成（可能有警告）'",
        "ls -lh /home/<USER>/zip/$zipFileName"
    )
    
    $remoteScript = $remoteCommands -join "; "
    Write-Host "执行远程命令..." -ForegroundColor Yellow
    ssh $ServerAlias $remoteScript
    
    if ($LASTEXITCODE -ne 0) {
        throw "远程命令执行失败"
    }
    
    # 阶段2：下载压缩包
    Write-Host "`n[阶段2] 下载压缩包..." -ForegroundColor Cyan
    Write-Host "下载文件: $zipFileName" -ForegroundColor Yellow
    
    scp "${ServerUser}@${ServerIP}:/home/<USER>/zip/$zipFileName" .
    
    if ($LASTEXITCODE -ne 0) {
        throw "文件下载失败"
    }
    
    # 验证下载的文件
    if (!(Test-Path $zipFileName)) {
        throw "下载的压缩包不存在"
    }
    
    $fileSize = (Get-Item $zipFileName).Length / 1MB
    Write-Host "下载成功，文件大小: $($fileSize.ToString('F2')) MB" -ForegroundColor Green
    
    # 阶段3：本地解压和处理
    Write-Host "`n[阶段3] 本地解压和处理..." -ForegroundColor Cyan
    
    # 检查7-zip是否安装
    $sevenZipPath = Get-Command "7z" -ErrorAction SilentlyContinue
    if (-not $sevenZipPath) {
        Write-Host "检查常见7-zip安装路径..." -ForegroundColor Yellow
        $possiblePaths = @(
            "${env:ProgramFiles}\7-Zip\7z.exe",
            "${env:ProgramFiles(x86)}\7-Zip\7z.exe"
        )
        
        foreach ($path in $possiblePaths) {
            if (Test-Path $path) {
                $sevenZipPath = $path
                break
            }
        }
        
        if (-not $sevenZipPath) {
            throw "未找到7-zip，请先安装7-zip"
        }
    } else {
        $sevenZipPath = $sevenZipPath.Source
    }
    
    Write-Host "使用7-zip: $sevenZipPath" -ForegroundColor Yellow
    
    # 创建临时解压目录
    $tempExtractDir = "temp_extract_$timestamp"
    if (Test-Path $tempExtractDir) {
        Remove-Item $tempExtractDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $tempExtractDir | Out-Null
    
    # 解压缩
    Write-Host "解压缩到临时目录..." -ForegroundColor Yellow
    & $sevenZipPath x $zipFileName -o"$tempExtractDir" -y
    
    if ($LASTEXITCODE -ne 0) {
        throw "解压缩失败"
    }
    
    # 查找解压后的项目目录
    $extractedProjectPath = Join-Path $tempExtractDir "home/ubuntu/salary_changes"
    if (!(Test-Path $extractedProjectPath)) {
        throw "解压后未找到项目目录: $extractedProjectPath"
    }
    
    # 阶段4：清理不必要的目录
    Write-Host "`n[阶段4] 清理不必要的目录..." -ForegroundColor Cyan
    
    $dirsToDelete = @("logs", "state", "data/db")
    foreach ($dir in $dirsToDelete) {
        $fullPath = Join-Path $extractedProjectPath $dir
        if (Test-Path $fullPath) {
            Write-Host "删除目录: $dir" -ForegroundColor Yellow
            Remove-Item $fullPath -Recurse -Force
        } else {
            Write-Host "目录不存在: $dir" -ForegroundColor Gray
        }
    }
    
    # 阶段5：部署到本地
    Write-Host "`n[阶段5] 部署到本地..." -ForegroundColor Cyan
    
    # 备份现有项目（如果存在）
    if (Test-Path $LocalProjectName) {
        $backupName = "${LocalProjectName}_backup_$timestamp"
        Write-Host "备份现有项目到: $backupName" -ForegroundColor Yellow
        Rename-Item $LocalProjectName $backupName
    }
    
    # 移动新项目到当前目录
    Write-Host "部署新项目..." -ForegroundColor Yellow
    Move-Item $extractedProjectPath $LocalProjectName
    
    # 清理临时文件
    Write-Host "清理临时文件..." -ForegroundColor Yellow
    Remove-Item $tempExtractDir -Recurse -Force
    Remove-Item $zipFileName -Force
    
    # 阶段6：启动管理员PowerShell
    Write-Host "`n[阶段6] 启动管理员PowerShell..." -ForegroundColor Cyan
    
    $projectFullPath = Resolve-Path $LocalProjectName
    Write-Host "项目路径: $projectFullPath" -ForegroundColor Green
    
    # 创建启动脚本
    $startScript = @"
Write-Host "=== 薪资变动系统管理员环境 ===" -ForegroundColor Green
Set-Location '$projectFullPath'
Write-Host "当前目录: `$(Get-Location)" -ForegroundColor Yellow
Write-Host "项目文件:" -ForegroundColor Yellow
Get-ChildItem | Format-Table Name, Length, LastWriteTime
Write-Host "`n准备就绪！可以开始测试..." -ForegroundColor Green
"@
    
    $startScriptPath = "start_admin_shell.ps1"
    $startScript | Out-File -FilePath $startScriptPath -Encoding UTF8
    
    Write-Host "启动管理员PowerShell..." -ForegroundColor Yellow
    Start-Process -FilePath "powershell" -ArgumentList "-ExecutionPolicy Bypass -File `"$startScriptPath`"" -Verb RunAs
    
    Write-Host "`n=== 部署完成 ===" -ForegroundColor Green
    Write-Host "项目已部署到: $projectFullPath" -ForegroundColor Green
    Write-Host "管理员PowerShell已启动" -ForegroundColor Green
    
} catch {
    Write-Host "`n=== 部署失败 ===" -ForegroundColor Red
    Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查网络连接和SSH配置" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")