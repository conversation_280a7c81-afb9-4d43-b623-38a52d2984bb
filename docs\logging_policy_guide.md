# 日志治理策略与使用指南

本文档说明本系统的日志治理策略与使用方式，包括策略文件、环境变量、慢 SQL 记录、RingBuffer 调试与热更新。

## 策略文件

- 位置：`state/logging_policy.json`
- 作用：集中定义控制台/文件日志级别、是否启用性能日志、SQL 详日志与慢 SQL 阈值、是否启用调试 RingBuffer 等。
- 热更新：设置环境变量 `LOG_POLICY_WATCH=1` 后，系统会监听策略文件变化并自动重建日志 sinks（菜单 Tools 中也有“刷新策略并重建”入口）。

## 环境变量（覆盖策略文件同名配置）

- `LOG_CONSOLE_LEVEL`：控制台日志级别（默认 WARNING）
- `LOG_FILE_LEVEL`：文件日志级别（默认 INFO）
- `LOG_PERF_ENABLED`：是否启用性能日志（0/1）
- `LOG_SQL_DETAIL`：是否启用 SQL 详日志（0/1，默认 0）
- `LOG_SLOW_SQL_MS`：慢 SQL 阈值毫秒数（默认 300）
- `LOG_RING_ENABLED`：是否启用调试 RingBuffer（0/1）
- `LOG_POLICY_WATCH`：是否监听策略文件并热更新（0/1）

说明：若环境变量存在，将优先生效；否则由策略文件决定。

## 慢 SQL 与详日志

- 慢 SQL：当单次 SQL 执行耗时 ≥ `LOG_SLOW_SQL_MS` 时，统一以 INFO 级别输出 `[PERF][SLOW_SQL]` 简要记录（包括耗时、影响行数/返回行数）。
- 详日志：当 `LOG_SQL_DETAIL=1` 时，输出 SQL 文本与参数（参数将进行脱敏），并进行 2s 节流，避免日志风暴。

涉及模块：
- 数据访问层：`src/modules/data_storage/database_manager.py`（统一策略落地）
- 业务分页/排序层：`src/modules/data_storage/dynamic_table_manager.py`（与环境变量一致）

## 敏感信息脱敏

- 使用 `src/utils/logging_utils.py` 的 `redact(text)` 对工号/身份证等连续数字进行掩码处理（保留前6后2）。
- 已在 SQL 参数与关键业务日志样本处接入，持续扩面中。

## 调试 RingBuffer

- Sink：`create_ring_sink()`，在日志初始化时注册，用于收集最近 N 条 DEBUG 日志于内存，不落地到文件。
- 导出：`export_debug_ring_to_file()` 将当前缓冲导出到 `temp/debug_ring_<ts>.log`，便于现场问题回溯。
- UI：主菜单 Tools 提供“打开调试日志查看器/导出”入口。

## 开发/测试建议

- 开发期可设置：
  - `LOG_CONSOLE_LEVEL=DEBUG`
  - `LOG_FILE_LEVEL=DEBUG`
  - `LOG_SQL_DETAIL=1`
  - `LOG_SLOW_SQL_MS=50`
  - `LOG_RING_ENABLED=1`
  - `LOG_POLICY_WATCH=1`
- 生产期建议：
  - 控制台 WARNING、文件 INFO，详日志关闭（按需临时开启），慢 SQL 阈值 300~800ms。

## 常见操作

- 开启 SQL 详日志（当前进程）

```
# Windows PowerShell 示例
$env:LOG_SQL_DETAIL = "1"
```

- 临时降低慢 SQL 阈值到 50ms（定位性能问题）

```
$env:LOG_SLOW_SQL_MS = "50"
```

- 启用策略热更新并修改策略文件验证

```
$env:LOG_POLICY_WATCH = "1"
# 修改 state/logging_policy.json 后应看到状态栏提示并重建成功
```


